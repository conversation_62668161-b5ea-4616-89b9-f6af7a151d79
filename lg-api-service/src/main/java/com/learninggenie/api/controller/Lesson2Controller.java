package com.learninggenie.api.controller;

import com.alibaba.fastjson.JSONObject;
import com.learninggenie.api.constant.HeaderConstants;
import com.learninggenie.api.model.*;
import com.learninggenie.api.model.captcha.SendStatusResponse;
import com.learninggenie.api.model.curriculum.CurriculumListResponse;
import com.learninggenie.api.model.curriculum.GenerateLessonDetailRequest;
import com.learninggenie.api.model.curriculum.LessonModel;
import com.learninggenie.api.model.curriculum.lesson.CheckLessonContainBookRequest;
import com.learninggenie.api.model.curriculum.lesson.GenerateCornerCenterRequest;
import com.learninggenie.api.model.googleauth.CheckGoogleAuthRequest;
import com.learninggenie.api.model.lesson2.*;
import com.learninggenie.api.model.lesson2.LessonSlidesModel;
import com.learninggenie.api.model.unitplanner.CreateBatchGenerateTask;
import com.learninggenie.api.service.GoogleAuthService;
import com.learninggenie.api.service.lesson2.LessonDownloadService;
import com.learninggenie.api.service.lesson2.LessonService;
import com.learninggenie.api.service.lesson2.LessonVersionService;
import com.learninggenie.common.data.entity.PageList;
import com.learninggenie.common.data.entity.lesson2.*;
import com.learninggenie.common.data.model.lesson2.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.List;
import java.util.Map;

@Api(value = "Lesson2Controller", tags = "Lesson Plan")
@RestController
@RequestMapping("/api/v1/lessons2/lessons")
public class Lesson2Controller {

    @Autowired
    private LessonService lessonService;

    @Autowired
    private GoogleAuthService googleAuthService;

    @Autowired
    private LessonVersionService lessonVersionService;

    @Autowired
    private LessonDownloadService lessonDownloadService;

    @ApiOperation(value = "发布课程", nickname = "2645f5e1-cd20-4630-a248-37d05bc0c02c")
    @RequestMapping(value = "/publish", method = RequestMethod.POST)
    public PublishLessonResponse publishLesson(@RequestBody LessonPublishRequest request) {
        return lessonService.publishLesson(request);
    }

    @ApiOperation(value = "批量发布改编课程", nickname = "8f649aa3-287f-4288-8a2b-3582ed0f623b")
    @PostMapping(value = "/batchPublishAdaptedLesson")
    public SuccessResponse batchPublishAdaptedLesson(@RequestBody BatchPublishAdaptedLessonRequest request) {
        return lessonService.batchPublishAdaptedLesson(request);
    }

    @ApiOperation(value = "批量移除改编课程")
    @PostMapping(value = "/batchRemoveAdaptedLesson")
    public SuccessResponse batchRemoveAdaptedLesson(@RequestBody LessonBatchRemoveAdaptedRequest request) {
        return lessonService.batchRemoveAdaptedLesson(request);
    }

    @ApiOperation(value = "保存课程草稿", nickname = "e55f3a42-b9ca-4e45-8714-45daa9022099")
    @RequestMapping(value = "/saveDraft", method = RequestMethod.POST)
    public LessonEntity saveLessonDraft(@RequestBody LessonSaveDraftRequest request) {
        return lessonService.saveLessonDraft(request);
    }

    @ApiOperation(value = "查询课程详情", nickname = "d46712a1-eb16-4186-a51a-ce5378753737")
    @RequestMapping(value = "/detail", method = RequestMethod.GET)
    public LessonDetailResponse getLessonDetail(@ApiParam(name = "lessonId", value = "课程Id", required = true)
                                                @RequestParam("lessonId") String id,
                                                @ApiParam(name = "langCode", value = "语言码", required = true) @RequestParam(required = false) String langCode,
                                                @ApiParam(name = "isDetect", value = "是否需要识别语言类型", required = false) @RequestParam(required = false) Boolean isDetect) {
        return lessonService.getLessonDetailWithTranslation(id, langCode, isDetect);
    }

    @ApiOperation(value = "获取课程详情", nickname = "025ac6ad-161b-46d2-b035-4d7b2bf54383")
    @GetMapping(value = "/getCurriculumLessonDetail")
    public LessonModel getCurriculumLessonDetail(@ApiParam(name = "lessonId", value = "课程Id", required = true)
                                                 @RequestParam("lessonId") String id) {
        return lessonService.getCurriculumLessonDetail(id);
    }

    @ApiOperation(value = "获取 center 组课程详情", nickname = "d48b7825-57de-4375-9077-5d5e7de7e7d4")
    @GetMapping(value = "/getCenterLessonDetail")
    public LessonModel getCenterLessonDetail(@ApiParam(name = "lastWeeklyPlanId", value = "上一个周的周计划 ID", required = true)
                                             @RequestParam("lastWeeklyPlanId") String lastWeeklyPlanId,
                                             @ApiParam(name = "itemId", value = "周计划项 ID", required = true)
                                             @RequestParam("itemId") String itemId) {
        return lessonService.getCenterLessonDetail(lastWeeklyPlanId, itemId);
    }

    @ApiOperation(value = "查询课程最新草稿详情", nickname = "fe9740b4-07a8-421c-87b4-4cf754c544a9")
    @RequestMapping(value = "/lastDraftDetail", method = RequestMethod.GET)
    public LessonDetailResponse getLessonLastDraftDetail(@ApiParam(name = "lessonId", value = "课程Id", required = true)
                                                         @RequestParam("lessonId") String id) {
        return lessonService.getLessonLastDraftDetail(id);
    }

    @ApiOperation(value = "删除课程", nickname = "df83ad56-68fb-4c42-958c-1b1c6b3c5d5a")
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    public DeleteLessonResponse deleteLesson(@ApiParam(name = "lessonId", value = "课程Id", required = true)
                                             @RequestParam(value = "lessonId") String lessonId) {
        return lessonService.deleteLesson(lessonId);
    }

    @ApiOperation(value = "永久删除课程", nickname = "5ed80b1d-45a7-4aca-ae77-5ce5af8181bd")
    @RequestMapping(value = "/deletePermanently", method = RequestMethod.POST)
    public DeleteLessonPermanentlyResponse deleteLessonPermanently(@ApiParam(name = "lessonId", value = "课程ID", required = true) @RequestParam("lessonId") String lessonId) {
        return lessonService.deleteLessonPermanently(lessonId);
    }

    @ApiOperation(value = "查询课程推荐状态", nickname = "d46712a1-eb16-4186-a51a-ce5378753737")
    @RequestMapping(value = "/getLessonRecommendStatus", method = RequestMethod.GET)
    public LessonRecommendStatusResponse getLessonRecommendStatus(@ApiParam(name = "lessonId", value = "课程Id", required = true) @RequestParam("lessonId") String lessonId) {
        return lessonService.getLessonRecommendStatus(lessonId);
    }

    @ApiOperation(value = "推荐课程", nickname = "0f555cec-bea3-46aa-9f96-06c9ec40b2c8")
    @RequestMapping(value = "/promoteToAgency", method = RequestMethod.POST)
    public EmptyResponse promoteLesson(@ApiParam(name = "lessonId", value = "课程ID", required = true) @RequestParam("lessonId") String id) {
        lessonService.recommendToAgency(id);
        return new EmptyResponse();
    }

    @ApiOperation(value = "取消推荐课程", nickname = "3f807fd6-6544-47c8-93e4-b3aac79dbaf7")
    @RequestMapping(value = "/cancelPromoteToAgency", method = RequestMethod.POST)
    public EmptyResponse cancelPromoteToAgency(@ApiParam(name = "lessonId", value = "课程ID", required = true)
                                               @RequestParam("lessonId") String id) {
        lessonService.cancelRecommendToAgency(id);
        return new EmptyResponse();
    }

    @ApiOperation(value = "复制课程", nickname = "0237d35f-cf84-4736-95b6-2df70f4670c4")
    @RequestMapping(value = "/copyLesson", method = RequestMethod.POST)
    public LessonEntity copyLesson(@ApiParam(value = "课程ID", required = true) @RequestParam("lessonId") String id,
                                   @ApiParam(value = "是否保留原课程名，不添加 (copy) 后缀") @RequestParam(value = "useBeforeName",defaultValue = "false", required = false) Boolean useBeforeName) {
        return lessonService.copyLesson(id, useBeforeName);
    }

    @ApiOperation(value = "复制课程", nickname = "219353c1-3165-473d-9fa3-ba5c006770f3")
    @RequestMapping(value = "/copyAdaptLesson", method = RequestMethod.POST)
    public LessonModel copyAdaptLesson(@ApiParam(value = "课程 ID", required = true) @RequestParam("lessonId") String id,
                                       @ApiParam(value = "改编班级 ID", required = false) @RequestParam(value = "adaptGroupId", required = false) String adaptGroupId) {
        return lessonService.copyAdaptLesson(id, adaptGroupId);
    }

    @ApiOperation(value = "查询领域", nickname = "c22c42bf-b081-4d3b-8e63-699177b318e9")
    @RequestMapping(value = "/listDomains", method = RequestMethod.GET)
    public ListDomainsResponse getDomains() {
        return lessonService.listDomains();
    }

    @ApiOperation(value = "查询测评框架", nickname = "e47e34d9-64de-4edb-a178-9ec96961301d")
    @RequestMapping(value = "/getFrameworks", method = RequestMethod.GET)
    public GetFrameworkResponse getFrameworks() {
        return lessonService.getFrameworks();
    }

    @ApiOperation(value = "查询扩展的测评框架", nickname = "e58e34d9-64de-4edb-a178-9ec96961301d")
    @RequestMapping(value = "/getExtendFrameworks", method = RequestMethod.POST)
    public GetFrameworkResponse getExtendFrameworks(@RequestBody GetFrameworkRequest getFrameworkRequest) {
        return lessonService.getExtendFrameworks(getFrameworkRequest);
    }

    @ApiOperation(value = "查询测评点(包括领域)", nickname = "b1de412a-9b40-4283-9e15-00c71dddf99f")
    @RequestMapping(value = "/getMeasures", method = RequestMethod.GET)
    public GetMeasureResponse getMeasures(@ApiParam("测评框架 ID") @RequestParam("frameworkId") String frameworkId,
                                          @ApiParam("是否压缩测评点") @RequestParam(value = "compress", defaultValue = "true", required = false) Boolean compress) {
        return lessonService.getMeasures(frameworkId, compress);
    }

    @ApiOperation(value = "查询测评点(仅仅包括可映射的)", nickname = "b1de412c-9b40-4283-9e15-00c71dddf99f")
    @RequestMapping(value = "/getOnlyDomainMapFrameworkMeasures", method = RequestMethod.GET)
    public GetMeasureResponse getOnlyDomainMapFrameworkMeasures(@ApiParam("测评框架 ID") @RequestParam("frameworkId") String frameworkId,
                                          @ApiParam("是否压缩测评点") @RequestParam(value = "compress", defaultValue = "true", required = false) Boolean compress) {
        return lessonService.getOnlyDomainMapFrameworkMeasures(frameworkId, compress);
    }

    @ApiOperation(value = "查询测评点(包括领域，只有顶层和底层)", nickname = "b1de412a-9b40-4183-9e15-00c71dddf91f")
    @RequestMapping(value = "/getMeasuresContainTopAndBottom", method = RequestMethod.GET)
    public GetMeasureResponse getMeasuresContainTopAndBottom(@RequestParam("frameworkId") String frameworkId,
                                                             @RequestParam("unitId") String unitId) {
        return lessonService.getMeasuresContainTopAndBottom(frameworkId, unitId);
    }

    @ApiOperation(value = "查询公共课程", nickname = "c51562a4-341e-458a-bffa-d404c904d174")
    @RequestMapping(value = "/getPublicLessons", method = RequestMethod.GET)
    public PageResponse<LessonPublicPageItem> getPublicLessons(
            @ApiParam(name = "pageSize", value = "页大小") @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
            @ApiParam(name = "pageNum", value = "页码") @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
            @ApiParam(name = "ages", value = "年龄段") @RequestParam(value = "ages", required = false) String ages,
            @ApiParam(name = "domainIds", value = "领域 ID") @RequestParam(value = "domainIds", required = false) String domainIds,
            @ApiParam(name = "themeIds", value = "课程主题") @RequestParam(value = "themeIds", required = false) String themeIds,
            @ApiParam(name = "keyword", value = "搜索关键词，支持课程名称搜索") @RequestParam(value = "keyword", required = false) String keyword,
            @ApiParam(name = "orderKey", value = "排序，UPDATE_TIME | LIKES | FAVORITE") @RequestParam(value = "orderKey", required = false) String orderKey,
            @ApiParam(name = "otherMeasureIds", value = "other测评点 ID") @RequestParam(value = "otherMeasureIds", required = false) String otherMeasureIds,
            @ApiParam(name = "mappedDrdpFrameworkIds", value = "DRDP 框架 ID") @RequestParam(value = "mappedDrdpFrameworkIds", required = false) String mappedDrdpFrameworkIds,
            @ApiParam(name = "mappedFrameworkId", value = "当前勾选的框架 ID") @RequestParam(value = "mappedFrameworkId", required = false) String mappedFrameworkId) {
        return lessonService.getPublicLessons(pageSize, pageNum, ages, domainIds, themeIds, keyword, orderKey, otherMeasureIds, mappedDrdpFrameworkIds, mappedFrameworkId);
    }

    @ApiOperation(value = "分页查询所有的机构课程", nickname = "e7468d99-b824-4638-8593-822657a46952")
    @RequestMapping(value = "/getAgencyLessons", method = RequestMethod.GET)
    public PageResponse<LessonAgencyPageItem> getAgencyLessons(
            @ApiParam(name = "pageSize", value = "页大小") @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
            @ApiParam(name = "pageNum", value = "页码") @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
            @ApiParam(name = "ages", value = "年龄段") @RequestParam(value = "ages", required = false) String ages,
            @ApiParam(name = "domainIds", value = "领域 ID") @RequestParam(value = "domainIds", required = false) String domainIds,
            @ApiParam(name = "themeIds", value = "课程主题") @RequestParam(value = "themeIds", required = false) String themeIds,
            @ApiParam(name = "keyword", value = "搜索关键词，支持课程名称搜索") @RequestParam(value = "keyword", required = false) String keyword,
            @ApiParam(name = "orderKey", value = "排序，UPDATE_TIME | LIKES | FAVORITE") @RequestParam(value = "orderKey", required = false) String orderKey,
            @ApiParam(name = "otherMeasureIds", value = "other测评点 ID") @RequestParam(value = "otherMeasureIds", required = false) String otherMeasureIds,
            @ApiParam(name = "mappedDrdpFrameworkIds", value = "DRDP 框架 ID") @RequestParam(value = "mappedDrdpFrameworkIds", required = false) String mappedDrdpFrameworkIds,
            @ApiParam(name = "mappedFrameworkId", value = "当前勾选的框架 ID") @RequestParam(value = "mappedFrameworkId", required = false) String mappedFrameworkId) {
        return lessonService.getAgencyLessons(pageSize, pageNum, ages, domainIds, themeIds, keyword, orderKey, otherMeasureIds, mappedDrdpFrameworkIds, mappedFrameworkId);
    }

    @ApiOperation(value = "分页查询所有的我的课程", nickname = "b0ac22b3-59fe-47ad-bc88-14164d1ca7ba")
    @RequestMapping(value = "/getMyLessons", method = RequestMethod.GET)
    public PageResponse<LessonMyLessonsPageItem> getMyLessons(
            @ApiParam(name = "pageSize", value = "页大小") @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
            @ApiParam(name = "pageNum", value = "页码") @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
            @ApiParam(name = "ages", value = "年龄段") @RequestParam(value = "ages", required = false) String ages,
            @ApiParam(name = "domainIds", value = "领域 ID") @RequestParam(value = "domainIds", required = false) String domainIds,
            @ApiParam(name = "themeIds", value = "课程主题") @RequestParam(value = "themeIds", required = false) String themeIds,
            @ApiParam(name = "keyword", value = "搜索关键词，支持课程名称搜索") @RequestParam(value = "keyword", required = false) String keyword,
            @ApiParam(name = "orderKey", value = "排序，UPDATE_TIME | LIKES | FAVORITE") @RequestParam(value = "orderKey", required = false) String orderKey,
            @ApiParam(name = "status", value = "课程状态 DRAFT | PUBLISHED", required = true) @RequestParam(value = "status") String status,
            @ApiParam(name = "otherMeasureIds", value = "other 测评点 ID") @RequestParam(value = "otherMeasureIds", required = false) String otherMeasureIds,
            @ApiParam(name = "mappedDrdpFrameworkIds", value = "DRDP 框架 ID") @RequestParam(value = "mappedDrdpFrameworkIds", required = false) String mappedDrdpFrameworkIds,
            @ApiParam(name = "all", value = "是否查询所有课程") @RequestParam(value = "all", required = false) boolean all,
            @ApiParam(name = "publicOpen", value = "公共课程开关是否打开") @RequestParam(value = "publicOpen", required = false) boolean publicOpen,
            @ApiParam(value = "课程类型, true 特殊课程 | false 普通课程") @RequestParam(value = "isAdaptedLesson", required = false) Boolean isAdaptedLesson,
            @ApiParam(name = "mappedFrameworkId", value = "当前勾选的框架 ID") @RequestParam(value = "mappedFrameworkId", required = false) String mappedFrameworkId,
            @ApiParam(name = "adaptGroupId", value = "改编班级 ID") @RequestParam(value = "adaptGroupId", required = false) String adaptGroupId) {
        return lessonService.getMyLessons(pageSize, pageNum, ages, domainIds, themeIds, keyword, orderKey, status, otherMeasureIds, all, publicOpen, mappedDrdpFrameworkIds, isAdaptedLesson, mappedFrameworkId, adaptGroupId);
    }

    @ApiOperation(value = "分页查询所有LessonPlan课程", nickname = "b0ac2233-59fe-47ad-bc88-14164d1ca7ba")
    @GetMapping(value = "/getLessonPlanList")
    public PageResponse<LessonMyLessonsPageItem> getLessonPlanList(
            @ApiParam(name = "pageSize", value = "页大小") @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
            @ApiParam(name = "pageNum", value = "页码") @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum) {
        return lessonService.getLessonPlanList(pageSize, pageNum);
    }

    @ApiOperation(value = "分页查询所有的我收藏的课程", nickname = "ba513ac7-2a28-4276-90d5-c2690c289155")
    @RequestMapping(value = "/getMyFavoriteLessons", method = RequestMethod.GET)
    public PageResponse<LessonMyFavoritePageItem> getMyFavoriteLessons(
            @ApiParam(name = "pageSize", value = "页大小") @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
            @ApiParam(name = "pageNum", value = "页码") @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
            @ApiParam(name = "ages", value = "年龄段") @RequestParam(value = "ages", required = false) String ages,
            @ApiParam(name = "domainIds", value = "领域 ID") @RequestParam(value = "domainIds", required = false) String domainIds,
            @ApiParam(name = "themeIds", value = "课程主题") @RequestParam(value = "themeIds", required = false) String themeIds,
            @ApiParam(name = "keyword", value = "搜索关键词，支持课程名称搜索") @RequestParam(value = "keyword", required = false) String keyword,
            @ApiParam(name = "orderKey", value = "排序，UPDATE_TIME | LIKES | FAVORITE") @RequestParam(value = "orderKey", required = false) String orderKey,
            @ApiParam(name = "otherMeasureIds", value = "other测评点 ID") @RequestParam(value = "otherMeasureIds", required = false) String otherMeasureIds,
            @ApiParam(name = "mappedDrdpFrameworkIds", value = "DRDP 框架 ID") @RequestParam(value = "mappedDrdpFrameworkIds", required = false) String mappedDrdpFrameworkIds,
            @ApiParam(value = "课程类型, true 特殊课程 | false 普通课程") @RequestParam(value = "isAdaptedLesson", required = false) Boolean isAdaptedLesson,
            @ApiParam(name = "mappedFrameworkId", value = "当前勾选的框架 ID") @RequestParam(value = "mappedFrameworkId", required = false) String mappedFrameworkId,
            @ApiParam(name = "adaptGroupId", value = "改编班级 ID") @RequestParam(value = "adaptGroupId", required = false) String adaptGroupId) {
        return lessonService.getMyFavoriteLessons(pageSize, pageNum, ages, domainIds, themeIds, keyword, orderKey, otherMeasureIds, mappedDrdpFrameworkIds, isAdaptedLesson, mappedFrameworkId, adaptGroupId);
    }

    @ApiOperation(value = "查询回收站课程", nickname = "d7b68156-0682-456a-9537-27705404b64f")
    @RequestMapping(value = "/getRecoverableLesson", method = RequestMethod.GET)
    public PageResponse<DeletedLessonItem> getRecoverableLesson(
            @ApiParam(name = "pageSize", value = "页大小") @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
            @ApiParam(name = "pageNum", value = "页码") @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum) {
        return lessonService.getDeletedLessons(pageSize, pageNum);
    }

    @ApiOperation(value = "恢复课程", nickname = "8de41006-3c70-48ef-a6d3-1f8f8081535e")
    @RequestMapping(value = "/recover", method = RequestMethod.POST)
    public RecoverLessonResponse recoverLesson(@ApiParam(name = "lessonId", value = "课程Id", required = true)
                                               @RequestParam(value = "lessonId") String id) {
        return lessonService.restoreLesson(id);
    }

    @ApiOperation(value = "恢复课程（包括删除的）", nickname = "1b1695db-a196-43b0-af5d-cdbe579f5f7b")
    @RequestMapping(value = "/recoverDeletedLesson", method = RequestMethod.POST)
    public RecoverLessonResponse recoverDeletedLesson(@ApiParam(name = "lessonId", value = "课程Id", required = true)
                                                      @RequestParam(value = "lessonId") String id) {
        return lessonService.recoverDeletedLesson(id);
    }

    @ApiOperation(value = "生成课程 PDF", nickname = "c991f624-43e2-44b9-9a5b-b89bb22d33c9")
    @RequestMapping(value = "/generateOwnLessonPDF", method = RequestMethod.POST)
    public DownFileResponse generateLessonPDF(
            @ApiParam(name = "lessonId", value = "课程Id", required = true) @RequestParam(value = "lessonId") String id,
            @ApiParam(name = "mappedFrameworkId", value = "映射框架 ID") @RequestParam(value = "mappedFrameworkId", required = false) String mappedFrameworkId,
            @ApiParam(name = "showDescription", value = "是否显示测评点详情") @RequestParam(value = "showDescription", required = false, defaultValue = "0") Boolean showDescription,
            @ApiParam(name = "onlyShowCore", value = "是否显示核心测评点") @RequestParam(value = "onlyShowCore", required = false, defaultValue = "0") Boolean onlyShowCore,
            @ApiParam(name = "showClrSource", value = "是否显示资源") @RequestParam(value = "showClrSource", required = false, defaultValue = "0") boolean showClrSource,
            @ApiParam(value = "下载的文档内容的语言类型的语言码", required = false) @RequestParam(required = false) String langCode,
            @ApiParam(name = "showImpStepSourceMap", value = "是否显示实施步骤资源") @RequestParam(value = "showImpStepSourceMap", required = false, defaultValue = "{}") String showImpStepSourceMap,
            @ApiParam(name = "showMappedTypicalBehaviors", value = "典型行为显示模式: false(DRDP 2015), true(映射框架), DRDP-2025(DRDP 2025)", required = false) String showMappedTypicalBehaviors) {
        return lessonService.generateLessonPDF(id, mappedFrameworkId, showDescription, onlyShowCore, showClrSource, langCode, JSONObject.parseObject(showImpStepSourceMap, Map.class), showMappedTypicalBehaviors);
    }

    @ApiOperation(value = "收藏课程", nickname = "1e7870ab-cbc1-4a1d-b2c0-ac334133c757")
    @RequestMapping(value = "/favorite", method = RequestMethod.POST)
    public EmptyResponse addFavoriteForLesson(@ApiParam(name = "lessonId", value = "课程Id", required = true)
                                              @RequestParam(value = "lessonId") String lessonId) {
        lessonService.favorite(lessonId);
        return new EmptyResponse();
    }

    @ApiOperation(value = "取消课程收藏", nickname = "2d1db276-2896-487c-af1d-ba5411f732aa")
    @RequestMapping(value = "/cancelFavorite", method = RequestMethod.POST)
    public EmptyResponse cancelFavorite(@ApiParam(name = "lessonId", value = "课程Id", required = true)
                                        @RequestParam(value = "lessonId") String lessonId) {
        lessonService.cancelFavorite(lessonId);
        return new EmptyResponse();
    }

    @ApiOperation(value = "点赞课程", nickname = "73666d96-b14c-43dd-afe3-0916c1a13498")
    @RequestMapping(value = "/like", method = RequestMethod.POST)
    public EmptyResponse like(@ApiParam(name = "lessonId", value = "课程Id", required = true)
                              @RequestParam(value = "lessonId") String lessonId) {
        lessonService.like(lessonId);
        return new EmptyResponse();
    }

    @ApiOperation(value = "取消课程点赞", nickname = "4b16a447-f02e-47aa-a031-f94c986ca1f9")
    @RequestMapping(value = "/cancelLike", method = RequestMethod.POST)
    public EmptyResponse cancelLike(@ApiParam(name = "lessonId", value = "课程Id", required = true)
                                    @RequestParam(value = "lessonId") String lessonId) {
        lessonService.cancelLike(lessonId);
        return new EmptyResponse();
    }

    @ApiOperation(value = "查询所有的Theme", nickname = "5354b1f4-38f1-4e20-81f5-4201ed6d51bb")
    @RequestMapping(value = "/getThemes", method = RequestMethod.GET)
    public LessonGetThemesResponse getThemes(@RequestParam(value = "includeCustom", required = false) boolean includeCustom) {
        return lessonService.getThemes(false, includeCustom);
    }

    @ApiOperation(value = "查询所有的 Age Group", nickname = "6f499ed6-ab3e-4bb7-a215-42be39e65528")
    @RequestMapping(value = "/getAgeGroups", method = RequestMethod.GET)
    public LessonGetAgeGroupsResponse getAgeGroups(@ApiParam(name = "region", value = "用户地区")
                                                   @RequestParam(value = "region", required = false) String region) {
        return lessonService.getAgeGroups(region);
    }

    @ApiOperation(value = "查询复制本课程的的课程", nickname = "112afbd2-e805-4374-a72a-cd735d3a9f6b")
    @RequestMapping(value = "/getCopyLessons", method = RequestMethod.GET)
    public GetLinkedLessonResponse getCopyLessons(@ApiParam(name = "lessonId", value = "课程Id", required = true)
                                                  @RequestParam(value = "lessonId") String lessonId) {
        return lessonService.getCopyLessons(lessonId);
    }

    @ApiOperation(value = "查询评论列表", nickname = "a57bb7c1-2b65-4072-8e96-895b08fbe963")
    @RequestMapping(value = "/getComments", method = RequestMethod.GET)
    public CommentPageResponse getComments(@ApiParam(value = "页大小") @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                           @ApiParam(value = "页码") @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                           @ApiParam(value = "课程Id", required = true) @RequestParam(value = "lessonId") String lessonId) {
        return lessonService.getComments(lessonId, pageSize, pageNum);
    }

    @ApiOperation(value = "查询子评论列表", nickname = "acb9f355-2173-4b2a-a53f-17828b9b9f6d")
    @RequestMapping(value = "/getSubComments", method = RequestMethod.GET)
    public PageList<CommentModel> getSubComments(@ApiParam(value = "页大小") @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                                 @ApiParam(value = "页码") @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                 @ApiParam(value = "root评论Id", required = true) @RequestParam(value = "rootCommentId") String rootCommentId) {
        return lessonService.getSubComments(rootCommentId, pageSize, pageNum);
    }

    @ApiOperation(value = "评论课程", nickname = "cf0a934b-414e-4e7b-a479-7594e433d0cb")
    @RequestMapping(value = "/commentLesson", method = RequestMethod.POST)
    public CommentResponse commentLesson(@RequestBody CommentLessonRequest request) {
        return lessonService.commentLesson(request);
    }

    @ApiOperation(value = "评论删除", nickname = "2732c345-1b65-4b96-90f0-a9d14103a367")
    @RequestMapping(value = "/deleteComment", method = RequestMethod.POST)
    public DeleteCommentResponse deleteComment(@RequestParam("commentId") String id) {
        return lessonService.deleteComment(id);
    }

    @ApiOperation(value = "增加浏览量", nickname = "7090ba6b-ad62-427f-b2ae-2d21f6303e82")
    @RequestMapping(value = "/increaseLessonViewCount", method = RequestMethod.POST)
    public EmptyResponse increaseLessonViewCount(@RequestParam("lessonId") String lessonId) {
        lessonService.increaseLessonViewCount(lessonId);
        return new EmptyResponse();
    }

    @ApiOperation(value = "查询课程更新版本", nickname = "84cfab2b-9e04-47c4-ad89-af4a46ac7725")
    @RequestMapping(value = "/updateVersion", method = RequestMethod.GET)
    public LessonUpdateHistoryResponse updateVersion(@ApiParam(name = "lessonId", value = "课程Id", required = true)
                                                     @RequestParam(value = "lessonId") String lessonId) {
        return lessonService.getUpdateVersion(lessonId);
    }

    @ApiOperation(value = "查询学校老师，不包括老师助手", nickname = "3deb14d2-65d4-42d3-9d0a-6e160761595e")
    @RequestMapping(value = "/admin/getCenterTeachers", method = RequestMethod.GET)
    public GetCenterTeachersResponse getCenterTeachers() {
        return lessonService.getCenterTeachers();
    }

    @ApiOperation(value = "查询机构下学校的老师和管理员", nickname = "c239a7a2-0bca-4f25-bc35-a91233aada71")
    @RequestMapping(value = "/admin/getAgencyStaffs", method = RequestMethod.GET)
    public GetAgencyStaffsResponse getAgencyStaffs() {
        return lessonService.getAgencyStaffs();
    }

    @ApiOperation(value = "查询老师是否点过对话框提示", nickname = "0ea5a5bf-bf7f-47e3-9007-94675e9d1c7e")
    @RequestMapping(value = "/getTipStatus", method = RequestMethod.GET)
    public LessonDialogResponse getTipStatus() {
        return lessonService.getTeacherDialog();
    }

    @ApiOperation(value = "添加提示记录", nickname = "af423508-ae22-4528-849e-5a98fe6c2f1f")
    @RequestMapping(value = "/addTipRecords", method = RequestMethod.POST)
    public EmptyResponse addTipRecords() {
        lessonService.addTipRecords();
        return new EmptyResponse();
    }

    @ApiOperation(value = "分页查询管理端教师课程", nickname = "f33fb433-68a7-4793-960b-0d5a123f55d0")
    @RequestMapping(value = "/admin/getTeacherLessons", method = RequestMethod.GET)
    public PageResponse<ManageTeacherLessonPageItem> getManageTeacherLessons(@ApiParam(value = "页大小") @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                                                             @ApiParam(value = "页码") @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                                             @ApiParam(value = "教师ID", required = true) @RequestParam("userId") String userId,
                                                                             @ApiParam(value = "搜索关键字") @RequestParam(value = "keyword", required = false) String keyword,
                                                                             @ApiParam(value = "排序，UPDATE_TIME | LIKES | FAVORITE") @RequestParam(value = "orderKey", defaultValue = "UPDATE_TIME") String orderKey,
                                                                             @ApiParam(value = "课程类型, true 特殊课程 | false 普通课程") @RequestParam(value = "isAdaptedLesson", required = false) Boolean isAdaptedLesson) {
        return lessonService.getManageTeacherLessons(pageSize, pageNum, userId, keyword, orderKey, isAdaptedLesson);
    }

    @ApiOperation(value = "外部链接资源保存")
    @RequestMapping(value = "/externalMediaUrl", method = RequestMethod.POST)
    public ExternalMediaResponse saveExternalMedia(@RequestBody LessonsExternalMediaResponse lessonsMediaExternalEntity) {
        ExternalMediaResponse response = lessonService.saveExternalMedia(lessonsMediaExternalEntity);
        return response;
    }


    @ApiOperation(value = "iPad端文件下载地址发送邮件", nickname = "309da56e-9db9-49eb-ac55-26f3f03c1233")
    @ResponseBody
    @RequestMapping(value = "/sendFileDownloadEmail", method = RequestMethod.POST)
    public SendStatusResponse sendFileDownloadEmail(@RequestBody FileDownloadEmailRequest request) {
        return lessonService.sendFileDownloadEmail(request);
    }

    @ApiOperation(value = "添加课程主题", nickname = "e014a021-55b9-4171-8204-e897f9634fd7")
    @RequestMapping(value = "/addLessonTheme", method = RequestMethod.POST)
    public SuccessResponse addLessonTheme(@RequestBody AddLessonThemeRequest request) {
        return lessonService.addLessonTheme(request);
    }

    @ApiOperation(value = "删除课程主题", nickname = "c690ec75-9d1b-44af-9855-62568553f546")
    @RequestMapping(value = "/deleteLessonTheme", method = RequestMethod.POST)
    public SuccessResponse deleteLessonTheme(@RequestParam String id) {
        return lessonService.deleteLessonTheme(id);
    }

    @ApiOperation(value = "批量生成区角周活动项概览")
    @PostMapping(value = "/generateCornerCenterOverviewStream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter generateCornerCenterOverviewStream(@RequestBody GenerateCornerCenterRequest request) {
        return lessonService.generateCornerCenterOverviewStream(request);
    }

    @ApiOperation(value = "单个生成区角周活动项概览")
    @PostMapping(value = "/generateSingleCornerCenterOverviewStream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter generateSingleCornerCenterOverviewStream(@RequestBody GenerateCornerCenterRequest request) {
        return lessonService.generateSingleCornerCenterOverviewStream(request);
    }

    @ApiOperation(value = "生成 Centers 组课程概览")
    @GetMapping(value = "/generateCenterOverviewStream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter generateCenterOverviewStream(@RequestParam String unitId, @RequestParam String planId) {
        return lessonService.generateCenterOverviewStream(unitId, planId);
    }

    @ApiOperation(value = "生成单个 Centers 组课程概览", nickname = "b72ae685-90fb-422f-acc1-7fcac86403e3")
    @GetMapping(value = "/generateSingleCenterOverviewStream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter generateSingleCenterOverviewStream(@RequestParam String unitId, @RequestParam String planId, @RequestParam String centerId) {
        return lessonService.generateSingleCenterOverviewStream(unitId, planId, centerId);
    }

    @ApiOperation(value = "生成 centers 组课程详情", nickname = "43193b75-67d3-4cb7-afd2-4629b199fd92")
    @PostMapping(value = "/generateCenterLessonStream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter generateCenterLessonStream(@RequestBody GenerateLessonDetailRequest request) {
        return lessonService.generateCenterLessonStream(request);
    }

    @ApiOperation(value = "生成差异化教学分组内容（Group 仅仅分组）", nickname = "3b1bfd97-def3-4396-b8d9-ec7e75bd9090")
    @PostMapping(value = "/generateUniversalDesignForLearningGroupStream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter generateUniversalDesignForLearningGroupV1(@RequestBody GenerateLessonUniversalDesignForLearningRequest generateUniversalDesignForLearningRequest) {
        return lessonService.generateUniversalDesignForLearningGroupV1(generateUniversalDesignForLearningRequest);
    }

    @ApiOperation(value = "生成差异化教学分组内容", nickname = "3b1bfd97-def3-4396-b8d9-ec7e75bd9090")
    @PostMapping(value = "/generateUniversalDesignForLearningStream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter generateUniversalDesignForLearningGroupV2(@RequestBody GenerateLessonUniversalDesignForLearningRequest generateUniversalDesignForLearningRequest) {
        return lessonService.generateUniversalDesignForLearningGroupV2(generateUniversalDesignForLearningRequest);
    }

    @ApiOperation(value = "创建批量生成课程任务", nickname = "3320c540-456a-438a-86f0-bdf8ae9f614e")
    @PostMapping(value = "/createGenerateLessonTasks")
    public SuccessResponse createGenerateLessonTasks(@RequestBody CreateGenerateLessonTasksRequest request) {
        return lessonService.createGenerateLessonTasks(request);
    }

    @ApiOperation(value = "查询批量生成课程任务", nickname = "e99c9d37-e941-4a74-b2c1-a21f118f6231")
    @GetMapping(value = "/getBatchGenerateLessonTasks")
    public GetBatchGenerateLessonTaskResponse getBatchGenerateLessonTasks(@RequestParam String batchId) {
        return lessonService.getBatchGenerateLessonTasks(batchId);
    }

    @ApiOperation(value = "批量执行生成课程任务", nickname = "9eff368e-bf43-4820-b6d3-57c0d14fdb4c")
    @PostMapping(value = "/batchGenerateLesson")
    public SuccessResponse batchGenerateLesson(String taskIds) {
        return lessonService.batchGenerateLesson(taskIds);
    }

    @ApiOperation(value = "停止生成课程任务", nickname = "e3c93139-61e1-4f2f-a8a9-0515bdba937e")
    @PostMapping(value = "/stopGenerateLessonTasks")
    public SuccessResponse stopGenerateLessonTasks(String taskIds) {
        return lessonService.stopGenerateLessonTasks(taskIds);
    }

    @ApiOperation(value = "生成课程详情", nickname = "51b297dc-1e6d-4c78-8449-821dc283392e")
    @PostMapping(value = "/generateLesson")
    public SuccessResponse generateLesson(String taskId) {
        lessonService.generateLesson(taskId);
        return new SuccessResponse();
    }

    @ApiOperation(value = "获取 Unit 正在生成课程的任务", nickname = "f627b1a7-d6b7-416e-9917-2f0bfd3b7e12")
    @GetMapping(value = "/getUnitGeneratingLessonTask")
    public SuccessResponse getUnitGeneratingLessonTask(String unitId) {
        return lessonService.getUnitGeneratingLessonTask(unitId);
    }

    @ApiOperation(value = "检查是否 authCode 是否合法", nickname = "90727e54-3c21-4eab-baaa-7f4bb29691c5")
    @PostMapping(value = "/checkGoogleAuth")
    public SuccessResponse checkGoogleAuth(@RequestBody CheckGoogleAuthRequest request,
                                           HttpServletRequest httpServletRequest) throws IOException {
        return googleAuthService.checkGoogleAuth(request, httpServletRequest);
    }

    @ApiOperation(value = "退出登录", nickname = "90727e54-3c21-4eab-baaa-7f4bb29691c5")
    @PostMapping(value = "/logoutGoogleAuth")
    public SuccessResponse logoutGoogleAuth(@RequestBody CheckGoogleAuthRequest request) throws IOException {
        return googleAuthService.logoutGoogleAuth(request);
    }

    @ApiOperation(value = "生成课程文档", nickname = "88f98c4b-2636-42fd-a701-ceb299705b2c")
    @GetMapping(value = "/generateLessonDetailDoc")
    public DownFileResponse generateLessonDetailDoc(@RequestHeader(HeaderConstants.X_UID) String userId,
                                                    @RequestParam("lessonId") String lessonId,
                                                    @RequestParam(value = "mappedFrameworkId", required = false) String mappedFrameworkId,
                                                    @RequestParam("scopeKey") String scopeKey,
                                                    @RequestParam("downloadType") String downloadType,
                                                    @RequestParam(value = "fileName", required = false) String fileName,
                                                    @RequestParam(value = "showClrSource", required = false) boolean showClrSource,
                                                    @ApiParam(value = "下载的文档内容的语言类型的语言码", required = false) @RequestParam(required = false) String langCode,
                                                    @RequestParam(value = "showImpStepSourceMap", required = false) String showImpStepSourceMap,
                                                    @RequestParam(value = "showMappedTypicalBehaviors", required = false) String showMappedTypicalBehaviors) throws IOException {
        return lessonService.generateLessonDetailDoc(userId, lessonId, mappedFrameworkId, scopeKey, downloadType, fileName, showClrSource, langCode, JSONObject.parseObject(showImpStepSourceMap, Map.class), showMappedTypicalBehaviors);
    }

    @ApiOperation(value = "开关保存", nickname = "95127e54-3c21-4eab-baaa-7f4bb29691c5")
    @PostMapping(value = "/saveOpenMixedAgeGroup")
    public SuccessResponse saveOpenMixedAgeGroup(@RequestBody SaveMixedAgeGroupMetaRequest request) throws IOException {
        return lessonService.saveOpenMixedAgeGroup(request);
    }

    @ApiOperation(value = "获取是否开启混合年龄开关", nickname = "9fc3faa7-d136-4435-a0ac-36c0a71b401f")
    @GetMapping(value = "/getOpenMixedAgeGroup")
    public SuccessResponse getOpenMixedAgeGroup() {
        return lessonService.getOpenMixedAgeGroup();
    }

    @ApiOperation(value = "通过 Lesson Type 和 LessonId 以及 Lesson 的数据生成对应的 Google Slides 内容",
            nickname = "c7dc30ce-bf4e-4366-88f2-fb911e584e40")
    @ResponseBody
    @RequestMapping(value = "/createLessonGoogleSlide", method = RequestMethod.POST)
    public LessonTemplateModel createLessonGoogleSlide(@RequestBody CreateLessonGoogleSlideRequest request) throws Exception {
        return lessonService.createLessonGoogleSlide(request);
    }

    @ApiOperation(value = "通过 LessonId 以及 Lesson 的数据生成对应的 Google Slides 内容",
            nickname = "c40a34c34-d45f-4ba6-8779-d38163039663")
    @ResponseBody
    @RequestMapping(value = "/createLessonSlides", method = RequestMethod.POST)
    public CreateLessonGoogleSlidesResponse createLessonSlides(@RequestBody CreateLessonGoogleSlidesRequest request) throws Exception {
        return lessonService.createLessonSlides(request);
    }

    @ApiOperation(value = "上传课程 slides 接口", nickname = "f3d4df3d-41b7-4c5d-a00d-43de9ad0bf83")
    @ResponseBody
    @RequestMapping(value = "/uploadLessonSlides", method = RequestMethod.POST)
    public UploadLessonSlidesResponse uploadLessonSlides(@RequestBody UploadLessonSlidesRequest request) throws Exception {
        return lessonService.uploadLessonSlides(request);
    }

    @ApiOperation(value = "更新要生成 Google Slides 的 Metadata",
            nickname = "eaa7af45-2423-471e-be80-625853579569")
    @ResponseBody
    @RequestMapping(value = "/updateLessonSlides", method = RequestMethod.POST)
    public CreateLessonGoogleSlidesResponse updateLessonSlides(@RequestBody UpdateLessonGoogleSlidesRequest request) throws Exception {
        return lessonService.updateLessonSlides(request);
    }

    @ApiOperation(value = "同步课程幻灯片数据", nickname = "f1e54553-2666-4f7d-aa64-4f5dac945d12")
    @ResponseBody
    @PostMapping(value = "/syncSlidesData")
    public void syncSlidesData(String id) {
        lessonService.syncSlidesData(id);
    }

    @ApiOperation(value = "更新要生成 Google Slides 的 Metadata",
            nickname = "c7dc30ce-c3de-4366-88f2-fb911e584e40")
    @ResponseBody
    @RequestMapping(value = "/updateLessonGoogleSlideMetadata", method = RequestMethod.POST)
    public LessonTemplateModel updateLessonGoogleSlideMetadata(@RequestBody CreateLessonGoogleSlideRequest request) throws Exception {
        return lessonService.updateLessonGoogleSlideMetadata(request);
    }

    @ApiOperation(value = "将创建出来的 PPT 保存到用户的 Google Drive 中",
            nickname = "c7dc30ce-bf4e-4366-88f2-fb911e584e40")
    @ResponseBody
    @RequestMapping(value = "/saveLessonGoogleSlide", method = RequestMethod.POST)
    public LessonTemplateModel saveLessonGoogleSlide(@RequestBody SaveLessonGoogleSlideRequest request) throws Exception {
        return lessonService.saveLessonGoogleSlide(request);
    }

    @ApiOperation(value = "获取课程的 Google Slides 信息", nickname = "a7076687-023d-4dab-94a3-fc63aafd7c5a")
    @ResponseBody
    @GetMapping(value = "/getLessonTemplates")
    public GetLessonTemplatesResponse getLessonTemplates(@RequestParam("lessonId") String lessonId, @RequestParam(value = "ageGroup", required = false) String ageGroup) {
        return lessonService.getLessonTemplates(lessonId, ageGroup);
    }

    @ApiOperation(value = "删除课程模板", nickname = "31bd3f66-8e34-40b0-9736-aa4c6e82675a")
    @ResponseBody
    @PostMapping(value = "/deleteLessonTemplate")
    public SuccessResponse deleteLessonTemplate(@RequestParam("id") String id) {
        return lessonService.deleteLessonTemplate(id);
    }

    @ApiOperation(value = "获取课程幻灯片", nickname = "7ebbc19f-ff4e-4961-8dc9-51986133f8a1")
    @ResponseBody
    @GetMapping(value = "/getLessonSlides")
    public GetLessonSlidesResponse getLessonSlides(@RequestParam("lessonId") String lessonId) {
        return lessonService.getLessonSlides(lessonId);
    }

    @ApiOperation(value = "生成课程幻灯片", nickname = "50d9d610-29b1-4ece-8af0-2ba78a4f544b")
    @ResponseBody
    @PostMapping(value = "/generateLessonSlides")
    public SuccessResponse generateLessonSlides(String taskId) {
        return lessonService.generateLessonSlides(taskId);
    }

    @ApiOperation(value = "保存课程幻灯片到用户的 Google Drive", nickname = "8f3bc19f-ff4e-4961-8dc9-51986133f9b2")
    @ResponseBody
    @PostMapping(value = "/saveLessonSlidesToDrive")
    public DownFileResponse saveLessonSlidesToDrive(@RequestBody SaveLessonSlidesToDriveRequest request) throws Exception {
        return lessonService.saveLessonSlidesToDrive(request);
    }

    @ApiOperation(value = "删除临时幻灯片项目", nickname = "1a39963b-d642-465f-9cd0-a1e90c5e9803")
    @ResponseBody
    @PostMapping(value = "/deleteTempLessonSlides")
    public SuccessResponse deleteTempLessonSlides(@RequestParam("id") String id) throws IOException {
        return lessonService.deleteTemplateSlides(id);
    }

    @ApiOperation(value = "获取框架映射关系", nickname = "b3eb6ac7-79b5-4ea9-9118-7ca7d6fc47cc")
    @GetMapping(value = "/getFrameworkMeasureMap")
    public GetFrameworkMeasureMapResponse getFrameworkMeasureMap(@RequestParam("frameworkId") String frameworkId,
                                                                 @RequestParam(value = "mappedFrameworkId", required = false) String mappedFrameworkId) {
        return lessonService.getFrameworkMeasureMap(frameworkId, mappedFrameworkId);
    }

    @ApiOperation(value = "检查课程内容是否包含书信息 (临时接口)", nickname = "d3c6e374-6af7-44b6-b557-6ebb67e97e67")
    @ResponseBody
    @GetMapping(value = "/checkLessonContainBooks")
    public SuccessResponse checkLessonContainBooks(@ApiParam(value = "课程 ID" ) String id) {
        CheckLessonContainBookRequest request = new CheckLessonContainBookRequest();
        request.setId(id);
        return lessonService.checkLessonContainBooks(request);
    }

    @ApiOperation(value = "检查课程内容是否包含书信息", nickname = "76C9E92A-E539-AAF9-DE45-7BAF496FC3E0")
    @ResponseBody
    @PostMapping(value = "/checkLessonContainBooks")
    public SuccessResponse checkLessonContainBooks(@RequestBody CheckLessonContainBookRequest request) {
        return lessonService.checkLessonContainBooks(request);
    }

    @ApiOperation(value = "生成自定义课程模板", nickname = "4d875504-862d-4a7f-b06f-ce930c1cb209")
    @PostMapping(value = "/generateCustomLessonTemplateStream",  produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter generateCustomLessonTemplateStream(@RequestBody GenerateCustomLessonTemplateRequest request) {
        return lessonService.generateCustomLessonTemplateStream(request);
    }

    @ApiOperation(value = "获取课程历史版本列表", nickname = "b874f1f3-1f43-4abb-932e-d05a93c331d6")
    @ResponseBody
    @RequestMapping(value = "/getVersionList", method = RequestMethod.GET)
    public LessonVersionResponse getLessonVersions(
            @RequestParam("lessonId") String lessonId,
            @RequestParam(value = "page", defaultValue = "1") int page,
            @RequestParam(value = "pageSize", defaultValue = "10") int pageSize) {
        return lessonService.getLessonVersions(lessonId, page, pageSize);
    }

    @ApiOperation(value = "获取指定版本的课程详情", nickname = "b5e398c7-6f79-44f5-9ba2-4de21f2b533e")
    @ResponseBody
    @RequestMapping(value = "/getVersionDetail", method = RequestMethod.GET)
    public LessonDetailResponse getLessonByVersion(@RequestParam("versionId") String versionId) {
        return lessonService.getLessonByVersion(versionId);
    }

    @ResponseBody
    @PostMapping(value = "/getLessonsVersionInfo")
    public CurriculumListResponse<LessonVersionEntity> getLessonsVersionInfo(@RequestBody LessonVersionRequest request) {
        List<LessonVersionEntity> data = lessonVersionService.getLessonsVersionInfo(request);
        return new CurriculumListResponse<>(data);
    }

    /**
     * 创建批量生成课程文件包任务
     *
     * @param request 请求数据
     * @return 响应
     */
    @PostMapping(value = "/createBatchGenerateLessonTask")
    public SuccessResponse createBatchGenerateLessonTask(@RequestBody CreateBatchGenerateTask request){
        return lessonDownloadService.createBatchGenerateLessonTask(request);
    }

    /**
     * 获取当前课程的可下载 Slides 数据
     *
     * @param lessonId 课程 ID
     * @return 响应
     */
    @GetMapping(value = "/getLessonSlidesDownloadModel")
    public LessonSlidesModel getLessonSlidesDownloadModel(@RequestParam("lessonId") String lessonId) {
        return lessonService.checkLessonSlides(lessonId, null);
    }


    /**
     * 创建课程多个模板下载任务
     *
     * @param lessonId 课程 ID
     * @return 响应
     */
    @GetMapping(value = "/createLessonTemplatesDownload")
    public DownFileResponse createLessonTemplatesDownload(@RequestParam("lessonId") String lessonId) {
        return lessonDownloadService.createLessonTemplatesDownload(lessonId);
    }

    @ApiOperation(value = "获取课程锚文本内容")
    @GetMapping(value = "/getLessonAnchorText")
    public com.learninggenie.common.data.entity.lesson2.LessonAnchorTextEntity getLessonAnchorText(@RequestParam("url") String url) {
        return lessonService.getLessonAnchorText(url);
    }
}
