package com.learninggenie.api.service.lesson2;

import com.learninggenie.api.model.*;
import com.learninggenie.api.model.ai.check.CheckContentResponse;
import com.learninggenie.api.model.captcha.SendStatusResponse;
import com.learninggenie.api.model.curriculum.*;
import com.learninggenie.api.model.curriculum.lesson.*;
import com.learninggenie.api.model.curriculum.test.CreateLessonOverviewTestRequest;
import com.learninggenie.api.model.curriculum.test.CreateLessonTestRequest;
import com.learninggenie.api.model.curriculum.test.CreateTypicalBehaviorsTestRequest;
import com.learninggenie.api.model.dll.DLLSubjectModel;
import com.learninggenie.api.model.lesson2.LessonDetailResponse;
import com.learninggenie.api.model.lesson2.*;
import com.learninggenie.api.model.lesson2.curriculum.BatchCurriculumPdfRequest;
import com.learninggenie.api.model.lesson2.curriculum.ExtractAndSearchCoverResponse;
import com.learninggenie.api.model.note.*;
import com.learninggenie.api.model.prompt.CreatePromptTestResponse;
import com.learninggenie.common.data.entity.PageList;
import com.learninggenie.common.data.entity.authentication.UserProfileEntity;
import com.learninggenie.common.data.entity.lesson2.*;
import com.learninggenie.common.data.enums.prompt.CreateSource;
import com.learninggenie.common.data.model.AgencyModel;
import com.learninggenie.common.data.model.lesson2.*;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.List;
import java.util.Map;

public interface LessonService {

    /**
     * 发布课程
     *
     * @param request 课程信息
     * @return 课程id
     */
    PublishLessonResponse publishLesson(LessonPublishRequest request);

    /**
     * 批量发布课程
     *
     * @param request 批量发布课程请求
     * @return 批量发布课程 id 列表
     */
    SuccessResponse batchPublishAdaptedLesson(BatchPublishAdaptedLessonRequest request);

    /**
     * 获取映射框架ids v2
     *
     * @param mappingFrameworkModel 映射框架模型
     * @return {@link String }
     */
    String getMappedFrameworkIdsV2(GetMappedFrameworkIdsModel mappingFrameworkModel);

    /**
     * 批量删除改编课程
     *
     * @param request 入参类
     * @return 操作结果
     */
    SuccessResponse batchRemoveAdaptedLesson(LessonBatchRemoveAdaptedRequest request);

    /**
     * 保存课程草稿
     *
     * @param request 课程信息
     * @return 草稿信息
     */
    LessonEntity saveLessonDraft(LessonSaveDraftRequest request);

    /**
     * 对课程点赞
     *
     * @param lessonId 课程Id
     */
    void like(String lessonId);

    /**
     * 取消课程的点赞
     *
     * @param lessonId 课程Id
     */
    void cancelLike(String lessonId);

    /**
     * 收藏课程
     *
     * @param lessonId 收藏课程的Id
     */
    void favorite(String lessonId);

    /**
     * 取消收藏课程
     *
     * @param lessonId 收藏课程的Id
     */
    void cancelFavorite(String lessonId);

    /**
     * 查询所有的主题
     *
     * @param includeAgency 是否包含机构主题
     * @param includeCustom 是否包含自定义主题
     * @return 主题的集合
     */
    LessonGetThemesResponse getThemes(boolean includeAgency, boolean includeCustom);

    /**
     * 添加课程自定义主题
     *
     * @param request 添加课程主题请求体
     * @return 操作结果
     */
    SuccessResponse addLessonTheme(AddLessonThemeRequest request);

    /**
     * 删除课程自定义主题
     *
     * @param id 主题ID
     * @return 操作结果
     */
    SuccessResponse deleteLessonTheme(String id);

    /**
     * 查询所有的年龄组
     *
     * @param region ：地区 分为 加州 和 非加州
     * @return 年龄组的集合
     */
    LessonGetAgeGroupsResponse getAgeGroups(String region);

    /**
     * 获取评论列表
     *
     * @param pageSize 页大小
     * @param pageNum  页码
     * @param lessonId 课程ID
     * @return 评论列表、课程作者ID、 总评论数、顶级评论数、
     */
    CommentPageResponse getComments(String lessonId, Integer pageSize, Integer pageNum);

    /**
     * 评论课程
     *
     * @param request 评论请求内容
     * @return 生成的评论ID
     */
    CommentResponse commentLesson(CommentLessonRequest request);

    /**
     * 删除评论
     *
     * @param id 评论ID
     */
    DeleteCommentResponse deleteComment(String id);

    /**
     * 查询 复制本课程的课程集合
     *
     * @param lessonId 本课程的I的
     * @return 课程集合
     */
    GetLinkedLessonResponse getCopyLessons(String lessonId);


    /**
     * 查询课程的详情
     *
     * @param id 课程Id
     * @return 课程的详情
     */
    LessonDetailResponse getLessonDetail(String id);

    /**
     * 查询单元课程中的课程详情
     *
     * @param id 课程Id
     * @return 单元课程中的课程详情
     */
    LessonModel getCurriculumLessonDetail(String id);

    /**
     * 通过周计划 ID 和 周计划项 ID 获取相同 center 的课程详情
     *
     * @param lastWeeklyPlanId 上一个周的周计划 ID
     * @param itemId           周计划项 ID
     * @return 课程详情
     */
    LessonModel getCenterLessonDetail(String lastWeeklyPlanId, String itemId);

    /**
     * 删除课程
     *
     * @param lessonId 课程Id
     * @return 课程id
     */
    DeleteLessonResponse deleteLesson(String lessonId);

    /**
     * 查询DRDP 中的领域
     *
     * @return 领域的集合
     */
    ListDomainsResponse listDomains();

    /**
     * 回收站恢复课程
     *
     * @param id 课程Id
     * @return 课程id
     */
    RecoverLessonResponse restoreLesson(String id);

    /**
     * 恢复课程
     *
     * @param id 课程Id
     * @return 课程id
     */
    RecoverLessonResponse recoverDeletedLesson(String id);

    /**
     * 查询用户的推荐权限及课程的推荐状态
     *
     * @param lessonId 课程ID
     * @return 推荐权限及课程状态
     */
    LessonRecommendStatusResponse getLessonRecommendStatus(String lessonId);

    /**
     * 推荐到agency 的课程
     *
     * @param id 课程Id
     */
    void recommendToAgency(String id);

    /**
     * 取消推荐到agency 的课程
     *
     * @param id 课程Id
     */
    void cancelRecommendToAgency(String id);

    /**
     * 复制课程
     *
     * @param id 课程ID
     * @return 复制出的课程信息
     */
    LessonEntity copyLesson(String id, Boolean useBeforeName);

    /**
     * 复制改编课程
     *
     * @param id 课程 ID
     * @param adaptGroupId 改编班级 ID
     * @return 复制出的课程信息
     */
    LessonModel copyAdaptLesson(String id, String adaptGroupId);

    /**
     * 查询回收站课程
     *
     * @return 回收站的课程
     */
    PageResponse<DeletedLessonItem> getDeletedLessons(Integer pageSize, Integer pageNum);

    /**
     * 添加课程浏览量
     *
     * @param lessonId 课程ID
     */
    void increaseLessonViewCount(String lessonId);

    /**
     * 回收站删除
     *
     * @param lessonId 课程ID
     * @return 课程id
     */
    DeleteLessonPermanentlyResponse deleteLessonPermanently(String lessonId);

    /**
     * 查询本课程的更新版本
     *
     * @param lessonId 课程的ID
     * @return 返回更新记录的集合
     */
    LessonUpdateHistoryResponse getUpdateVersion(String lessonId);

    /**
     * 查询测评框架
     *
     * @return 测评框架集合
     */
    GetFrameworkResponse getFrameworks();

    /**
     * 查询测评框架
     *
     * @return 测评框架集合
     */
    GetFrameworkResponse getExtendFrameworks(GetFrameworkRequest request);

    /**
     * 查询测评点(包括领域)
     *
     * @param frameworkId 框架 的Id
     * @return 测评点的集合
     */
    GetMeasureResponse getMeasures(String frameworkId, Boolean compress);

    /**
     * 查询测评点(包括领域)，支持指定测评点 ID
     *
     * @param frameworkId 框架 ID
     * @param compress 是否压缩
     * @param measureIds 测评点 ID 列表，如果为空则获取所有测评点
     * @return 测评点响应
     */
    GetMeasureResponse getMeasures(String frameworkId, Boolean compress, List<String> measureIds);

    GetMeasureResponse getOnlyDomainMapFrameworkMeasures(String frameworkId, Boolean compress);

    /**
     * 查询测评点(包括领域，只有顶层和底层)
     *
     * @param frameworkId 框架 的Id
     * @return 测评点的集合
     */
    GetMeasureResponse getMeasuresContainTopAndBottom(String frameworkId, String unitId);

    /**
     * 获取登录人所属机构信息
     *
     * @return 机构信息
     */
    AgencyModel getAgency();

    /**
     * 查询 center及 center下的老师信息 (不包括老师助手)
     *
     * @return 学校及其老师信息
     */
    GetCenterTeachersResponse getCenterTeachers();

    /**
     * 查询机构下学校的老师和管理员
     *
     * @return 机构下学校的老师和管理员
     */
    GetAgencyStaffsResponse getAgencyStaffs();

    /**
     * 查询老师是否点过课程详情中对话框提示
     *
     * @return true 或者 false
     */
    LessonDialogResponse getTeacherDialog();

    /**
     * 老师第一次点击对话框提示,添加记录
     */
    void addTipRecords();

    /**
     * 查询管理端教师课程列表
     *
     * @param pageSize        页码
     * @param pageNum         页大小
     * @param userId          教师ID
     * @param keyword         关键字
     * @param orderKey        排序字段
     * @param isAdaptedLesson 特殊课程
     * @return 教师课程列表、页码 、页大小、总记录数
     */
    PageResponse<ManageTeacherLessonPageItem> getManageTeacherLessons(Integer pageSize, Integer pageNum, String userId, String keyword, String orderKey, Boolean isAdaptedLesson);

    /**
     * 查询公共课程
     *
     * @param pageSize 页大小
     * @param pageNum 页码
     * @param ages 年龄值
     * @param domainIds 领域 ID
     * @param themeIds 主题 ID
     * @param keyword 关键字
     * @param orderKey 排序
     * @param otherMeasureIds 测评点 ID
     * @param mappedFrameworkId 当前展示的映射框架
     * @return 公共课程列表、页码、页大小、总记录数
     */
    PageResponse<LessonPublicPageItem> getPublicLessons(Integer pageSize, Integer pageNum, String ages, String domainIds, String themeIds, String keyword, String orderKey, String otherMeasureIds, String mappedDrdpFrameworkIds, String mappedFrameworkId);

    /**
     * 查询机构课程
     *
     * @param pageSize 页大小
     * @param pageNum 页码
     * @param ages 年龄值
     * @param domainIds 领域 ID
     * @param themeIds 主题 ID
     * @param keyword 关键字
     * @param orderKey 排序
     * @param otherMeasureIds 测评点 ID
     * @param mappedFrameworkId 当前展示的映射框架
     * @return 机构课程列表、页码、页大小、总记录数
     */
    PageResponse<LessonAgencyPageItem> getAgencyLessons(Integer pageSize, Integer pageNum, String ages, String domainIds, String themeIds, String keyword, String orderKey, String otherMeasureIds, String mappedDrdpFrameworkIds, String mappedFrameworkId);

    /**
     * 查询我的课程
     *
     * @param pageSize 页大小
     * @param pageNum 页码
     * @param ages 年龄值
     * @param domainIds 领域 ID
     * @param themeIds 主题 ID
     * @param keyword 关键字
     * @param orderKey 排序
     * @param status 课程状态
     * @param otherMeasureIds 测评点 ID
     * @param all 是否查询全部课程
     * @param publicOpen 公共课程开关是否打开
     * @param mappedDrdpFrameworkIds DRDP 框架 ID
     * @param isAdaptedLesson 特殊课程
     * @param mappedFrameworkId 当前展示的映射框架
     * @param adaptGroupId 改编班级 ID
     * @return 我的课程列表、页码、页大小、总记录数
     */
    PageResponse<LessonMyLessonsPageItem> getLessons(Integer pageSize, Integer pageNum, String ages, String domainIds, String themeIds, String keyword, String orderKey, String status, String otherMeasureIds, boolean all, boolean publicOpen, String mappedDrdpFrameworkIds, Boolean isAdaptedLesson, String mappedFrameworkId, String adaptGroupId);

    /**
     * 查询我的课程
     *
     * @param pageSize 页大小
     * @param pageNum 页码
     * @param ages 年龄值
     * @param domainIds 领域 ID
     * @param themeIds 主题 ID
     * @param keyword 关键字
     * @param orderKey 排序
     * @param status 课程状态
     * @param otherMeasureIds 测评点 ID
     * @param mappedDrdpFrameworkIds DRDP 框架 ID
     * @param isAdaptedLesson 是否特殊课程
     * @param mappedFrameworkId 当前展示的映射框架
     * @param adaptGroupId 改编班级 ID
     * @return 我的课程列表、页码、页大小、总记录数
     */
    PageResponse<LessonMyLessonsPageItem> getMyLessons(Integer pageSize, Integer pageNum, String ages, String domainIds, String themeIds, String keyword, String orderKey, String status, String otherMeasureIds, boolean all, boolean publicOpen, String mappedDrdpFrameworkIds, Boolean isAdaptedLesson, String mappedFrameworkId, String adaptGroupId);


    /**
     * 获取 LessonPlan 列表
     *
     * @param pageSize
     * @param pageNum
     * @return
     */
    PageResponse<LessonMyLessonsPageItem> getLessonPlanList(Integer pageSize, Integer pageNum);

    /**
     * 查询我收藏的课程
     *
     * @param pageSize 页大小
     * @param pageNum 页码
     * @param ages 年龄值
     * @param domainIds 领域 ID
     * @param themeIds 主题 ID
     * @param keyword 关键字
     * @param orderKey 排序
     * @param otherMeasureIds 测评点 ID
     * @param mappedDrdpFrameworkIds DRDP 框架 ID
     * @param isAdaptedLesson 是否特殊课程
     * @param mappedFrameworkId 当前展示的映射框架
     * @param adaptGroupId 改编班级 ID
     * @return 我的收藏列表、页码、页大小、总记录数
     */
    PageResponse<LessonMyFavoritePageItem> getMyFavoriteLessons(Integer pageSize, Integer pageNum, String ages, String domainIds, String themeIds, String keyword, String orderKey, String otherMeasureIds, String mappedDrdpFrameworkIds, Boolean isAdaptedLesson, String mappedFrameworkId, String adaptGroupId);

    /**
     * 查询子评论列表
     *
     * @param rootCommentId 顶级评论Id
     * @param pageSize      页大小
     * @param pageNum       页码
     * @return 子评论列表
     */
    PageList<CommentModel> getSubComments(String rootCommentId, Integer pageSize, Integer pageNum);

    /**
     * 查询课程最新草稿详情
     *
     * @param id 课程 ID
     * @return 课程详情
     */
    LessonDetailResponse getLessonLastDraftDetail(String id);

    /**
     * 下载课程详情 PDF
     *
     * @param id                课程 ID
     * @param mappedFrameworkId 当前展示的映射框架
     * @param showDescription   是否展示Measures详情
     * @param onlyShowCore      是否只展示核心测评点
     * @return URL
     */
    DownFileResponse generateLessonPDF(String id, String mappedFrameworkId, Boolean showDescription, Boolean onlyShowCore, boolean showSource, String langCode, Map<String, Boolean> showImpStepSourceMap, String showMappedTypicalBehaviors);

    /**
     * 下载课程详情 PDF （服务）
     *
     * @param id                课程 ID
     * @param mappedFrameworkId 当前展示的映射框架
     * @param showDescription   是否展示Measures详情
     * @param onlyShowCore      是否只展示核心测评点
     * @return URL
     */
    DownFileResponse generateLessonPDF(String id, String mappedFrameworkId, Boolean showDescription, Boolean onlyShowCore, boolean showSource, String langCode, Map<String, Boolean> showImpStepSourceMap, Boolean isServer, String showMappedTypicalBehaviors);

    /**
     * 检查课程 Slides，如果没有幻灯片进行生成
     *
     * @param lessonId 课程 ID
     * @param age      年龄段
     * @return 课程 Slides
     */
    com.learninggenie.api.model.lesson2.LessonSlidesModel checkLessonSlides(String lessonId, String age);

    /**
     * 检查课程所有模板，如果没有幻灯片进行生成
     *
     * @param id 课程 ID
     * @param step 课程步骤
     * @return List<LessonTemplateDownloadModel>
     */
    List<LessonTemplateDownloadModel> checkLessonTemplateSlides(String id, LessonStepModel step);

    ExternalMediaResponse saveExternalMedia(LessonsExternalMediaResponse lessonsMediaExternalEntity);

    /**
     * 通过课程 ID 获取年龄段
     *
     * @param lessonIds 课程 ID 列表
     * @return 课程年龄段
     */
    Map<String, String[]> getLessonAgeByLessonIds(List<String> lessonIds);

    /**
     * 获取测评点
     *
     * @param lessons
     * @return
     */
    Map<String, String[]> getLessonMeasureNamesByLessonIds(List<LessonEntity> lessons);

    /**
     * 通过课程作者用户 ID 获取作者信息
     *
     * @param authorUserIds 用户 ID 列表
     * @return 用户信息
     */
    Map<String, UserProfileEntity> getLessonAuthorByUserIds(List<String> authorUserIds);

    /**
     * 设置链接封面 ID
     *
     * @param lessons
     */
    void setLessonExternalMediaId(List<LessonEntity> lessons);

    /**
     * 根据媒体 ID 获取媒体 URL
     */
    Map<String, String> getMediaURLs(List<String> mediaIds);

    /**
     * 将获取到的同一个媒体 ID 下的 URL 作为数组返回
     *
     * @param mediaURLs 媒体 ID URL 映射
     * @param mediaIds  媒体 ID 列表，逗号分隔
     * @return 媒体 URL 列表
     */
    String[] getMediaURLs(Map<String, String> mediaURLs, String mediaIds);


    /**
     * 通过课程 ID 查询该课程是否已点赞
     *
     * @param userId    用户 ID
     * @param lessonIds 课程 ID 列表
     * @return 课程点赞记录
     */
    Map<String, LessonLikeEntity> getLessonLikes(String userId, List<String> lessonIds);

    /**
     * 通过课程 ID 查询该课程是否已收藏
     *
     * @param userId    用户 ID
     * @param lessonIds 课程 ID 列表
     * @return 课程收藏记录
     */
    Map<String, LessonFavoriteEntity> getLessonFavorites(String userId, List<String> lessonIds);

    /**
     * iPad发送下载文件附件邮件
     *
     * @param request 下载文件邮件发送请求体
     */
    SendStatusResponse sendFileDownloadEmail(FileDownloadEmailRequest request);

    /**
     * 获取课程的 DLL
     *
     * @param lessonId 课程 ID
     * @return DLL 列表
     */
    List<DLLSubjectModel> getDLLModel(String lessonId);

    /**
     * 创建课程
     *
     * @param request 课程保存请求体
     * @return 课程 ID
     */
    IdResponse createLesson(CreateLessonRequest request);

    /**
     * 更新课程
     *
     * @param request 课程保存请求体
     * @return 课程 ID
     */
    IdResponse updateLesson(CreateLessonRequest request);

    /**
     * 更新课程名称
     *
     * @param request 课程名称保存请求体
     * @return 课程 ID
     */
    IdResponse updateLessonName(CreateLessonNameRequest request);


    /**
     * 生成课程概览（异步）
     *
     * @param unitId 单元 ID
     * @param planId 周计划 ID
     * @return SSE Emitter
     */
    SseEmitter generateLessonOverviewStream(String unitId, String planId);

    /**
     * 课程概览需要进行二次分配判断
     *
     * @param unitId              单元 ID
     * @param planId              周计划 ID
     * @param promptUsageRecordId
     */
    List<LessonOverviewSecondAllocationResponse> createLessonOverviewSecondAllocation(String unitId, String planId, String promptUsageRecordId);

    /**
     * 生成区角活动概览（异步）
     *
     * @param request 请求数据
     * @return SSE Emitter
     */
    SseEmitter generateCornerCenterOverviewStream(GenerateCornerCenterRequest request);

    /**
     * 重新生成单个区角活动概览（异步）
     *
     * @param request 请求数据
     * @return SSE Emitter
     */
    SseEmitter generateSingleCornerCenterOverviewStream(GenerateCornerCenterRequest request);


    /**
     * 生成 Centers 课程概览（异步）
     *
     * @param unitId 单元 ID
     * @param planId 周计划 ID
     * @return SSE Emitter
     */
    SseEmitter generateCenterOverviewStream(String unitId, String planId);

    /**
     * 生成单个课程概览（异步）
     *
     * @param unitId    单元 ID
     * @param planId    周计划 ID
     * @param dayOfWeek 周几
     * @return SSE Emitter
     */
    SseEmitter generateSingleLessonOverviewStream(String unitId, String planId, String dayOfWeek);

    /**
     * 课程概览评估
     *
     * @param request 课程概览评估请求
     * @return 课程概览评估结果
     */
    LessonEvaluationResponse evaluationLessonIdeas(LessonEvaluationRequest request);


    /**
     * 课程详情评估
     *
     * @param request 课程详情评估请求
     * @return 课程详情评估结果
     */
    LessonEvaluationResponse evaluationLessonDetail(LessonEvaluationRequest request);

    /**
     * 生成单个 Center 课程概览（异步）
     *
     * @param unitId   单元 ID
     * @param planId   周计划 ID
     * @param centerId Center 组 ID
     * @return SSE Emitter
     */
    SseEmitter generateSingleCenterOverviewStream(String unitId, String planId, String centerId);

    /**
     * 生成课程概览
     *
     * @param request 请求内容
     * @return 课程概览
     */
    GenerateLessonOverviewResponse generateLessonOverview(GenerateLessonOverviewRequest request);

    /**
     * 评估课程概览
     *
     * @param promptUsageRecordId 评估记录 ID
     * @return 评估结果
     */
    EvaluatePromptResponse evaluateLessonOverview(String promptUsageRecordId);

    /**
     * 创建课程概览测试
     *
     * @param request 请求内容
     * @return 课程概览测试
     */
    CreatePromptTestResponse createLessonOverviewTest(CreateLessonOverviewTestRequest request);

    /**
     * 生成课程详情（异步）
     *
     * @param request 生成课程详情请求体
     * @return SSE Emitter
     */
    SseEmitter generateLessonStream(GenerateLessonDetailRequest request);

    /**
     * 生成课程 slides 详情（异步）
     *
     * @param request 生成课程详情请求体
     * @return SSE Emitter
     */
    SseEmitter generateLessonSlidesStream(GenerateLessonDetailSlidesRequest request);

    /**
     * 生成课程模版（异步）
     *
     * @param request 生成课程模版请求体
     * @return SSE Emitter
     */
    SseEmitter generateLessonTemplateStream(GenerateLessonTemplateRequest request);

    /**
     * AI 助手生成课程模版（异步）
     *
     * @param request 生成 AI 课程模版请求
     * @return SSE Emitter
     */
    SseEmitter generateAILessonTemplateStream(GenerateAILessonTemplateRequest request);

    /**
     * 生成 centers 组课程详情（异步）
     *
     * @param request 生成课程详情请求体
     * @return SSE Emitter
     */
    SseEmitter generateCenterLessonStream(GenerateLessonDetailRequest request);

    /**
     * 转换指定内容为课程
     *
     * @param request 请求内容
     * @return SSE Emitter
     */
    SseEmitter convertLessonStream(ConvertLessonRequest request);

    /**
     * curriculum中Ai助手生成课程详情
     *
     * @param request 请求内容
     * @return SSE Emitter
     */
    SseEmitter curriculumLessonPlanStream(ConvertLessonRequest request);

    /**
     * 生成课程详情
     *
     * @param request 请求内容
     * @return 课程详情
     */
    GenerateLessonResponse generateLesson(GenerateLessonRequest request);

    /**
     * 评估课程详情
     *
     * @param promptUsageRecordId 评估记录 ID
     * @return 评估结果
     */
    EvaluatePromptResponse evaluateLesson(String promptUsageRecordId);

    /**
     * 为完整的课程内容评分
     *
     * @param lessonId 课程 ID
     * @return 评估结果
     */
    EvaluatePromptResponse evaluateWholeLesson(String lessonId, String centerGroupId);

    /**
     * 创建课程详情测试
     *
     * @param request 请求内容
     * @return 课程详情测试
     */
    CreatePromptTestResponse createLessonTest(CreateLessonTestRequest request);


    /**
     * 生成课程封面
     *
     * @param request 请求信息
     * @return 封面信息
     */
    UploadMediaResponse generateLessonCover(GenerateLessonCoverRequest request) throws IOException;

    /**
     * 生成并保存课程封面
     *
     * @param request 请求信息
     * @return 封面信息
     */
    UploadMediaResponse generateAndSaveLessonCover(GenerateLessonCoverRequest request);

    /**
     * 提取课程封面
     *
     * @param request 请求信息
     * @return 封面信息
     */
    ExtractAndSearchCoverResponse extractAndSearchLessonCover(ExtractAndSearchLessonCoverRequest request);


    /**
     * 搜索封面
     *
     * @param keywords 关键字
     * @param source 来源
     * @param pageSize 页大小
     * @param pageNum 页码
     * @return {@link SearchImageResponse} 搜索图片响应
     */
    SearchImageResponse searchCover(String keywords, String source, Integer pageSize, Integer pageNum) throws IOException;

    /**
     * 设置封面信息
     *
     * @param request 请求体
     * @return 操作成功响应
     */
    SetCoverInfoResponse setCoverInfo(SetCoverInfoRequest request);

    /**
     * 设置课程幻灯片图片
     *
     * @param imageUrl 图片 URL
     * @return 图片 s3 地址
     */
    DownFileResponse setLessonSlideImage(String imageUrl);

    /**
     * 重新生成课程
     *
     * @param request 请求内容
     * @return 课程详情
     */
    GenerateLessonResponse redesignLesson(RedesignLessonRequest request);

    /**
     * 评估重新生成课程内容
     *
     * @param promptUsageRecordId Prompt 使用记录 ID
     */
    void evaluateRedesignLesson(String promptUsageRecordId);

    /**
     * 根据课程内容生成测评点
     *
     * @param request 请求内容
     * @return 测评点
     */
    GenerateMeasuresByLessonResponse generateMeasuresByLesson(GenerateMeasuresByLessonRequest request);


    /**
     * 检查课程内容是否合适
     *
     * @param request 请求内容
     * @return 测评点
     */
    CheckContentResponse checkLessonContentByAI(CheckLessonContentRequest request);

    /**
     * 生成 Center 活动概览
     *
     * @param request 请求内容
     * @return Center 活动概览
     */
    GenerateCenterOverviewResponse generateCenterOverview(GenerateCenterOverviewRequest request);

    /**
     * 生成 Center 活动
     *
     * @param request 请求内容
     * @return Center 活动
     */
    GenerateCenterResponse generateCenter(GenerateCenterRequest request);

    /**
     * 生成典型行为（异步）
     *
     * @param unitId            单元 ID
     * @param planId            周计划 ID
     * @param lessonId          课程 ID
     * @param frameworkMapped   是否映射框架
     * @return SSE Emitter
     */
    SseEmitter generateTypicalBehaviorsStream(String unitId, String planId, String lessonId, boolean frameworkMapped);

    /**
     * 生成典型行为（异步）
     *
     * @param request 请求信息
     * @return SSE Emitter
     */
    SseEmitter generateTypicalBehaviorsStream(GenerateTypicalBehaviorsRequest request);

    /**
     * 标准教学指导（异步）
     *
     * @param request 请求信息
     * @return SSE Emitter
     */
    SseEmitter generateTeachingTipsStream(GenerateTypicalBehaviorsRequest request);

    /**
     * 生成课程校训（异步）
     *
     * @param request 请求信息
     * @return SSE Emitter
     */
    SseEmitter generateLessonLearnerProfileStream(GenerateLessonLearnerProfilesRequest request);

    /**
     * 生成课程测验
     *
     * @param request 请求信息
     * @return SSE Emitter
     */
    SseEmitter generateLessonQuizStream(GenerateLessonQuizQuestionRequest request);

    /**
     * 重新生成课程测验
     *
     * @param request 请求信息
     * @return SSE Emitter
     */
    SseEmitter regenerateLessonQuizStream(GenerateLessonQuizQuestionRequest request);

    /**
     * 获取课程测验等级标准
     *
     * @return 课程测验等级标准
     */
    GetLessonQuizLevelStandardResponse getLessonQuizLevelStandard();


    /**
     * 更新课程测验等级标准
     *
     * @param request 请求信息
     * @return 操作结果
     */
    SuccessResponse updateLessonQuizLevelStandard(UpdateLessonQuizLevelStandardRequest request);

    /**
     * 生成典型行为
     *
     * @param request 请求内容
     * @return 典型行为
     */
    GenerateTypicalBehaviorResponse generateTypicalBehavior(GenerateTypicalBehaviorsRequest request);

    /**
     * 生成典型行为
     *
     * @param unitId   单元 ID
     * @param lessonId 课程 ID
     * @return 典型行为
     */
    GenerateTypicalBehaviorResponse generateTypicalBehavior(String unitId, String lessonId, CreateSource createSource);

    /**
     * 评估典型行为
     *
     * @param promptUsageRecordId Prompt 使用记录 ID
     * @return 评估结果
     */
    EvaluatePromptResponse evaluateTypicalBehavior(String promptUsageRecordId);

    /**
     * 创建典型行为测试
     *
     * @param request 请求内容
     * @return 典型行为测试
     */
    CreatePromptTestResponse createTypicalBehaviorsTest(CreateTypicalBehaviorsTestRequest request);

    /**
     * 根据观察内容生成测评点和评分（异步）
     *
     * @param request 请求信息
     * @return SSE Emitter
     */
    SseEmitter generateObservationMeasuresAndLevels(GenerateMeasuresAndLevelsRequest request);

    /**
     * 观察记录内容评估整体质量（异步）
     *
     * @param request 请求信息
     * @return SSE Emitter
     */
    SseEmitter observationEvaluateOverallQuality(ImproveObservationRequest request);

    /**
     * 根据观察记录推荐测评点（异步）
     *
     * @param request 请求信息
     * @return SSE Emitter
     */
    SseEmitter observationRecommendMeasureStream(ImproveObservationRequest request);

    /**
     * 单条观察记录评分（异步）
     *
     * @param request 请求信息
     * @return SSE Emitter
     */
    SseEmitter singleObservationScoreStream(ImproveObservationRequest request);

    /**
     * 比较单条观察记录内容中老师和 GPT 的测评点（异步）
     *
     * @param request 请求信息
     * @return SSE Emitter
     */
    SseEmitter compareObservationMeasureStream(ImproveObservationRequest request);

    /**
     * 优化单条观察记录内容（异步）
     *
     * @param request 请求信息
     * @return SSE Emitter
     */
    SseEmitter improveOptimizeObservation(ImproveObservationRequest request);

    /**
     * 生成差异化学习内容
     *
     * @param unitId   单元 ID
     * @param planId   周计划 ID
     * @param lessonId 课程 ID
     * @return 差异化学习内容
     */
    SseEmitter generateUniversalDesignForLearningStream(String unitId, String planId, String lessonId);

    /**
     * 生成差异化学习内容
     *
     * @param request 请求内容
     * @return 差异化学习内容
     */
    SseEmitter generateUniversalDesignForLearningStream(GenerateUniversalDesignForLearningRequest request);

    /**
     * 生成差异化学习分组内容
     *
     * @param unitId   单元 ID
     * @param planId   周计划 ID
     * @param lessonId 课程 ID
     * @return 差异化学习内容
     */
    SseEmitter generateUniversalDesignForLearningGroupStream(String unitId, String planId, String lessonId, String groupId);

    /**
     * 生成差异化学习内容
     *
     * @param unitId   单元 ID
     * @param planId   周计划 ID
     * @param lessonId 课程 ID
     * @return 差异化学习内容
     */
    GenerateUniversalDesignForLearningResponse generateUniversalDesignForLearning(String unitId, String planId, String lessonId);

    /**
     * 生成差异化学习内容
     *
     * @param request 请求内容
     * @return 差异化学习内容
     */
    GenerateUniversalDesignForLearningResponse generateUniversalDesignForLearning(GenerateUniversalDesignForLearningRequest request);

    /**
     * 评估差异化学习内容
     *
     * @param promptUsageRecordId Prompt 使用记录 ID
     * @return 评估结果
     */
    EvaluatePromptResponse evaluateUniversalDesignForLearning(String promptUsageRecordId);

    /**
     * 生成文化响应式教学内容
     *
     * @param unitId   单元 ID
     * @param planId   周计划 ID
     * @param lessonId 课程 ID
     * @return 文化响应式教学内容
     */
    SseEmitter generateCulturallyResponsiveInstructionStream(String unitId, String planId, String lessonId);

    /**
     * 生成文化响应式教学内容
     *
     * @param request 请求内容
     * @return 文化响应式教学内容
     */
    SseEmitter generateCulturallyResponsiveInstructionStream(GenerateCulturallyResponsiveInstructionRequest request);

    /**
     * 生成文化响应式教学分组内容
     *
     * @param unitId   单元 ID
     * @param planId   周计划 ID
     * @param lessonId 课程 ID
     * @return 文化响应式教学内容
     */
    SseEmitter generateCulturallyResponsiveInstructionGroupStream(String unitId, String planId, String lessonId, String groupId);

    /**
     * 生成班级定制话的文化响应式教学分组内容
     *
     * @param generateLessonCLRGroupRequest 请求内容
     * @return 班级定制话的文化响应式教学分组内容
     */
    SseEmitter generateCulturallyResponsiveInstructionGroupStream(GenerateLessonCLRGroupRequest generateLessonCLRGroupRequest);

    /**
     * 生成文化响应式教学内容
     *
     * @param unitId   单元 ID
     * @param planId   周计划 ID
     * @param lessonId 课程 ID
     * @return 文化响应式教学内容
     */
    GenerateCulturallyResponsiveInstructionResponse generateCulturallyResponsiveInstruction(String unitId, String planId, String lessonId);

    /**
     * 生成文化响应式教学内容
     *
     * @param request 请求内容
     * @return 文化响应式教学内容
     */
    GenerateCulturallyResponsiveInstructionResponse generateCulturallyResponsiveInstruction(GenerateCulturallyResponsiveInstructionRequest request);

    /**
     * 评估文化响应式教学内容
     *
     * @param promptUsageRecordId Prompt 使用记录 ID
     * @return 评估结果
     */
    EvaluatePromptResponse evaluateCulturallyResponsiveInstruction(String promptUsageRecordId);

    /**
     * 生成家庭活动内容
     *
     * @param unitId   单元 ID
     * @param planId   周计划 ID
     * @param lessonId 课程 ID
     * @return 家庭活动内容
     */
    SseEmitter generateHomeActivityStream(String unitId, String planId, String lessonId);


    /**
     * 生成 Curriculum 家庭活动内容
     * @param request
     * @return
     */
    SseEmitter generateCurriculumHomeActivity(GenerateHomeActivityRequest request);

    /**
     * 生成家庭活动内容
     *
     * @param unitId   单元 ID
     * @param planId   周计划 ID
     * @param lessonId 课程 ID
     * @return 家庭活动内容
     */
    GenerateHomeActivityResponse generateHomeActivity(String unitId, String planId, String lessonId);

    /**
     * 生成课程家庭活动领域
     *
     * @param frameworkId  框架 ID
     * @param homeActivity 原家庭活动
     * @return 家庭活动
     */
    List<HomeActivityModel> generateHomeActivityDomain(String frameworkId, String homeActivity);

    /**
     * 生成家庭活动内容
     *
     * @param request 请求内容
     * @return 家庭活动内容
     */
    GenerateHomeActivityResponse generateHomeActivity(GenerateHomeActivityRequest request);

    /**
     * 评估家庭活动内容
     *
     * @param promptUsageRecordId Prompt 使用记录 ID
     * @return 评估结果
     */
    EvaluatePromptResponse evaluateHomeActivity(String promptUsageRecordId);

    SseEmitter generateObservationAssessmentStream(ObservationCopilotRequest request);

    SseEmitter generateObservationAssessmentStreamV2(ObservationCopilotRequest request);

    /**
     * 为不同的教师生成差异化学习分组内容
     *
     * @param generateUniversalDesignForLearningRequest 为不同的教师生成差异化学习分组内容请求体
     * @return 差异化学习内容
     */
    SseEmitter generateUniversalDesignForLearningGroupV1(@RequestBody GenerateLessonUniversalDesignForLearningRequest generateUniversalDesignForLearningRequest);

    /**
     * 为不同的教师生成差异化学习分组内容
     *
     * @param generateUniversalDesignForLearningRequest 为不同的教师生成差异化学习分组内容请求体
     * @return 差异化学习内容
     */
    SseEmitter generateUniversalDesignForLearningGroupV2(@RequestBody GenerateLessonUniversalDesignForLearningRequest generateUniversalDesignForLearningRequest);


    /**
     * 创建批量生成课程任务
     *
     * @param request 请求内容
     * @return 操作结果
     */
    SuccessResponse createGenerateLessonTasks(CreateGenerateLessonTasksRequest request);

    /**
     * 查询批量生成课程任务
     *
     * @param batchId 批量 ID
     * @return 任务详情
     */
    GetBatchGenerateLessonTaskResponse getBatchGenerateLessonTasks(String batchId);

    /**
     * 查询批量生成课程任务列表
     *
     * @param taskIds 任务 ID 列表
     * @return 操作结果
     */
    SuccessResponse batchGenerateLesson(String taskIds);

    /**
     * 停止批量生成课程任务
     *
     * @param taskIds 任务 ID 列表
     * @return 操作结果
     */
    SuccessResponse stopGenerateLessonTasks(String taskIds);

    /**
     * 生成课程详情
     *
     * @param taskId 任务 ID
     */
    void generateLesson(String taskId);

    /**
     * 获取 Unit 正在生成课程的任务
     *
     * @param unitId 单元 ID
     * @return 结果
     */
    SuccessResponse getUnitGeneratingLessonTask(String unitId);

    void adaptLesson(String taskId);


    /**
     * 为 userId 对应的用户生成课程详情文档
     *
     * @param userId       用户 ID
     * @param lessonId     课程 ID
     * @param scopeKey     授权的 key
     * @param downloadType
     * @param fileName
     * @param langCode     下载的文档内容的语言类型的语言码
     * @return {@link String} 课程详情文档 URL
     */
    DownFileResponse generateLessonDetailDoc(String userId, String lessonId, String mappedFrameworkId, String scopeKey, String downloadType, String fileName, boolean showClrSource, String langCode, Map<String, Boolean> showImpStepSourceMap, String showMappedTypicalBehaviors) throws IOException;

    /**
     * 为 userId 对应的用户生成课程详情文档
     *
     * @param userId       用户 ID
     * @param lessonId     课程 ID
     * @param scopeKey     授权的 key
     * @param downloadType
     * @param fileName
     * @param langCode     下载的文档内容的语言类型的语言码
     * @return {@link String} 课程详情文档 URL
     */
    DownFileResponse generateLessonDetailDoc(String userId, String lessonId, String mappedFrameworkId, String scopeKey, String downloadType, String fileName, boolean showClrSource, String langCode, Map<String, Boolean> showImpStepSourceMap, String realPathName, Boolean isServer, String showMappedTypicalBehaviors) throws IOException;

    /**
     * 根据单元数据生成 DEI 最佳实践内容
     *
     * @param request       请求内容
     * @return DEI 最佳实践内容
     */
    SseEmitter generateDeiByUnitStream(GenerateDeiBestPracticeRequest request);

    /**
     * 根据 CLR 生成资源数据
     *
     * @param request 请求内容
     * @return 资源数据
     */
    LessonSourceResponse generateResourcesByCLR(GenerateResourcesByCLRRequest request);

    /**
     * 生成测评点映射的 PDF
     *
     * @param request 请求
     * @return {@link SuccessResponse}
     */
    SuccessResponse generateCoreEvaluationPointPDF(BatchCurriculumPdfRequest request);

    /**
     * 获取课程详情
     * @param lessonId 课程 id
     * @param langCode 语言类型码，不为空时会翻译课程详情进行返回
     * @param isDetect 是否识别内容的语言类型，不为空时将会进行识别
     * @return 课程详情
     */
    LessonDetailResponse getLessonDetailWithTranslation(String lessonId, String langCode, Boolean isDetect);


    /**
     * 根据实施步骤生成资源数据
     *
     * @param request 请求内容
     * @return 资源数据
     */
    LessonImpStepSourceResponse generateResourcesByImpStep(GenerateResourcesByImpStepRequest request);

    /**
     * 根据课程所在年龄组生成混合年龄组的内容
     *
     * @param request 请求
     * @return {@link SseEmitter }
     */
    SseEmitter generateLessonMixedAgeGroupStream(GenerateLessonMixedAgeGroupRequest request);

    /**
     * 保存混合年龄组开关
     *
     * @param request 请求
     * @return {@link SuccessResponse }
     */
    SuccessResponse saveOpenMixedAgeGroup(SaveMixedAgeGroupMetaRequest request);


    /**
     * 获取混合年龄组开关
     *
     * @return {@link SuccessResponse }
     */
    SuccessResponse getOpenMixedAgeGroup();

    /**
     * 获取混合年龄组标签
     *
     * @param request  请求
     * @return {@link SuccessResponse }
     */
    SuccessResponse getShowMixedAgeGroupData(GetShowMixedAgeGroupDataRequest request);

    /**
     * 校验 AI 生成的书内容，过滤调无效的书
     *
     * @param booksContent 书内容
     * @return 书名列表
     */
    String checkAIBooks(String booksContent);

    /**
     * 校验 AI 生成的书内容，过滤调无效的书
     *
     * @param books 书内容
     * @return 书名列表
     */
    Map<String, String> checkAIBooks(List<String> books);

    /**
     * 根据课程 idea 推荐课程模版
     *
     * @param request 请求内容
     * @return 课程详情
     */
    RecommendLessonTemplateResponse recommendLessonTemplate(RecommendLessonTemplateRequest request);

    /**
     * 通过 Lesson Type 和 LessonId 以及 Lesson 的数据生成对应的 Google Slides 内容
     *
     * @param request 请求
     * @return {@link SuccessResponse }
     */
    LessonTemplateModel createLessonGoogleSlide(CreateLessonGoogleSlideRequest request) throws Exception;

    /**
     * 通过 LessonId 以及 Lesson 的数据生成对应的 Google Slides 内容
     *
     * @param request 请求
     * @return {@link SuccessResponse }
     */
    CreateLessonGoogleSlidesResponse createLessonSlides(CreateLessonGoogleSlidesRequest request) throws Exception;

    /**
     * 上传课程 slides
     *
     * @param request 请求
     * @return {@link UploadLessonSlidesResponse }
     */
    UploadLessonSlidesResponse uploadLessonSlides(UploadLessonSlidesRequest request) throws Exception;

    /**
     * 将创建出来的 PPT 保存到用户的 Google Drive 中
     *
     * @param request 请求
     * @return {@link SuccessResponse }
     */
    LessonTemplateModel saveLessonGoogleSlide(SaveLessonGoogleSlideRequest request) throws Exception;

    /**
     * 获取课程模版列表
     *
     * @param lessonId 课程 ID
     * @param ageGroup 年龄组
     * @return 课程模版列表
     */
    GetLessonTemplatesResponse getLessonTemplates(String lessonId, String ageGroup);

    /**
     * 删除课程模板
     *
     * @param id 模板 ID
     * @return 操作成功响应
     */
    SuccessResponse deleteLessonTemplate(String id);

    /**
     * 获取课程幻灯片
     *
     * @param lessonId 课程 ID
     * @return 课程幻灯片
     */
    GetLessonSlidesResponse getLessonSlides(String lessonId);

    /**
     * 生成课程幻灯片
     *
     * @param taskId 任务 ID
     * @return 操作成功响应
     */
    SuccessResponse generateLessonSlides(String taskId);

    /**
     * 保存课程幻灯片到用户的 Google Drive
     *
     * @param request 保存请求
     * @return 保存结果
     * @throws Exception 保存异常
     */
    DownFileResponse saveLessonSlidesToDrive(SaveLessonSlidesToDriveRequest request) throws Exception;

    /**
     * 删除临时课程幻灯片
     *
     * @param id 幻灯片项目 ID
     * @return 操作成功响应
     */
    SuccessResponse deleteTemplateSlides(String id) throws IOException;

    /**
     * 校验是否可以改编课程
     *
     * @param request 请求内容
     * @return {@link CheckCanContinueAdaptResponse}
     */
    CheckCanContinueAdaptResponse checkCanContinueAdapt(CheckCanContinueAdaptRequest request);

    /**
     * @param frameworkId 框架 ID
     * @return 课程映射
     */
    GetFrameworkMeasureMapResponse getFrameworkMeasureMap(String frameworkId);

    /**
     * @param frameworkId 框架 ID
     * @param mappedFrameworkId 要映射的框架的 ID
     * @return 课程映射
     */
    GetFrameworkMeasureMapResponse getFrameworkMeasureMap(String frameworkId, String mappedFrameworkId);

    /**
     * 同步 Google Slides 数据
     * @param taskId 任务 ID
     */
    void syncSlidesData(String taskId);

    /**
     * 更新要生成 Google Slides 的 Metadata
     *
     * @param request 请求
     * @return {@link CreateLessonGoogleSlidesResponse}
     */
    CreateLessonGoogleSlidesResponse updateLessonSlides(UpdateLessonGoogleSlidesRequest request) throws Exception;

    /**
     * 更新要生成 Google Slides 的 Metadata
     *
     * @param request 请求
     * @return {@link LessonTemplateModel}
     */
    LessonTemplateModel updateLessonGoogleSlideMetadata(CreateLessonGoogleSlideRequest request) throws Exception;

    /**
     * 检查课程内容与改编想法中是否包含书
     *
     * @param {@link CheckLessonContainBookRequest} 请求
     * @return 检查结果
     */
    SuccessResponse checkLessonContainBooks(CheckLessonContainBookRequest request);

    /**
     * 生成自定义课程模板流式接口
     *
     * @param request
     * @return {@link SseEmitter }
     */
    SseEmitter generateCustomLessonTemplateStream(GenerateCustomLessonTemplateRequest request);

    /**
     * 检查单元内容和学科/测评点是否匹配
     *
     * @param request 单元数据
     * @return 检测结果
     */
    CheckDomainConformOfUnitResponse checkDomainConformOfUnitByAI(CheckDomainConformOfUnitRequest request);

    /**
     * 检查单元周数是否合理
     *
     * @param unitId 单元 ID
     * @return 检测结果
     */
    CheckUnitWeekNumberResponse checkUnitWeekNumberByAI(String unitId);

    /**
     * 检查单元每周的课程数是否合理是否合理
     *
     * @param request 单元周计划数据
     * @return 检测结果
     */
    CheckWeekLessonsNumberResponse checkWeekLessonsNumberByAI(CheckWeekLessonsNumberRequest request);

    /**
     * 获取课程历史版本列表
     * @param lessonId 课程ID
     * @param page 页码，从1开始
     * @param pageSize 每页大小
     * @return 历史版本响应，包含版本列表和分页信息
     */
    LessonVersionResponse getLessonVersions(String lessonId, int page, int pageSize);

    /**
     * 获取指定版本的课程详情
     * @param versionId 版本ID
     * @return 课程详情
     */
    LessonDetailResponse getLessonByVersion(String versionId);

    /**
     * 批量创建课程版本
     *
     * @param lessonIds 课程 ID 列表
     */
    void batchCreateLessonVersionByLessonIds(List<String> lessonIds);

    /**
     * 编辑课程资源
     *
     * @param request 资源编辑请求
     * @return 实施步骤和资源响应
     */
    LessonResourceResponse updateResource(LessonResourceRequest request);

    /**
     * 获取历史资源版本
     *
     * @param lessonId 课程 Id
     * @param type 资源类型
     * @return 课程资源历史
     */
    LessonResourceHistoryResponse getResourceHistory(String lessonId, String type);

    /**
     * 删除资源历史
     *
     * @param lessonId 课程 Id
     * @return 课程资源
     */
    SuccessResponse clearResourceHistory(String lessonId);

    /**
     * 设置课程目标类型
     *
     * @param objectiveType 目标类型
     * @return 操作成功响应
     */
    SuccessResponse setLessonObjectiveType(String objectiveType);

    /**
     * 获取课程目标类型
     *
     * @return 课程目标类型响应
     */
    LessonObjectiveTypeResponse getLessonObjectivesType();

    /**
     * 获取课程锚文本内容
     *
     * @param url 页面 URL
     * @return 锚文本响应，如果不存在则返回 null
     */
    LessonAnchorTextEntity getLessonAnchorText(String url);
}
