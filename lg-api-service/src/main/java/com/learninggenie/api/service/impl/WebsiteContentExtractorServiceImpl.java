package com.learninggenie.api.service.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.learninggenie.common.data.entity.lesson2.LessonAnchorTextEntity;
import com.learninggenie.common.data.model.ArticleContent;
import com.learninggenie.common.data.model.ResultPojo;
import com.learninggenie.common.utils.JsonUtil;
import com.learninggenie.common.utils.RestApiUtil;
import com.learninggenie.common.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.UUID;

@Service
@Slf4j
public class WebsiteContentExtractorServiceImpl implements WebsiteContentExtractorService {

    @Autowired
    @Qualifier("customRestTemplateTimeoutFiveMinute")
    private RestTemplate restTemplate;

    @Value("${diffbot.api.token:********************************}")
    private String diffbotToken;

    @Value("${diffbot.api.url:https://api.diffbot.com/v3/article}")
    private String diffbotApiUrl;

    @Override
    public ArticleContent extractArticle(String url) {
        if (StringUtil.isEmptyOrBlank(url)) {
            return null;
        }
        String requestUrl = buildRequestUrl(url);
        // 调用 Diffbot API
        ResultPojo resultPojo = RestApiUtil.get(restTemplate, requestUrl, null, null);
        String data = resultPojo.getData();
        return JsonUtil.fromJson(data, ArticleContent.class);
    }

    private String buildRequestUrl(String url) {
        try {
            return diffbotApiUrl + "?token=" + diffbotToken +
                    "&url=" + URLEncoder.encode(url, StandardCharsets.UTF_8.toString());
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
    }
}