package com.learninggenie.api.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.learninggenie.common.data.model.ArticleContent;
import com.learninggenie.common.data.model.ResultPojo;
import com.learninggenie.common.utils.JsonUtil;
import com.learninggenie.common.utils.RestApiUtil;
import com.learninggenie.common.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.List;

@Service
@Slf4j
public class WebsiteContentExtractorServiceImpl implements WebsiteContentExtractorService {

    @Autowired
    @Qualifier("customRestTemplateTimeoutFiveMinute")
    private RestTemplate restTemplate;

    @Value("${diffbot.api.token:********************************}")
    private String diffbotToken;

    @Value("${diffbot.api.url:https://api.diffbot.com/v3}")
    private String diffbotApiUrl;

    @Override
    public ArticleContent extractArticle(String url) {
        if (StringUtil.isEmptyOrBlank(url)) {
            return null;
        }
        String requestUrl = UriComponentsBuilder
                .fromHttpUrl(diffbotApiUrl + "/analyze")
                .queryParam("token", diffbotToken)
                .queryParam("url", url)
                .build()
                .toUriString();
        // 调用 Diffbot API
        ResultPojo resultPojo = RestApiUtil.get(restTemplate, requestUrl, null, null);
        String data = resultPojo.getData();
        // 没有内容直接返回 null
        if (StringUtil.isEmptyOrBlank(data)) {
            return null;
        }
        JSONObject jsonObject = new JSONObject(data);
        String type = jsonObject.optString("type");
        if (!type.equalsIgnoreCase("article")) {
            return null;
        }
        String contentListJson = jsonObject.optString("objects");
        // 将 JSON 字符串转为 ArticleContent 列表
        List<ArticleContent> contentList = JsonUtil.readValue(contentListJson, new TypeReference<List<ArticleContent>>() {
        });
        // 返回第一个 ArticleContent，如果没有则返回 null
        return CollectionUtils.isEmpty(contentList) ? null : contentList.get(0);
    }
}