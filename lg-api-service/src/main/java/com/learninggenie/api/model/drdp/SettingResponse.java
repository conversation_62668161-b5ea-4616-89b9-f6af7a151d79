package com.learninggenie.api.model.drdp;

import com.learninggenie.common.data.model.drdp.setting.DRDPSetting;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.List;

public class SettingResponse {
    @ApiModelProperty(value = "机构 DRDP 上传设置列表")
    private List<DRDPSetting> drdpSettings;

    public SettingResponse() {
        this.drdpSettings = new ArrayList<>();
    }

    public List<DRDPSetting> getDrdpSettings() {
        return drdpSettings;
    }

    public void setDrdpSettings(List<DRDPSetting> drdpSettings) {
        this.drdpSettings = drdpSettings;
    }
}
