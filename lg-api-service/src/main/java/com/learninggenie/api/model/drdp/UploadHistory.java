package com.learninggenie.api.model.drdp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class UploadHistory {
    @ApiModelProperty(value = "上传记录id")
    private String id;

    @ApiModelProperty(value = "上传时间")
    private String uploadTime;

    @ApiModelProperty(value = "上传类型")
    private String uploadType;

    @ApiModelProperty(value = "周期别名")
    private String periodAlias;

    @ApiModelProperty(value = "框架")
    private String framework;

    @ApiModelProperty(value = "学校")
    private String site;

    @ApiModelProperty(value = "班级")
    private String classes;

    @ApiModelProperty(value = "需要上传数")
    private String needUpload;

    @ApiModelProperty(value = "已上传数")
    private String uploaded;

    @ApiModelProperty(value = "状态")
    private String status;

    @ApiModelProperty(value = "修复数")
    private String reUpload;

    @ApiModelProperty(value = "上传结果")
    private List<UploadHistory> uploadHistories;

    public UploadHistory() {
        this.uploadHistories = new ArrayList<>();
    }
}
