package com.learninggenie.api.model.importdata;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertEquals;

/**
 * 导入列值自定义配置的测试类
 */
@RunWith(MockitoJUnitRunner.class)
public class ImportColumnValueCustomConfigTest {

    @InjectMocks
    private ImportColumnValueCustomConfig importColumnValueCustomConfig;

    /**
     * 测试解析值方法
     */
    @Test
    public void testParseValue() {
        // 数据准备
        List<ImportColumnValueCustomRule> rules = new ArrayList<>();
        ImportColumnValueCustomRule rule1 = new ImportColumnValueCustomRule();
        rule1.setColumnName("columnName1");
        rule1.setBlankValueResult("blankValueResult1");
        rule1.setDefaultUnknownValueResult("defaultUnknownValueResult1");
        List<ValuesResultMapping> valuesResultMappings = new ArrayList<>();
        ValuesResultMapping valuesResultMapping1 = new ValuesResultMapping();
        List<String> values1 = new ArrayList<>();
        values1.add("value1");
        valuesResultMapping1.setValues(values1);
        valuesResultMapping1.setResult("mappedValue1");
        ValuesResultMapping valuesResultMapping2 = new ValuesResultMapping();
        List<String> values2 = new ArrayList<>();
        values2.add("value2");
        values2.add("value3");
        valuesResultMapping2.setValues(values2);
        valuesResultMapping2.setResult("mappedValue2");
        valuesResultMappings.add(valuesResultMapping1);
        valuesResultMappings.add(valuesResultMapping2);
        rule1.setValuesResultMappings(valuesResultMappings);
        rules.add(rule1);
        ImportColumnValueCustomRule rule2 = new ImportColumnValueCustomRule();
        rule2.setColumnName("columnName2");
        rule2.setBlankValueResult("blankValueResult2");
        rule2.setDefaultUnknownValueResult("defaultUnknownValueResult2");
        ValuesResultMapping valuesResultMapping3 = new ValuesResultMapping();
        List<String> values3 = new ArrayList<>();
        values3.add("value4");
        values3.add("value5");
        valuesResultMapping3.setValues(values3);
        valuesResultMapping3.setResult("mappedValue3");
        valuesResultMappings = new ArrayList<>();
        valuesResultMappings.add(valuesResultMapping3);
        rule2.setValuesResultMappings(valuesResultMappings);
        rules.add(rule2);
        importColumnValueCustomConfig.setRules(rules);
        String name = "columnName1";
        String value = "";

        // 调用测试方法
        String result = importColumnValueCustomConfig.parseValue(name, value);
        // 验证结果
        assertEquals("blankValueResult1", result); // 验证输入 value 为空的情况
        name = "columnName2";
        value = null;
        result = importColumnValueCustomConfig.parseValue(name, value);
        // 验证结果
        assertEquals("blankValueResult2", result); // 验证输入 value 为 null 的情况
        name = "columnName3";
        value = "value3";
        result = importColumnValueCustomConfig.parseValue(name, value);
        // 验证结果
        assertEquals("value3", result); // 验证输入 name 不存在的情况
        name = "";
        value = "value4";
        result = importColumnValueCustomConfig.parseValue(name, value);
        // 验证结果
        assertEquals("value4", result); // 验证输入 name 为空的情况
        name = null;
        value = "value4";
        result = importColumnValueCustomConfig.parseValue(name, value);
        // 验证结果
        assertEquals("value4", result); // 验证输入 name 为 null 的情况
        name = "columnName1";
        value = "value5";
        result = importColumnValueCustomConfig.parseValue(name, value);
        // 验证结果
        assertEquals("defaultUnknownValueResult1", result); // 验证输入 value 没有成功匹配的情况
        // 验证匹配上的映射值
        name = "columnName1";
        value = "value1";
        result = importColumnValueCustomConfig.parseValue(name, value);
        // 验证结果
        assertEquals("mappedValue1", result); // 验证输入 value 成功匹配的情况
        // 验证匹配上的映射值
        name = "columnName1";
        value = "VALUE2";
        result = importColumnValueCustomConfig.parseValue(name, value);
        // 验证结果
        assertEquals("mappedValue2", result); // 验证输入 value 为大写情况成功匹配的情况
        // 验证匹配上的映射值
        name = "columnName2";
        value = " value4 ";
        result = importColumnValueCustomConfig.parseValue(name, value);
        // 验证结果
        assertEquals("mappedValue3", result); // 验证输入 value 存在前后空格成功匹配的情况
    }

}
