package com.learninggenie.api.model.importdata;

import com.learninggenie.common.data.entity.ImportLogEntity;
import com.learninggenie.common.data.enums.Gender;
import com.learninggenie.common.data.enums.importLog.ImportLogOrigin;
import com.learninggenie.common.data.enums.importLog.ImportLogType;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

/**
 * 导入功能中,导入学生的测试类
 */
@RunWith(MockitoJUnitRunner.class)
public class ImportChildTest {
    @InjectMocks
    private ImportChild ImportChild;

    @Test
    public void testCreateCommandForCreateType() {
        // 数据准备
        ImportChild importChild = new ImportChild();
        // 设置小孩 first name
        importChild.setFirstName("firstName");
        // 设置小孩 last name
        importChild.setLastName("lastName");
        // 设置小孩 middle name
        importChild.setMiddleName("middleName");
        // 设置小孩 display name
        importChild.setDisplayName("displayName");
        importChild.setGroupId("groupId");
        importChild.setGroupName("groupName");
        importChild.setCenterName("centerName");
        importChild.setGender(Gender.NONBINARY.toString());
        ImportChild importChild01 = new ImportChild();
        importChild01.setGender(Gender.NONBINARY.toString());
        importChild01.setGroupId("groupId");
        importChild.setImportChild(importChild01);
        // 定义 jobId
        String jobId = "JobId";
        ImportLogEntity importChildCommand = importChild.createCommand(ImportLogType.IN, ImportLogOrigin.Child, jobId);
        // 验证
        // 验证返回的 ImportLogEntity 对象的属性值是否符合预期
        assertNotNull(importChildCommand.getId());
        assertEquals(jobId, importChildCommand.getJobId());
        assertNotNull(importChildCommand.getExecute());
        assertNotNull(importChildCommand.getRollBack());

        // 验证 IN 的属性值是否符合预期
        ImportLogEntity importChildCommand01 = importChild.createCommand(ImportLogType.Update, ImportLogOrigin.Child, jobId);
        assertNotNull(importChildCommand01.getId());
        assertEquals(jobId, importChildCommand01.getJobId());
        assertNotNull(importChildCommand01.getExecute());
        assertNotNull(importChildCommand01.getRollBack());
    }
}
