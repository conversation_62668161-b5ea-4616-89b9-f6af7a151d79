package com.learninggenie.api.model.importdata;

import com.learninggenie.common.data.entity.ImportLogEntity;
import com.learninggenie.common.data.enums.importLog.ImportLogOrigin;
import com.learninggenie.common.data.enums.importLog.ImportLogType;
import com.learninggenie.common.data.enums.importLog.ImportMark;
import com.learninggenie.common.utils.TimeUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.UUID;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

/**
 * 导入功能中,导入学校的测试类
 *
 * <AUTHOR>
 * @date 2023/11/08
 */
@RunWith(MockitoJUnitRunner.class)
public class ImportCenterTest {

    @InjectMocks
    private ImportCenter importCenter;

    /**
     * 使用 Create 类型,测试创建学校的 Command
     */
    @Test
    public void testCreateCommandForCreateType() {
        // 创建一个 Mock 对象
        // 设置 CacheKey
        importCenter.setCacheKey("CacheKey");
        // 设置 ImportMark
        importCenter.setType(ImportMark.NEW_CENTER);
        // 设置 ImportCenter 的 CenterId
        String importCenterId = UUID.randomUUID().toString();
        importCenter.setId(importCenterId);
        // 设置 ImportCenter 的 jobId
        String jobId = "JobId";
        importCenter.setJobId(jobId);
        // 设置 ImportCenter 的 UserId
        importCenter.setUserId("UserId");
        // 设置 ImportCenter 的 AgencyId
        importCenter.setAgencyId("AgencyId");
        // 设置 ImportCenter 的 Center Name
        importCenter.setName("Name");
        // 设置 ImportCenter 的 时区
        importCenter.setTimeZone("Asia/Shanghai");
        // 设置 ImportCenter 的 时区偏移量
        importCenter.setSendTime("SendTime");
        // 设置 ImportCenter 的 创建时间, 按照 UTC 时间
        importCenter.setCreatedUtc(TimeUtil.getUtcNowStr());
        // 设置 ImportCenter 中的班级
        importCenter.setGroups(new ArrayList<>());
        // 设置 ImportCenter 中的教师
        importCenter.setTeachers(new ArrayList<>());
        // 设置 ImportCenter NoRight 为 false
        importCenter.setNoRight(false);
        // 设置 ImportCenter 的 SourcedId
        importCenter.setSourcedId("SourcedId");
        // 设置 ImportCenter 的 最后修改时间
        importCenter.setDateLastModified(TimeUtil.getUtcNow());
        // 设置 ImportCenter 的 SourcedStatus
        importCenter.setSourcedStatus("SourcedStatus");
        // 设置 ImportCenter 的 RosterType
        importCenter.setRosterType("RosterType");

        // 调用 createCommand 方法
        ImportLogEntity importCenterCommand = importCenter.createCommand(ImportLogType.Create, ImportLogOrigin.Group, jobId);

        // 验证返回的 ImportLogEntity 对象的属性值是否符合预期
        assertNotNull(importCenterCommand.getId());
        assertEquals(jobId, importCenterCommand.getJobId());
        assertNotNull(importCenterCommand.getExecute());
        assertNotNull(importCenterCommand.getRollBack());
    }

    /**
     * 使用 Update 类型,测试创建学校的 Command
     */
    @Test
    public void testCreateCommandForUpdateType() {
        // 创建一个 Mock 对象
        // 设置 CacheKey
        importCenter.setCacheKey("CacheKey");
        // 设置 ImportMark
        importCenter.setType(ImportMark.UPDATE_PARENT);
        // 设置 ImportCenter 的 CenterId
        String importCenterId = UUID.randomUUID().toString();
        importCenter.setId(importCenterId);
        // 设置 ImportCenter 的 jobId
        String jobId = "JobId";
        importCenter.setJobId(jobId);
        // 设置 ImportCenter 的 UserId
        importCenter.setUserId("UserId");
        // 设置 ImportCenter 的 AgencyId
        importCenter.setAgencyId("AgencyId");
        // 设置 ImportCenter 的 Center Name
        importCenter.setName("Name");
        // 设置 ImportCenter 的 时区
        importCenter.setTimeZone("Asia/Shanghai");
        // 设置 ImportCenter 的 时区偏移量
        importCenter.setSendTime("SendTime");
        // 设置 ImportCenter 的 创建时间, 按照 UTC 时间
        importCenter.setCreatedUtc(TimeUtil.getUtcNowStr());
        // 设置 ImportCenter 中的班级
        importCenter.setGroups(new ArrayList<>());
        // 设置 ImportCenter 中的教师
        importCenter.setTeachers(new ArrayList<>());
        // 设置 ImportCenter NoRight 为 false
        importCenter.setNoRight(false);
        // 设置 ImportCenter 的 SourcedId
        importCenter.setSourcedId("SourcedId");
        // 设置 ImportCenter 的 最后修改时间
        importCenter.setDateLastModified(TimeUtil.getUtcNow());
        // 设置 ImportCenter 的 SourcedStatus
        importCenter.setSourcedStatus("SourcedStatus");
        // 设置 ImportCenter 的 RosterType
        importCenter.setRosterType("RosterType");

        // 模拟方法调用并设置返回值
        // 调用 createCommand 方法
        ImportLogEntity importCenterCommand = importCenter.createCommand(ImportLogType.Update, ImportLogOrigin.Group, jobId);

        // 验证返回的 ImportLogEntity 对象的属性值是否符合预期
        assertNotNull(importCenterCommand.getId());
        assertEquals(jobId, importCenterCommand.getJobId());
        assertNotNull(importCenterCommand.getExecute());
        assertNotNull(importCenterCommand.getRollBack());
    }
}
