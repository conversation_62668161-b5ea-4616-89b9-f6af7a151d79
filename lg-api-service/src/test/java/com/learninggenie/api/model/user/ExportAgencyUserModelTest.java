package com.learninggenie.api.model.user;

import com.learninggenie.common.data.enums.user.Role;
import com.learninggenie.common.data.model.UserModel;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.*;

/**
 * 导出机构下用户模型测试类
 */
@RunWith(MockitoJUnitRunner.class)
public class ExportAgencyUserModelTest {

    /**
     * 测试通过 UserModel 模型转换
     */
    @Test
    public void testConvertUserModel() {
        // 用户信息
        UserModel user = new UserModel();
        user.setEmail("<EMAIL>"); // 邮箱
        user.setDisplayName("test-convert-user-model"); // 姓名
        user.setRole(Role.AGENCY_OWNER.toString()); // 角色

        // 转换
        ExportAgencyUserModel exportAgencyUserModel = ExportAgencyUserModel.from(user);

        // 验证
        assertEquals(user.getEmail(), exportAgencyUserModel.getEmail()); // 邮箱
        assertEquals(user.getDisplayName(), exportAgencyUserModel.getUsername()); // 姓名
        assertEquals(Role.AGENCY_OWNER.getDisplayName(), exportAgencyUserModel.getRole()); // 角色
        assertEquals(Role.AGENCY_OWNER.ordinal(), exportAgencyUserModel.getRoleSortIndex()); // 角色排序
    }

}
