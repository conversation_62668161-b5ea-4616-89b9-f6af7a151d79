package com.learninggenie.api.model.importdata;

import com.google.common.collect.Lists;
import com.learninggenie.common.data.entity.ImportLogEntity;
import com.learninggenie.common.data.enums.importLog.ImportLogOrigin;
import com.learninggenie.common.data.enums.importLog.ImportLogType;
import com.learninggenie.common.data.enums.importLog.ImportMark;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Date;
import java.util.UUID;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

/**
 * 导入功能中,导入老师的测试类
 *
 * <AUTHOR>
 * @date 2023/11/08
 */
@RunWith(MockitoJUnitRunner.class)
public class ImportTeacherTest {

    @InjectMocks
    private ImportTeacher importTeacher;

    /**
     * 使用 Create 类型,测试创建学校的 Command
     */
    @Test
    public void testCreateCommandForCreateType() {
        // 创建一个 Mock 对象
        // 设置删除的班级 id 的集合
        importTeacher.setDeleteGroupIds(Lists.newArrayList());
        // 设置密码
        importTeacher.setPasswordHash("Password");
        // 设置在那些班级中任教
        importTeacher.setGroupNames(Lists.newArrayList());
        // 设置老师的名字
        importTeacher.setDisplayName("Teacher Name");
        // 设置导入的类型
        importTeacher.setType(ImportMark.NEW_TEACHER);
        // 设置邀请状态为为邀请
        importTeacher.setInvite(false);
        // 设置任教的班级 id 的集合
        importTeacher.setGroupIds(Lists.newArrayList());
        // 设置老师的 id
        String teacherId = UUID.randomUUID().toString();
        importTeacher.setId(teacherId);
        // 设置用户的 用户名
        importTeacher.setUserName("UserName");
        // 设置用户的 First Name
        importTeacher.setFirstName("FirstName");
        // 设置用户的 Last Name
        importTeacher.setLastName("LastName");
        // 设置用户的 Email
        importTeacher.setEmail("<EMAIL>");
        // 设置用户的 Phone
        importTeacher.setPhone("");
        // 设置用户的 Password
        importTeacher.setPassword("Password");
        // 设置用户的 邀请 id
        importTeacher.setInvitationId("");
        // 设置哪个用户创建的
        importTeacher.setCreateUserId("");
        // 设置用户的 Source Id
        importTeacher.setSourcedId("SourceId");
        // 设置用户的 最后修改时间
        importTeacher.setDateLastModified(new Date());
        // 设置用户的 Sourced 状态
        importTeacher.setSourcedStatus("SourcedStatus");
        // 设置用户的 Sourced Group Ids
        importTeacher.setSourcedGroupIds(Lists.newArrayList());
        // 定义 jobId
        String jobId = "JobId";

        // 调用 createCommand 方法
        ImportLogEntity importTeacherCommand = importTeacher.createCommand(ImportLogType.Create, ImportLogOrigin.Group, jobId);

        // 验证返回的 ImportLogEntity 对象的属性值是否符合预期
        assertNotNull(importTeacherCommand.getId());
        assertEquals(jobId, importTeacherCommand.getJobId());
        assertNotNull(importTeacherCommand.getExecute());
        assertNotNull(importTeacherCommand.getRollBack());
    }

    /**
     * 使用 Update 类型,测试创建学校的 Command
     */
    @Test
    public void testCreateCommandForUpdateType() {
        // 创建一个 Mock 对象
        // 设置删除的班级 id 的集合
        importTeacher.setDeleteGroupIds(Lists.newArrayList());
        // 设置密码
        importTeacher.setPasswordHash("Password");
        // 设置在那些班级中任教
        importTeacher.setGroupNames(Lists.newArrayList());
        // 设置老师的名字
        importTeacher.setDisplayName("Teacher Name");
        // 设置导入的类型
        importTeacher.setType(ImportMark.NEW_TEACHER);
        // 设置邀请状态为为邀请
        importTeacher.setInvite(false);
        // 设置任教的班级 id 的集合
        importTeacher.setGroupIds(Lists.newArrayList());
        // 设置老师的 id
        String teacherId = UUID.randomUUID().toString();
        importTeacher.setId(teacherId);
        // 设置用户的 用户名
        importTeacher.setUserName("UserName");
        // 设置用户的 First Name
        importTeacher.setFirstName("FirstName");
        // 设置用户的 Last Name
        importTeacher.setLastName("LastName");
        // 设置用户的 Email
        importTeacher.setEmail("<EMAIL>");
        // 设置用户的 Phone
        importTeacher.setPhone("");
        // 设置用户的 Password
        importTeacher.setPassword("Password");
        // 设置用户的 邀请 id
        importTeacher.setInvitationId("");
        // 设置哪个用户创建的
        importTeacher.setCreateUserId("");
        // 设置用户的 Source Id
        importTeacher.setSourcedId("SourceId");
        // 设置用户的 最后修改时间
        importTeacher.setDateLastModified(new Date());
        // 设置用户的 Sourced 状态
        importTeacher.setSourcedStatus("SourcedStatus");
        // 设置用户的 Sourced Group Ids
        importTeacher.setSourcedGroupIds(Lists.newArrayList());
        // 定义 jobId
        String jobId = "JobId";

        // 模拟方法调用并设置返回值
        // 调用 createCommand 方法
        ImportLogEntity importTeacherCommand = importTeacher.createCommand(ImportLogType.Update, ImportLogOrigin.Group, jobId);

        // 验证返回的 ImportLogEntity 对象的属性值是否符合预期
        assertNotNull(importTeacherCommand.getId());
        assertEquals(jobId, importTeacherCommand.getJobId());
        assertNotNull(importTeacherCommand.getExecute());
        assertNotNull(importTeacherCommand.getRollBack());
    }
}
