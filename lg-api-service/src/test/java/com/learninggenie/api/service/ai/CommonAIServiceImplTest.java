package com.learninggenie.api.service.ai;

import com.learninggenie.api.model.ai.check.CheckContentRequest;
import com.learninggenie.api.model.ai.check.CheckContentResponse;
import com.learninggenie.api.provider.PromptProvider;
import com.learninggenie.api.service.ai.impl.CommonAIServiceImpl;
import com.learninggenie.api.service.lesson2.LessonService;
import com.learninggenie.common.ai.service.OpenAIService;
import com.learninggenie.common.data.entity.prompt.PromptEntity;
import com.learninggenie.common.data.model.openai.completion.chat.ChatCompletionRequest;
import com.learninggenie.common.data.model.openai.completion.chat.ChatCompletionResult;
import com.theokanning.openai.completion.chat.ChatCompletionChoice;
import com.theokanning.openai.completion.chat.ChatMessage;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class CommonAIServiceImplTest {

	@Mock
	private OpenAIService openAIService;

	@Mock
	private PromptProvider promptProvider;

	@Mock
	private LessonService lessonService;

	@InjectMocks
	private CommonAIServiceImpl commonAIService;


	@Test
	public void testCheckLessonContentByAI_NullRequest() {
		CheckContentResponse response = commonAIService.checkLessonContentByAI(null);
		assertFalse(response.getResult());
	}

	@Test
	public void testCheckLessonContentByAI_InvalidRequest() {
		CheckContentRequest invalidRequest = new CheckContentRequest();
		CheckContentResponse response = commonAIService.checkLessonContentByAI(invalidRequest);
		assertFalse(response.getResult());
	}

	@Test
	public void testCheckLessonContentByAI_ValidRequest() {
		CheckContentRequest validRequest = new CheckContentRequest();
		validRequest.setPrompt("Is this content appropriate?");
		validRequest.setPromptEntity(new PromptEntity());
		ChatCompletionResult mockResult = new ChatCompletionResult();
		List<ChatCompletionChoice> choiceList = new ArrayList<>();
		ChatCompletionChoice choice = new ChatCompletionChoice();
		ChatMessage message = new ChatMessage();
		message.setContent("Yes, the content is appropriate.");
		choice.setMessage(message);
		choiceList.add(choice);
		mockResult.setChoices(choiceList);
		when(openAIService.createChatCompletion(any(ChatCompletionRequest.class))).thenReturn(mockResult);
		// when(lessonService.checkAIBooks(anyList())).thenReturn(new HashMap<>());
		CheckContentResponse response = commonAIService.checkLessonContentByAI(validRequest);

		assertTrue(response.getResult());
		verify(openAIService, times(1)).createChatCompletion(any(ChatCompletionRequest.class));
		verify(promptProvider, times(1)).createPromptUsageRecord(anyString(), any(ChatCompletionResult.class), any(), any(), any(), any());
	}

	@Test
	public void testCheckLessonContentByAI_ValidRequest_NegativeResponse() {
		CheckContentRequest validRequest = new CheckContentRequest();
		validRequest.setPrompt("Is this content appropriate?");
		validRequest.setPromptEntity(new PromptEntity());
		ChatCompletionResult mockResult = new ChatCompletionResult();
		List<ChatCompletionChoice> choiceList = new ArrayList<>();
		ChatCompletionChoice choice = new ChatCompletionChoice();
		ChatMessage message = new ChatMessage();
		message.setContent("No, the content is appropriate.");
		choice.setMessage(message);
		choiceList.add(choice);
		mockResult.setChoices(choiceList);
		when(openAIService.createChatCompletion(any(ChatCompletionRequest.class))).thenReturn(mockResult);

		CheckContentResponse response = commonAIService.checkLessonContentByAI(validRequest);

		assertFalse(response.getResult());
		verify(openAIService, times(1)).createChatCompletion(any(ChatCompletionRequest.class));
		verify(promptProvider, times(1)).createPromptUsageRecord(anyString(), any(ChatCompletionResult.class), any(), any(), any(), any());
	}

	@Test
	public void testCheckLessonContentByAI_EmptyCompletion() {
		CheckContentRequest validRequest = new CheckContentRequest();
		validRequest.setPrompt("Is this content appropriate?");
		validRequest.setPromptEntity(new PromptEntity());
		ChatCompletionResult mockResult = new ChatCompletionResult();
		List<ChatCompletionChoice> choiceList = new ArrayList<>();
		ChatCompletionChoice choice = new ChatCompletionChoice();
		ChatMessage message = new ChatMessage();
		message.setContent(" ");
		choice.setMessage(message);
		choiceList.add(choice);
		mockResult.setChoices(choiceList);
		when(openAIService.createChatCompletion(any(ChatCompletionRequest.class))).thenReturn(mockResult);

		CheckContentResponse response = commonAIService.checkLessonContentByAI(validRequest);

		assertFalse(response.getResult());
		verify(openAIService, times(1)).createChatCompletion(any(ChatCompletionRequest.class));
		verify(promptProvider, times(1)).createPromptUsageRecord(anyString(), any(ChatCompletionResult.class), any(), any(), any(), any());
	}
}