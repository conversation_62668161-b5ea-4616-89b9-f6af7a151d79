package com.learninggenie.api.service.impl.inkind;

import com.learninggenie.api.model.inkind.SubmissionCutoffModel;
import com.learninggenie.api.model.inkind.SubmitActivityCutoffResponse;
import com.learninggenie.api.provider.UserProvider;
import com.learninggenie.api.provider.impl.InKindProviderImpl;
import com.learninggenie.common.cache.CacheService;
import com.learninggenie.common.data.dao.*;
import com.learninggenie.common.data.dao.impl.UserDaoImpl;
import com.learninggenie.common.data.dto.inkind.InKindReportDTO;
import com.learninggenie.common.data.dto.student.StudentDTO;
import com.learninggenie.common.data.entity.*;
import com.learninggenie.common.data.mapper.dynamo.AgencyMetadataMapper;
import com.learninggenie.common.filesystem.FileSystem;
import com.learninggenie.common.utils.TimeUtil;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class InKindProviderImplTest {
    @InjectMocks
    public InKindProviderImpl inKindProvider;

    @Mock
    private InkindDao inkindDao;

    @Mock
    private UserProvider userProvider;

    @Mock
    private CenterDao centerDao;

    @Mock
    private GroupDao groupDao;

    @Mock
    private StudentDao studentDao;

    @Mock
    private UserDaoImpl userDao;

    @Mock
    private FileSystem fileSystem;

    @Mock
    private CacheService cacheService;

    @Mock
    private AgencyDao agencyDao;

    @Mock
    private AgencyMetadataMapper agencyMetadataMapper;

    private static final String AGENCY_ID = "agency_1";

    private static final int SUBMISSION_CUTOFF_DAY = 20;


    @Test
    void testSetSignatureWithEmptyReports() {
        List<InkindReportEntity> reports = Collections.emptyList();

        inKindProvider.setSignature(reports);

        // Verify no interactions with mocks
        assertEquals(0, reports.size());
    }

    @Test
    void testSetSignatureWithNullParentSignatureId() {
        InkindReportEntity reportWithNullParentSignatureId = new InkindReportEntity();
        reportWithNullParentSignatureId.setParentSignatureId(null);
        List<InkindReportEntity> reports = Collections.singletonList(reportWithNullParentSignatureId);

        inKindProvider.setSignature(reports);

        // Verify no interactions with mocks
        assertEquals(1, reports.size());
        assertEquals(null, reports.get(0).getParentSignatureAbsoluteUrl());
    }

    @Test
    void testSetSignatureWithPrivateMediaEntity() {
        InkindReportEntity report = new InkindReportEntity();
        report.setParentSignatureId("123");
        List<InkindReportEntity> reports = Collections.singletonList(report);

        MediaEntity mediaEntity = new MediaEntity();
        mediaEntity.setId("123");
        mediaEntity.setPrivateFile(true);
        mediaEntity.setRelativePath("/private/path");

        when(inkindDao.getMediaByIds(anyList())).thenReturn(Collections.singletonList(mediaEntity));
        when(fileSystem.getPrivateUrl("/private/path")).thenReturn("privateUrl");

        inKindProvider.setSignature(reports);

        assertEquals("privateUrl", report.getParentSignatureAbsoluteUrl());
    }

    @Test
    void testSetSignatureWithPublicMediaEntity() {
        InkindReportEntity report = new InkindReportEntity();
        report.setParentSignatureId("123");
        List<InkindReportEntity> reports = Collections.singletonList(report);

        MediaEntity mediaEntity = new MediaEntity();
        mediaEntity.setId("123");
        mediaEntity.setPrivateFile(false);
        mediaEntity.setRelativePath("/public/path");

        when(inkindDao.getMediaByIds(anyList())).thenReturn(Collections.singletonList(mediaEntity));
        when(fileSystem.getPublicUrl("/public/path")).thenReturn("publicUrl");

        inKindProvider.setSignature(reports);

        assertEquals("publicUrl", report.getParentSignatureAbsoluteUrl());
    }

    @Test
    void testSetSignatureWithNonMatchingMediaEntity() {
        InkindReportEntity report = new InkindReportEntity();
        report.setParentSignatureId("123");
        List<InkindReportEntity> reports = Collections.singletonList(report);

        MediaEntity mediaEntity = new MediaEntity();
        mediaEntity.setId("456");
        mediaEntity.setPrivateFile(true);
        mediaEntity.setRelativePath("/private/path");

        when(inkindDao.getMediaByIds(anyList())).thenReturn(Collections.singletonList(mediaEntity));

        inKindProvider.setSignature(reports);

        assertEquals(null, report.getParentSignatureAbsoluteUrl());
    }

    @Test
    void testSetSignatureWithMultipleReports() {
        InkindReportEntity report1 = new InkindReportEntity();
        report1.setParentSignatureId("123");
        InkindReportEntity report2 = new InkindReportEntity();
        report2.setParentSignatureId("456");
        List<InkindReportEntity> reports = Arrays.asList(report1, report2);

        MediaEntity mediaEntity1 = new MediaEntity();
        mediaEntity1.setId("123");
        mediaEntity1.setPrivateFile(true);
        mediaEntity1.setRelativePath("/private/path1");

        MediaEntity mediaEntity2 = new MediaEntity();
        mediaEntity2.setId("456");
        mediaEntity2.setPrivateFile(false);
        mediaEntity2.setRelativePath("/public/path2");

        when(inkindDao.getMediaByIds(anyList())).thenReturn(Arrays.asList(mediaEntity1, mediaEntity2));
        when(fileSystem.getPrivateUrl("/private/path1")).thenReturn("privateUrl1");
        when(fileSystem.getPublicUrl("/public/path2")).thenReturn("publicUrl2");

        inKindProvider.setSignature(reports);

        assertEquals("privateUrl1", report1.getParentSignatureAbsoluteUrl());
        assertEquals("publicUrl2", report2.getParentSignatureAbsoluteUrl());
    }

    @Test
    public void testGetSubmissionCutoffDaySubmissionCutoffDayOpenIsFalse() {
        UserEntity user = new UserEntity();
        user.setId(UUID.randomUUID().toString());
        // when(reminderParentChildDao.getByUserId(user.getId(), "WEB_INKIND_CUT_OFF_DAY")).thenReturn(new ReminderParentChild());
        when(agencyMetadataMapper.get(anyString(), anyString())).thenReturn(null);
        SubmitActivityCutoffResponse response = inKindProvider.getSubmissionCutoffDay(UUID.randomUUID().toString(), true);
        assertEquals(true, !response.isNeedRemind());
    }


    @Test
    public void testCheckSubmissionCutoffWithinLimit() {
        Date submitDate = new Date();
        SubmitActivityCutoffResponse submissionCutoffDayResponse = new SubmitActivityCutoffResponse();
        submissionCutoffDayResponse.setSubmissionCutoffDayOpen(true);
        submissionCutoffDayResponse.setSubmissionCutoffDay(SUBMISSION_CUTOFF_DAY);

        SubmissionCutoffModel result = inKindProvider.checkSubmissionCutoff(submitDate, AGENCY_ID);

        assertNotNull(result);
        assertFalse(result.isCutoffDayState());
    }

    @Test
    public void testCheckSubmissionCutoffAfterLimit() {
        // Set the submit date to be after the cutoff day
        Date submitDate = new Date(System.currentTimeMillis() + (1000 * 60 * 60 * 24 * SUBMISSION_CUTOFF_DAY));

        SubmitActivityCutoffResponse submissionCutoffDayResponse = new SubmitActivityCutoffResponse();
        submissionCutoffDayResponse.setSubmissionCutoffDayOpen(true);
        submissionCutoffDayResponse.setSubmissionCutoffDay(SUBMISSION_CUTOFF_DAY);

        SubmissionCutoffModel result = inKindProvider.checkSubmissionCutoff(submitDate, AGENCY_ID);

        assertNotNull(result);
        assertFalse(result.isCutoffDayState());
        assertEquals(String.valueOf(-1), String.valueOf(result.getCutoffDay()));
    }

    @Test
    public void testCheckSubmissionCutoffBeforeLimit() {
        // Set the submit date to be before the cutoff day
        Date submitDate = new Date(System.currentTimeMillis() - (1000 * 60 * 60 * 24 * SUBMISSION_CUTOFF_DAY));

        SubmitActivityCutoffResponse submissionCutoffDayResponse = new SubmitActivityCutoffResponse();
        submissionCutoffDayResponse.setSubmissionCutoffDayOpen(true);
        submissionCutoffDayResponse.setSubmissionCutoffDay(SUBMISSION_CUTOFF_DAY);

        SubmissionCutoffModel result = inKindProvider.checkSubmissionCutoff(submitDate, AGENCY_ID);

        assertNotNull(result);
        assertFalse(result.isCutoffDayState());
    }

    @Test
    public void testCheckSubmissionCutoffWhenClosed() {
        // Setup response to simulate the cutoff day feature being closed
        SubmitActivityCutoffResponse submissionCutoffDayResponse = new SubmitActivityCutoffResponse();
        submissionCutoffDayResponse.setSubmissionCutoffDayOpen(false);

        SubmissionCutoffModel result = inKindProvider.checkSubmissionCutoff(new Date(), AGENCY_ID);

        assertNotNull(result);
        // Assuming that when the feature is closed, it should act as if no limit is imposed
        assertFalse(result.isCutoffDayState());
    }


    @Test
    void testBuildAssignmentJobList() {

        List<InkindReportEntity> reports = new ArrayList<>();
        InKindReportDTO reportDTO1 = new InKindReportDTO();
        reportDTO1.setActivityDate(new Date());
        reportDTO1.setEnrollmentId("child_1");
        reportDTO1.setThemeName("themeName");
        reportDTO1.setActivityNumber("activityNumber");
        reportDTO1.setActivityDescription("activityDescription");
        reportDTO1.setSourceName("sourceName");
        reportDTO1.setRateUnit("rateUnit");

        InKindReportDTO reportDTO2 = new InKindReportDTO();
        reportDTO2.setEnrollmentId("child_2");
        reportDTO2.setActivityDate(new Date());
        reportDTO2.setThemeName("themeName");
        reportDTO2.setActivityNumber("activityNumber");
        reportDTO2.setActivityDescription("activityDescription");
        reportDTO2.setSourceName("sourceName");
        reportDTO2.setRateUnit("rateUnit");

        reports.add(reportDTO1);
        reports.add(reportDTO2);

        Date utcNow = TimeUtil.getUtcNow();


        List<AssignEntity> nowAssignArr = new ArrayList<>();
        AssignEntity assignEntity = new AssignEntity();
        assignEntity.setGroupId("group_id");
        assignEntity.setId("assign_id");
        nowAssignArr.add(assignEntity);

        List<AssignTemplateEntity> templates = new ArrayList<>();
        AssignTemplateEntity template1 = new AssignTemplateEntity();
        template1.setId("template_id_1");
        template1.setThemeName("themeName");
        template1.setActivityNumber("activityNumber");
        template1.setActivityDescription("activityDescription");
        template1.setSourceName("sourceName");
        template1.setRateUnit("rateUnit");
        template1.setAssignId("assign_id");
        template1.setActivityRepeatCount(2);
        templates.add(template1);


        List<StudentDTO> studentDTOS = new ArrayList<>();
        StudentDTO studentDTO = new StudentDTO();
        studentDTO.setEnrollmentId("child_1");
        studentDTO.setGroupId("group_id");
        studentDTOS.add(studentDTO);
        // Setup
        when(inkindDao.getCurrentAssignByAgencyIdAndDate(any(String.class), any(String.class), any(Date.class))).thenReturn(nowAssignArr);
        when(inkindDao.findTemplateByAssignId(any(List.class))).thenReturn(templates);
        when(studentDao.listGroupIdsByChildIds(anyList())).thenReturn(studentDTOS);

        // Execute
        List<AssignmentJobEntity> result = inKindProvider.buildAssignmentJobList(reports, "child_1", "agencyId1", utcNow);

        // Verify
        // Adjust the assertions based on the actual expected result
        assertEquals(2, result.size(), "Expected two AssignmentJobEntity objects in the result list.");

    }

    @Test
    void testBuildAssignmentJobListMore() {

        List<InkindReportEntity> reports = new ArrayList<>();
        InKindReportDTO reportDTO1 = new InKindReportDTO();
        reportDTO1.setActivityDate(new Date());
        reportDTO1.setEnrollmentId("child_1");
        reportDTO1.setThemeName("themeName");
        reportDTO1.setActivityNumber("activityNumber");
        reportDTO1.setActivityDescription("activityDescription");
        reportDTO1.setSourceName("sourceName");
        reportDTO1.setRateUnit("rateUnit");
        reportDTO1.setTemplateId("123");

        InKindReportDTO reportDTO2 = new InKindReportDTO();
        reportDTO2.setEnrollmentId("child_2");
        reportDTO2.setActivityDate(new Date());
        reportDTO2.setThemeName("themeName");
        reportDTO2.setActivityNumber("activityNumber");
        reportDTO2.setActivityDescription("activityDescription");
        reportDTO2.setSourceName("sourceName");
        reportDTO2.setRateUnit("rateUnit");

        reports.add(reportDTO1);
        reports.add(reportDTO2);

        Date utcNow = TimeUtil.getUtcNow();


        List<AssignEntity> nowAssignArr = new ArrayList<>();
        AssignEntity assignEntity = new AssignEntity();
        assignEntity.setGroupId("group_id");
        assignEntity.setId("assign_id");
        nowAssignArr.add(assignEntity);

        List<AssignTemplateEntity> templates = new ArrayList<>();
        AssignTemplateEntity template1 = new AssignTemplateEntity();
        template1.setId("template_id_1");
        template1.setThemeName("themeName");
        template1.setActivityNumber("activityNumber");
        template1.setActivityDescription("activityDescription");
        template1.setSourceName("sourceName");
        template1.setRateUnit("rateUnit");
        template1.setAssignId("assign_id");
        template1.setActivityRepeatCount(2);
        templates.add(template1);


        List<StudentDTO> studentDTOS = new ArrayList<>();
        StudentDTO studentDTO = new StudentDTO();
        studentDTO.setEnrollmentId("child_1");
        studentDTO.setGroupId("group_id");
        studentDTOS.add(studentDTO);


        // Execute
        AssignTemplateEntity template2 = new AssignTemplateEntity();
        template2.setId("template_id_2");
        template2.setThemeName("themeName");
        template2.setActivityNumber("activityNumber");
        template2.setActivityDescription("activityDescription");
        template2.setSourceName("sourceName");
        template2.setRateUnit("rateUnit");
        template2.setAssignId("assign_id");
        template2.setActivityRepeatCount(2);
        templates.add(template2);
        AssignTemplateEntity template3 = new AssignTemplateEntity();
        template3.setId("template_id_3");
        template3.setThemeName("themeName");
        template3.setActivityNumber("activityNumber");
        template3.setActivityDescription("activityDescription");
        template3.setSourceName("sourceName");
        template3.setRateUnit("rateUnit");
        template3.setAssignId("assign_id_3");
        template3.setActivityRepeatCount(2);
        templates.add(template3);
        List<AssignmentJobEntity> newJobList = new ArrayList<>();
        AssignmentJobEntity job = new AssignmentJobEntity();
        job.setTemplateId("template_id_1");
        newJobList.add(job);
        List<String> noHaveJobTemplateId = new ArrayList<>();
        noHaveJobTemplateId.add("template_id_1");
        noHaveJobTemplateId.add("template_id_2");
        noHaveJobTemplateId.add("template_id_3");
        when(inkindDao.findAssignJobByEnrollmentIdAndTemplateIds("child_1", noHaveJobTemplateId)).thenReturn(newJobList);

        // Setup
        when(inkindDao.getCurrentAssignByAgencyIdAndDate(any(String.class), any(String.class), any(Date.class))).thenReturn(nowAssignArr);
        when(inkindDao.findTemplateByAssignId(any(List.class))).thenReturn(templates);
        when(studentDao.listGroupIdsByChildIds(anyList())).thenReturn(studentDTOS);

        List<AssignmentJobEntity> result = inKindProvider.buildAssignmentJobList(reports, "child_1", "agencyId1", utcNow);

        // Verify
        assertEquals(2, result.size(), "Expected an empty result list when no reports are provided.");

    }

}
