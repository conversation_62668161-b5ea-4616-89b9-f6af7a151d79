package com.learninggenie.api.util;

import com.amazonaws.services.lambda.model.InvokeResult;
import com.learninggenie.api.provider.RemoteProvider;
import com.learninggenie.api.provider.UserProvider;
import com.learninggenie.common.data.dao.ReportDao;
import com.learninggenie.common.data.enums.PdfConvertStatus;
import com.learninggenie.common.data.model.PdfConvertJobEntity;
import com.learninggenie.common.filesystem.FileSystem;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;

import static org.junit.Assert.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class HtmlToPdfTest {
    @Mock
    private ReportDao reportDao;

    @Mock
    private RemoteProvider remoteProvider;

    @Mock
    private UserProvider userProvider;

    @Mock
    private FileSystem fileSystem;

    @InjectMocks
    private HtmlToPdf htmlToPdf;

    @Test
    public void testGetPdfFromHtml() throws InterruptedException, ExecutionException {
        // 测试数据
        List<String> htmlUrls = new ArrayList<>();
        htmlUrls.add("http://example.com"); // 添加一个 HTML URL 到列表中
        String pdfBucket = "test-bucket"; // PDF存储桶的名称
        String fileKey = "test-file"; // 文件的键值

        ReflectionTestUtils.setField(htmlToPdf, "userProvider", userProvider); // 设置 userProvider
        ReflectionTestUtils.setField(htmlToPdf, "reportDao", reportDao); // 设置 reportDao
        ReflectionTestUtils.setField(htmlToPdf, "remoteProvider", remoteProvider); // 设置 remoteProvider

        // 模拟依赖项
        PdfConvertJobEntity pdfConvertJobEntity = new PdfConvertJobEntity(); // 创建一个 PDF 转换任务实体
        pdfConvertJobEntity.setStatus(PdfConvertStatus.SUCCEED.toString()); // 设置任务状态为成功
        pdfConvertJobEntity.setPdfUrl("http://example.com/pdf/test.pdf"); // 设置 PDF 的 URL
        when(reportDao.getPdfJob(anyString())).thenReturn(pdfConvertJobEntity); // 当调用 getPdfJob 方法时，返回上面创建的 PDF 转换任务实体
        when(userProvider.getCurrentUserId()).thenReturn("test-user"); // 当调用 getCurrentUserId 方法时，返回一个测试用户 ID

        InvokeResult invokeResult = new InvokeResult(); // 创建一个调用结果
        invokeResult.setStatusCode(200); // 设置状态码为 200
        Future<InvokeResult> future = mock(Future.class); // 模拟一个 Future 对象
        when(future.get()).thenReturn(invokeResult); // 当调用 Future 的 get 方法时，返回上面创建的调用结果
        when(remoteProvider.callPdfService(anyString(), anyList())).thenReturn(future); // 当调用 callPdfService 方法时，返回上面模拟的 Future 对象

        // 调用被测试的方法
        String result = htmlToPdf.getPdfFromHtml(htmlUrls, pdfBucket, fileKey); // 从 HTML URL 列表中获取 PDF

        // 验证结果
        assertEquals("http://example.com/pdf/test.pdf", result); // 断言结果与预期的 PDF URL 相同

    }

    @Test
    public void testConvertHtmlToPdf() {
        // 初始化参数
        List<String> htmlUrls = Arrays.asList("http://example.com");
        PdfConvertJobEntity job = new PdfConvertJobEntity();
        job.setId("pdfJobId001");
        job.setPdfName("test.pdf");

        // 设置 userProvider
        ReflectionTestUtils.setField(htmlToPdf, "userProvider", userProvider);
        // 设置 reportDao
        ReflectionTestUtils.setField(htmlToPdf, "reportDao", reportDao);
        // 设置 remoteProvider
        ReflectionTestUtils.setField(htmlToPdf, "remoteProvider", remoteProvider);
        // 设置 fileSystem
        ReflectionTestUtils.setField(htmlToPdf, "fileSystem", fileSystem);

        // 当调用 getCurrentUserId 方法时，返回一个测试用户 ID
        when(userProvider.getCurrentUserId()).thenReturn("testUserId");
        // 当调用 callPdfService 方法时，返回一个状态码为 200 的 InvokeResult
        when(remoteProvider.callPdfService(anyString(), anyList())).thenReturn(CompletableFuture.completedFuture(new InvokeResult().withStatusCode(200)));
        PdfConvertJobEntity pdfJob = new PdfConvertJobEntity();
        pdfJob.setId("pdfJobId001");
        pdfJob.setPdfName("test.pdf");
        pdfJob.setUrl("https://s3.amazonaws.com/com.learning-genie.prod.pdf/test.html");
        pdfJob.setPdfUrl("https://s3.amazonaws.com/com.learning-genie.prod.pdf/test.pdf");
        pdfJob.setStatus("SUCCEED");
        pdfJob.setType("IMPORT_PDF");
        pdfJob.setPdfPath("test.pdf");
        pdfJob.setBucket("com.learning-genie.prod.pdf");
        // 当调用 getPdfJob 方法时，返回上面创建的 PDF 转换任务实体
        when(reportDao.getPdfJob(anyString())).thenReturn(pdfJob);
        // 当调用 getFileStream 方法时，返回一个新的 ByteArrayInputStream
        when(fileSystem.getFileStream(any(), any())).thenReturn(new ByteArrayInputStream(new byte[10]));

        // 调用 convertHtmlToPdf 方法
        InputStream result = HtmlToPdf.convertHtmlToPdf(htmlUrls, job);

        // 断言结果不为空
        assertNotNull(result);
        // 验证 reportDao 的 createPdfConvertJob 方法被调用了一次
        verify(reportDao, times(1)).createPdfConvertJob(pdfJob);
        // 验证 remoteProvider 的 callPdfService 方法被调用了一次
        verify(remoteProvider, times(1)).callPdfService(anyString(), anyList());
        // 验证 reportDao 的 getPdfJob 方法被调用了一次
        verify(reportDao, times(1)).getPdfJob(anyString());
        // 验证 fileSystem 的 getFileStream 方法被调用了一次
        verify(fileSystem, times(1)).getFileStream(anyString(), anyString());
    }
}