package com.learninggenie.api.util;

import com.learninggenie.common.data.model.CenterEntity;
import junit.framework.TestCase;
import org.junit.Assert;
import org.junit.Test;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class CenterSorterTest extends TestCase {
    @Test
    public void testSort(){
        System.out.println("测试根据学校名称进行排序");
        List<CenterEntity> centers = new ArrayList<>();
        CenterEntity c1 = new CenterEntity("123","abc");
        centers.add(c1);
        CenterEntity c2 = new CenterEntity("234","bbc");
        centers.add(c2);
        CenterEntity c3 = new CenterEntity("345","cbc");
        centers.add(c3);
        CenterEntity c4 = new CenterEntity("456","cab");
        centers.add(c4);
        Collections.sort(centers, new CenterSorter());
        Assert.assertTrue(centers.get(0).getId().equals("123"));
        Assert.assertTrue(centers.get(1).getId().equals("234"));
        Assert.assertTrue(centers.get(2).getId().equals("456"));
        Assert.assertTrue(centers.get(3).getId().equals("345"));
    }
}
