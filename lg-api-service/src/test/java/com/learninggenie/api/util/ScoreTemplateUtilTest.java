package com.learninggenie.api.util;

import com.learninggenie.api.model.DashboardExcelResponse;
import com.learninggenie.api.model.dashboard.OutputData;
import com.learninggenie.common.data.model.AgencyModel;
import com.learninggenie.common.data.model.ChildEntity;
import com.learninggenie.common.data.model.GroupEntity;
import com.learninggenie.common.data.model.LevelEntity;
import org.junit.Assert;
import org.junit.Test;

import java.io.File;
import java.util.*;

import static org.mockito.Mockito.*;


public class ScoreTemplateUtilTest {

    @Test
    public void testGetTitlesTest() throws Exception{
        List list = ScoreTemplateUtil.getTitles(ScoreTemplateUtil.PS2015);
        Assert.assertTrue(list.size() == 62);
        List list1 = ScoreTemplateUtil.getTitles(ScoreTemplateUtil.IT2015);
        Assert.assertTrue(list1.size() == 61);
        List list2 = ScoreTemplateUtil.getTitles(ScoreTemplateUtil.K2015);
        Assert.assertTrue(list2.size() == 75);
    }
    @Test
    public void testFormatString() throws Exception{
       String str= ScoreTemplateUtil.formatString(" ddd ddd    ddd ");
        Assert.assertTrue("ddd ddd ddd".equals(str));
    }
    @Test
    public void testFormat(){
        String fileName =String.format("%s_%s_%s_%s.xls", "avb", "34", "56", "78");
        Assert.assertTrue(fileName.equals("avb_34_56_78.xls"));
    }

    @Test
    public void testSort(){
        List<LevelEntity> list = new ArrayList<>();
        {
            LevelEntity l = new LevelEntity();
            l.setId("0");
            l.setValue("m");
            list.add(l);
        }
        {
            LevelEntity l = new LevelEntity();
            l.setId("1");
            l.setValue("3");
            list.add(l);
        }
        {
            LevelEntity l = new LevelEntity();
            l.setId("2");
            l.setValue("5");
            list.add(l);
        }{
            LevelEntity l = new LevelEntity();
            l.setId("3");
            l.setValue("u");
            list.add(l);
        }

        {
            LevelEntity l = new LevelEntity();
            l.setId("4");
            l.setValue("7");
            list.add(l);
        }
        Collections.sort(list, new Comparator<LevelEntity>() {
            @Override
            public int compare(LevelEntity o1, LevelEntity o2) {
                int n1, n2;
                try {
                    n1 = Integer.parseInt(o1.getValue());
                } catch (Exception e) {
                    return 1;
                }
                try {
                    n2 = Integer.parseInt(o2.getValue());
                } catch (Exception e) {
                    return -1;
                }
                if (n1 >= n2) {
                    return 1;
                }
                return -1;
            }
        });
    }
    //TODO: migrate to new test
    @Test
    public void testOutputExcel() throws Exception{
        List<ChildEntity> childEntities = new ArrayList<>();
        ChildEntity c1 = new ChildEntity();
        c1.setId("123");
        c1.setLastName("123");
        c1.setFirstName("123");
        childEntities.add(c1);
        ChildEntity c2 = new ChildEntity();
        c2.setId("234");
        c2.setLastName("234");
        c2.setFirstName("234");
        childEntities.add(c2);
        ChildEntity c3 = new ChildEntity();
        c3.setId("345");
        c3.setLastName("345");
        c3.setFirstName("345");
        childEntities.add(c3);

        List<String> titles = new ArrayList<>();
        titles.add("a1");
        titles.add("a2");
        titles.add("a3");

        Map<String, Map<String, String>> allMaps = new HashMap<>();
        for(int i=0;i<3;i++){
            Map<String,String> data = new HashMap<>();
            data.put("a1","v1"+i);
            data.put("a2","v2"+i);
            data.put("a3","v3"+i);
            allMaps.put(childEntities.get(i).getId(),data);
        }
//        ByteArrayOutputStream outputStream = (ByteArrayOutputStream) ScoreTemplateUtil.outputExcel(childEntities,titles,allMaps);
//        InputStream inputStream = new ByteArrayInputStream(outputStream.toByteArray());
//        Workbook workbook = new XSSFWorkbook(inputStream);
//        Sheet s = workbook.getSheetAt(0);
//        Assert.assertTrue(s.getLastRowNum()==3);
//        for(int i=0;i<=s.getLastRowNum();i++){
//            Assert.assertTrue(s.getRow(i).getLastCellNum()==3);
//        }
//        workbook.close();
    }

    @Test
    public void testOutputExcelHasoutputExcelnputStream() throws Exception{
//        InputStream inputStream = new FileInputStream(new File(URLDecoder.decode(ScoreTemplateUtilTest.class.getClassLoader().getResource("test.xlsx").getFile())));
        List<ChildEntity> childEntities = new ArrayList<>();
        ChildEntity c1 = new ChildEntity();
        c1.setId("123");
        c1.setLastName("123");
        c1.setFirstName("123");
        c1.setGroup(new GroupEntity("123","c1"));
        childEntities.add(c1);
        ChildEntity c2 = new ChildEntity();
        c2.setId("234");
        c2.setLastName("234");
        c2.setFirstName("234");
        c2.setGroup(new GroupEntity("123", "c1"));
        childEntities.add(c2);
        ChildEntity c3 = new ChildEntity();
        c3.setId("345");
        c3.setLastName("345");
        c3.setFirstName("345");
        c3.setGroup(new GroupEntity("123", "c1"));
        childEntities.add(c3);

        List<String> titles = new ArrayList<>();
        titles.add("a1");
        titles.add("a2");
        titles.add("a3");
        Map<String, Map<String, String>> allMaps = new HashMap<>();
        List<Map<String,Object>> list = new ArrayList<>();
        for(int i=0;i<3;i++){
            Map<String,String> data = new HashMap<>();
            data.put("a1","v"+i);
            data.put("a2","v"+i);
            data.put("a3","v"+i);
            allMaps.put(childEntities.get(i).getId(),data);

            Map<String,Object> childMap = new HashMap<>();
            childMap.put("firstName", childEntities.get(i).getFirstName());
            childMap.put("lastName", childEntities.get(i).getLastName());
            childMap.put("ratedMeasure", 0);
            childMap.put("conditionalMeasure", 0);
            list.add(childMap);
        }

//        ByteArrayOutputStream outputStream = (ByteArrayOutputStream) ScoreTemplateUtil.outputExcel(childEntities,
//                titles, allMaps, "xlsx", new XSSFWorkbook(inputStream), false, false, list);
//        InputStream in = new ByteArrayInputStream(outputStream.toByteArray());
//        Workbook workbook = new XSSFWorkbook(in);
//        Sheet s = workbook.getSheetAt(0);
//        Assert.assertTrue(s.getLastRowNum()==3);
//        for(int i=1;i<=s.getLastRowNum();i++){
//            Assert.assertTrue(s.getRow(i).getCell(0).getStringCellValue().equals("v"+(i-1)));
//            Assert.assertTrue(s.getRow(i).getCell(1).getStringCellValue().equals("v"+(i-1)));
//            Assert.assertTrue(s.getRow(i).getCell(2).getStringCellValue().equals("v"+(i-1)));
//        }
//        workbook.close();
    }

    @Test
    public void testOutputDRDPCommonExcel() throws Exception {
        // 创建方法参数所需的模拟对象
        AgencyModel agency = mock(AgencyModel.class); // 创建一个模拟的 AgencyModel 对象
        String alias = "2023-2024 Fall"; // 创建一个别名字符串
        DashboardExcelResponse response = mock(DashboardExcelResponse.class); // 创建一个模拟的 DashboardExcelResponse 对象
        List<String> itChildIds = new ArrayList<>(); // 创建一个空的 itChildIds 列表
        Map<String, Map<String, String>> itAllMaps = new HashMap<>(); // 创建一个空的 itAllMaps 映射
        List<String> psChildIds = new ArrayList<>(); // 创建一个空的 psChildIds 列表
        Map<String, Map<String, String>> psAllMaps = new HashMap<>(); // 创建一个空的 psAllMaps 映射
        List<String> kChildIds = new ArrayList<>(); // 创建一个空的 kChildIds 列表
        Map<String, Map<String, String>> kAllMaps = new HashMap<>(); // 创建一个空的 kAllMaps 映射
        List<String> saChildIds = new ArrayList<>(); // 创建一个空的 saChildIds 列表
        Map<String, Map<String, String>> saAllMaps = new HashMap<>(); // 创建一个空的 saAllMaps 映射
        List<OutputData> othersDataList = new ArrayList<>(); // 创建一个空的 othersDataList 列表
        Map<String, Map<String, String>> it2025AllNewMaps = new HashMap<>();
        Map<String, Map<String, String>> ptkAllNewMaps = new HashMap<>();
        Map<String, Map<String, String>> ptk3AllNewMaps = new HashMap<>();
        // 调用待测试的方法
        ScoreTemplateUtil.outputDRDPCommonExcel(agency, alias, response, itChildIds, itAllMaps, psChildIds, psAllMaps, kChildIds, kAllMaps, saChildIds, saAllMaps, it2025AllNewMaps, ptkAllNewMaps, ptk3AllNewMaps, othersDataList, false);

        // 验证结果
        verify(agency).getName(); // 验证是否调用了 agency 的 getName 方法
        verify(response).setFile(any(File.class)); // 验证是否调用了 response 的 setFile 方法
        verify(response).setFileType(anyString()); // 验证是否调用了 response 的 setFileType 方法
    }
}
