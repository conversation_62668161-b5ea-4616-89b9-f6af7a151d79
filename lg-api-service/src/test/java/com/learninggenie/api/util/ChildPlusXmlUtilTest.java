package com.learninggenie.api.util;

import com.learninggenie.api.model.*;
import com.learninggenie.common.data.model.User;
import junit.framework.Assert;
import junit.framework.TestCase;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.junit.Test;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

public class ChildPlusXmlUtilTest extends TestCase {
    @Test
    public void testParsechildPlusXmlData() throws Exception {
       System.out.println("测试解析childplus的xml数据文件");
        File f = new File(URLDecoder.decode(ChildPlusXmlUtilTest.class.getClassLoader().getResource("data.xml").getFile()));
        if (!f.exists()) {
            return;
        }
        BufferedReader reader = new BufferedReader(new FileReader(f));
        StringBuffer tempString = new StringBuffer();
        String str;
        while ((str = reader.readLine()) != null) {
            tempString.append(str);
        }

        ChildPlusData childPlusData = new ChildPlusData();
        Document document = DocumentHelper.parseText(tempString.toString());
        Element export = document.getRootElement();


        // Element export = root.element("ChildPlusDataExport");
        List<EnrollmentClassroom> enrollmentClassrooms = new ArrayList<>();
        childPlusData.setEnrollmentClassrooms(enrollmentClassrooms);
        Iterator participantsIterator = export.elementIterator("Participants");
        while (participantsIterator.hasNext()) {
            Element participants = (Element) participantsIterator.next();
            Iterator participantIterator = participants.elementIterator("Participant");
            while (participantIterator.hasNext()) {
                Element participant = (Element) participantIterator.next();
                String personId = participant.attributeValue("PersonID");
                Iterator participationIterator = participant.elementIterator("Participation");
                while (participationIterator.hasNext()) {
                    Element participation = (Element) participationIterator.next();
                    Iterator enrollmentIterator = participation.elementIterator("Enrollment");
                    while (enrollmentIterator.hasNext()) {
                        Element enrollment = (Element) enrollmentIterator.next();
                        enrollment.elementTextTrim("SiteID");
                        String classroomId = enrollment.elementTextTrim("ClassroomID");
                        EnrollmentClassroom enrollmentClassroom = new EnrollmentClassroom(personId.toUpperCase(), classroomId.toUpperCase());
                        enrollmentClassrooms.add(enrollmentClassroom);
                    }
                }
            }
        }

        Iterator familyApplicationsIterator = export.elementIterator("FamilyApplications");
        List<User> parents = new ArrayList<>();
        List<Enrollment> enrollments = new ArrayList<>();
        childPlusData.setParents(parents);
        childPlusData.setEnrollments(enrollments);
        while (familyApplicationsIterator.hasNext()) {
            Element familyApplications = (Element) familyApplicationsIterator.next();
            Iterator familyApplicationIterator = familyApplications.elementIterator("FamilyApplication");
            while (familyApplicationIterator.hasNext()) {
                Element familyApplication = (Element) familyApplicationIterator.next();
                Iterator familyMemberIterator = familyApplication.elementIterator("FamilyMember");

                while (familyMemberIterator.hasNext()) {
                    Element familyMember = (Element) familyMemberIterator.next();
                    String personId = familyMember.elementTextTrim("PersonID");

                    String firstName = familyMember.elementTextTrim("FirstName");
                    String lastName = familyMember.elementTextTrim("LastName");
                    String adultChild = familyMember.elementTextTrim("AdultChild");

                    if ("Child".equals(adultChild)) {
                        Iterator relationIterator = familyMember.elementIterator("Relationship");
                        Enrollment enrollment = new Enrollment();
                        enrollment.setId(personId.toUpperCase());
                        enrollment.setFirstName(firstName);
                        enrollment.setLastName(lastName);
                        while (relationIterator.hasNext()) {
                            Element relation = (Element) relationIterator.next();
                            String adultPersionId = relation.elementTextTrim("AdultPersonID");
                            enrollment.getParentIdList().add(adultPersionId);
                        }
                        enrollments.add(enrollment);
                    } else {
                        String email = familyMember.elementTextTrim("Email");
                        User parent = new User();
                        parent.setId(personId.toUpperCase());
                        parent.setFirstName(firstName);
                        parent.setLastName(lastName);
                        parent.setEmail(email);
                        parents.add(parent);
                    }
                }
            }
        }


        List<User> staffs = new ArrayList<>();
        childPlusData.setStaffs(staffs);
        Iterator personnelsIterator = export.elementIterator("Personnel");
        while (personnelsIterator.hasNext()) {
            Element personnels = (Element) personnelsIterator.next();
            Iterator personnelIterator = personnels.elementIterator("Personnel");
            while (personnelIterator.hasNext()) {
                Element personnel = (Element) personnelIterator.next();
                String personId = personnel.attributeValue("PersonID");
                String firstName = personnel.elementTextTrim("FirstName");
                String lastName = personnel.elementTextTrim("LastName");
                String position = personnel.elementTextTrim("Position");
                String email = personnel.elementTextTrim("Email");
                //Teacher,Assistant/Aide,Site Manager,Director
                User staff = new User();
                staff.setId(personId.toUpperCase());
                staff.setFirstName(firstName);
                staff.setLastName(lastName);
                staff.setEmail(email);
                staff.setPosition(position);
                staffs.add(staff);
            }
        }

        List<Center> centers = new ArrayList<>();
        childPlusData.setCenters(centers);
        Iterator locationsIterator = export.elementIterator("Locations");
        while (locationsIterator.hasNext()) {
            Element locations = (Element) locationsIterator.next();
            Iterator agencyIterator = locations.elementIterator("Agency");
            while (agencyIterator.hasNext()) {
                Element agency = (Element) agencyIterator.next();
                Iterator siteIterator = agency.elementIterator("Site");
                while (siteIterator.hasNext()) {
                    Element site = (Element) siteIterator.next();
                    String sitedId = site.attributeValue("SiteID");
                    String siteName = site.elementTextTrim("Name");
                    Center center = new Center();
                    center.setId(sitedId.toUpperCase());
                    center.setName(siteName);
                    centers.add(center);
                    Iterator classroomIterator = site.elementIterator("Classroom");
                    while (classroomIterator.hasNext()) {
                        Element classroom = (Element) classroomIterator.next();
                        String classroomId = classroom.elementTextTrim("ClassroomID");
                        String classroomName = classroom.elementTextTrim("ClassName");
                        Group group = new Group();
                        group.setId(classroomId.toUpperCase());
                        group.setName(classroomName);
                        center.getGroups().add(group);
                        Iterator teacherIterator = classroom.elementIterator("ClassroomTeacher");
                        while (teacherIterator.hasNext()) {
                            Element teacher = (Element) teacherIterator.next();

                            String teacherId = teacher.attributeValue("PersonID");
                            String teacherName = teacher.elementTextTrim("Name");
                            User teacherEntity = new User();
                            teacherEntity.setId(teacherId.toUpperCase());
                            teacherEntity.setName(teacherName);
                            group.getTeachers().add(teacherEntity);
                        }

                        Iterator aideIterator = classroom.elementIterator("ClassroomAide");
                        while (aideIterator.hasNext()) {
                            Element aide = (Element) aideIterator.next();
                            String teacherId = aide.attributeValue("PersonID");
                            String teacherName = aide.elementTextTrim("Name");
                            User teacherEntity = new User();
                            teacherEntity.setId(teacherId.toUpperCase());
                            teacherEntity.setName(teacherName);
                            group.getTeachers().add(teacherEntity);

                        }
                    }
                }
            }
        }
        reader.close();
        Assert.assertTrue(childPlusData.getCenters().size() == 1);
        Assert.assertTrue(childPlusData.getEnrollments().size()==1);
        Assert.assertTrue(childPlusData.getParents().size()==1);
        Assert.assertTrue(childPlusData.getStaffs().size()==2);
    }
}
