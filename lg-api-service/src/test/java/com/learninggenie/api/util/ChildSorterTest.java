package com.learninggenie.api.util;

import com.learninggenie.common.data.model.ChildEntity;
import junit.framework.TestCase;
import org.junit.Ignore;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by Administrator on 2015/6/5 0005.
 */
public class ChildSorterTest extends TestCase {
    @Ignore
    @Test
    public void testSort() {
        List<ChildEntity> list = new ArrayList<>();
        ChildEntity c1 = new ChildEntity("1","xz c","xz","c",null);
        list.add(c1);
        ChildEntity c2 = new ChildEntity("1","xz a","xz","a",null);
        list.add(c2);
        ChildEntity c3 = new ChildEntity("1","xz b","xz","b",null);
        list.add(c3);

       // Collections.sort(list, new ChildSorter());

       // Assert.assertTrue(list.get(0).getName().equals("xz a"));
       // Assert.assertTrue(list.get(1).getName().equals("xz b"));
       // Assert.assertTrue(list.get(2).getName().equals("xz c"));

    }
}
