package com.learninggenie.api.util;

import com.learninggenie.api.model.Group;
import com.learninggenie.common.data.model.User;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;

/**
 * 第三方导入 Excel 工具类测试类
 */
@RunWith(MockitoJUnitRunner.class)
public class ParseExcelUtilTest {

    /**
     * 测试 excel 中通过 email 和 group 来获取对应老师的信息
     */
    @Test
    public void testGetTeacher() {
        // 定义 teacherEmail 和 group
        String teacherEmail = "<EMAIL>";
        // 定义从 excel 中获取到的 group
        Group group = new Group();
        // 定义班级中的 teacher
        ArrayList<User> teachers = new ArrayList<>();
        // 循环添加 teacher
        for (int i = 0; i < 3; i++) {
            // 定义班级中的老师信息
            User teacher = new User();
            // 设置老师的 email
            teacher.setEmail("teacher" + i + "@gmail.com");
            // 将老师添加进入 teachers
            teachers.add(teacher);
        }
        // 将 teachers 设置到 group 中
        group.setTeachers(teachers);

        // 调用 ParseExcelUtil 中的 getTeacher 方法
        User teacher = ParseExcelUtil.getTeacher(teacherEmail, group);
        // 断言 teacher 不为空
        Assertions.assertNotNull(teacher);
    }
}
