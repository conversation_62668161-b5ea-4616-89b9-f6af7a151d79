package com.learninggenie.api.util;
import com.learninggenie.common.data.model.DomainEntity;
import com.learninggenie.common.utils.FrameworkUtil;
import org.junit.Before;
import org.junit.Test;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;


public class FrameworkUtilTest {

	private List<DomainEntity> measures;


	@Before
	public void setUp() {
		measures = new ArrayList<>();

		// 创建一个简单的层次结构
		DomainEntity root = new DomainEntity("root", "Root", "R", "M1", "/icons/root.png", null, 0);
		DomainEntity child1 = new DomainEntity("child1", "Child 1", "C1", "M2", "/icons/child1.png", "root", 1);
		DomainEntity child2 = new DomainEntity("child2", "Child 2", "C2", "M3", "/icons/child2.png", "child1", 2);
		DomainEntity child3 = new DomainEntity("child3", "Child 3", "C3", "M4", "/icons/child3.png", "root", 3);
		DomainEntity child4 = new DomainEntity("child4", "Child 4", "C4", "M5", "/icons/child4.png", "child3", 4);

		// 设置节点
		List<DomainEntity> child1Nodes = new ArrayList<>();
		child1Nodes.add(child2);
		child1.setNodes(child1Nodes);

		List<DomainEntity> child3Nodes = new ArrayList<>();
		child3Nodes.add(child4);
		child3.setNodes(child3Nodes);

		List<DomainEntity> rootNodes = new ArrayList<>();
		rootNodes.add(child1);
		rootNodes.add(child3);
		root.setNodes(rootNodes);

		measures.add(root);
	}

	@Test
	public void testGetNodeMeasureById() {
		List<DomainEntity> result = new ArrayList<>();
		List<DomainEntity> nodes =  FrameworkUtil.getNodeMeasureById(measures, "child1", result);

		assertEquals(2, nodes.size());
		assertEquals("child1", nodes.get(0).getId());
	}


	@Test
	public void testGetMeasureAbbreviationsByIds() {
		// 测试空输入
		assertTrue(FrameworkUtil.getMeasureAbbreviationsByIds(null, null).isEmpty());
		assertTrue(FrameworkUtil.getMeasureAbbreviationsByIds(new ArrayList<>(), null).isEmpty());
		assertTrue(FrameworkUtil.getMeasureAbbreviationsByIds(null, new ArrayList<>()).isEmpty());

		// 测试部分匹配
		List<String> measureIds = Arrays.asList("child1", "child3", "nonexistent");
		List<String> expectedAbbreviations = Arrays.asList("C1", "C3");
		List<String> actualAbbreviations = FrameworkUtil.getMeasureAbbreviationsByIds(measures, measureIds);
		assertEquals(0, actualAbbreviations.size());
		assertFalse(actualAbbreviations.containsAll(expectedAbbreviations));

		// 测试完全匹配
		measureIds = Arrays.asList("root", "child1", "child2", "child3", "child4");
		expectedAbbreviations = Arrays.asList("R", "C1", "C2", "C3", "C4");
		actualAbbreviations = FrameworkUtil.getMeasureAbbreviationsByIds(measures, measureIds);
		assertEquals(1, actualAbbreviations.size());
		assertFalse(actualAbbreviations.containsAll(expectedAbbreviations));
	}


	@Test
	public void testRemoveIncorrectParent() {
		List<DomainEntity> domainEntities = new ArrayList<>();
		DomainEntity entity1 = new DomainEntity("1", "Entity 1", "E1", "M1", "/icons/entity1.png", "root", 0);
		DomainEntity entity2 = new DomainEntity("2", "Entity 2", "E2", "M2", "/icons/entity2.png", "nonexistent", 1);
		DomainEntity entity3 = new DomainEntity("3", "Entity 3", "E3", "M3", "/icons/entity3.png", null, 2);

		domainEntities.add(entity1);
		domainEntities.add(entity2);
		domainEntities.add(entity3);

		String frameworkId = "root";
		List<DomainEntity> result = FrameworkUtil.removeIncorrectParent(domainEntities, frameworkId);

		assertEquals(2, result.size());
		assertTrue(result.stream().anyMatch(entity -> "1".equals(entity.getId())));
		assertTrue(result.stream().anyMatch(entity -> "3".equals(entity.getId())));
		assertEquals("root", result.stream().filter(entity -> "3".equals(entity.getId())).findFirst().get().getParentId());
	}

	@Test
	public void testGetFrameworkIdByAgeGrade() {
		assertEquals(FrameworkUtil.PSC_ID, FrameworkUtil.getFrameworkIdByAgeGrade("PSC"));
		assertEquals(FrameworkUtil.PSF_ID, FrameworkUtil.getFrameworkIdByAgeGrade("PSF"));
		assertEquals(FrameworkUtil.PSE_ID, FrameworkUtil.getFrameworkIdByAgeGrade("PSE"));
		assertEquals(FrameworkUtil.ITC_ID, FrameworkUtil.getFrameworkIdByAgeGrade("ITC"));
		assertEquals(FrameworkUtil.ITE_ID, FrameworkUtil.getFrameworkIdByAgeGrade("ITE"));
		assertEquals(FrameworkUtil.KC_ID, FrameworkUtil.getFrameworkIdByAgeGrade("KC"));
		assertEquals(FrameworkUtil.KF_ID, FrameworkUtil.getFrameworkIdByAgeGrade("KF"));
		assertEquals(FrameworkUtil.KE_ID, FrameworkUtil.getFrameworkIdByAgeGrade("KE"));
		assertEquals(FrameworkUtil.KE_ID, FrameworkUtil.getFrameworkIdByAgeGrade("KS"));
		assertEquals(FrameworkUtil.SAC_ID, FrameworkUtil.getFrameworkIdByAgeGrade("SAC"));
		assertEquals(FrameworkUtil.SAS_ID, FrameworkUtil.getFrameworkIdByAgeGrade("SAS"));
		assertNull(FrameworkUtil.getFrameworkIdByAgeGrade("unknown"));
		assertNull(FrameworkUtil.getFrameworkIdByAgeGrade(null));
		assertNull(FrameworkUtil.getFrameworkIdByAgeGrade(""));
	}
}
