package com.learninggenie.api.util;

import com.learninggenie.api.model.Enrollment;
import com.learninggenie.api.model.importdata.ImportChild;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;


/**
 * ImportUtil 测试类
 */
@RunWith(MockitoJUnitRunner.class)
public class ImportUtilTest {

    /**
     * isEqualsInImportFile 方法测试
     */
    @Test
    public void isEqualsInImportFileTest() {
        // 参数准备
        ImportChild child1 = new ImportChild();
        child1.setDisplayName("1");
        child1.setBirthDate("2022-10-02");
        child1.setExternalId("1");
        ImportChild child2 = new ImportChild();
        child2.setDisplayName("1");
        child2.setBirthDate("2022-10-02");
        child2.setExternalId(null);
        // 方法调用
        boolean equalsInImportFile1 = ImportUtil.isEqualsInImportFile(child1, child2);
        assert equalsInImportFile1; // 结果验证: 名字、生日相同但是EID 存在空，返回 true
        child2.setExternalId("2");
        // 方法调用
        boolean equalsInImportFile2 = ImportUtil.isEqualsInImportFile(child1, child2);
        assert !equalsInImportFile2; // 结果验证: 名字、生日相同，EID 不同，返回 false
        child1.setDisplayName("2");
        child2.setDisplayName("1");
        child1.setExternalId("1");
        child2.setExternalId("1");
        // 方法调用
        boolean equalsInImportFile3 = ImportUtil.isEqualsInImportFile(child1, child2);
        assert !equalsInImportFile3; // 结果验证: 名字不同、生日和 EID 相同，返回 false
        child1.setDisplayName("1");
        child1.setBirthDate("2022-10-02");
        child1.setExternalId("1");
        child2.setDisplayName("1");
        child2.setBirthDate("2022-10-01");
        child2.setExternalId("1");
        // 方法调用
        boolean equalsInImportFile4 = ImportUtil.isEqualsInImportFile(child1, child2);
        assert !equalsInImportFile4; // 结果验证: 名字相同、生日不同、 EID 相同返回 false
        child1.setDisplayName("1");
        child1.setBirthDate("2022-10-02");
        child1.setExternalId("1");
        child2.setDisplayName("1");
        child2.setBirthDate("2022-10-02");
        child2.setExternalId("1");
        // 方法调用
        boolean equalsInImportFile5 = ImportUtil.isEqualsInImportFile(child1, child2);
        assert equalsInImportFile5; // 结果验证: 名字相同、生日相同、 EID 相同返回 true
    }

    /**
     * isBirthdayEquals 方法测试
     */
    @Test
    public void isBirthdayEqualsTest() {
        ImportChild child1 = new ImportChild();
        child1.setBirthDate("2022-10-02");
        ImportChild child2 = new ImportChild();
        child2.setBirthDate("2022-10-02");
        // 方法调用
        boolean equalsInImportFile1 = ImportUtil.isBirthdayEquals(child1, child2);
        // 结果验证: 孩子生日相同返回 true
        assert equalsInImportFile1;
        child1.setBirthDate("2022-9-01");
        child2.setBirthDate("2022-9-02");
        // 方法调用
        boolean equalsInImportFile2 = ImportUtil.isBirthdayEquals(child1, child2);
        // 结果验证: 孩子生日不同返回 false
        assert !equalsInImportFile2;
    }

    /**
     * isRepeatWithIdAndNameBirthday 方法测试
     */
    @Test
    public void testIsRepeatWithIdAndNameBirthday() {
        // 创建测试数据
        Enrollment enrollment = new Enrollment();
        enrollment.setId("0");
        enrollment.setExternalId("eeee");
        enrollment.setName("1");
        enrollment.setFirstName("Alice");
        enrollment.setLastName("1");
        enrollment.setBirthDate("2222-01-01");
        List<Enrollment> enrollments = new ArrayList<>();
        Enrollment enrollment1 = new Enrollment();
        Enrollment enrollment2 = new Enrollment();
        Enrollment enrollment3 = new Enrollment();
        enrollment1.setId("1");
        enrollment1.setExternalId("EID1");
        enrollment1.setName("Alice1");
        enrollment1.setFirstName("Alice");
        enrollment1.setLastName("2");
        enrollment1.setBirthDate("2222-01-01");
        enrollment2.setId("2");
        enrollment2.setName("Alice2");
        enrollment2.setExternalId("EID2");
        enrollment2.setFirstName("Alice");
        enrollment2.setLastName("2");
        enrollment2.setBirthDate("2000-01-01");
        enrollment3.setId("2");
        enrollment3.setName("Alice1");
        enrollment3.setExternalId("");
        enrollment3.setFirstName("Alice");
        enrollment3.setLastName("1");
        enrollment3.setBirthDate("2222-01-01");
        enrollments.add(enrollment1);
        enrollments.add(enrollment2);
        enrollments.add(enrollment3);
        // 调用被测试方法，传入测试数据
        boolean isRepeat = ImportUtil.isRepeatWithIdAndNameBirthday(enrollments, enrollment);
        // 断言期望的结果为 true
        assert isRepeat;
    }
}
