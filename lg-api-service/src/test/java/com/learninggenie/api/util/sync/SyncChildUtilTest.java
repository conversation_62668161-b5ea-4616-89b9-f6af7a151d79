package com.learninggenie.api.util.sync;

import com.learninggenie.api.model.ExcelData;
import com.learninggenie.api.model.importdata.ImportStructure;
import com.learninggenie.api.model.importdata.ParseExcelModel;
import com.learninggenie.common.data.enums.ImportResource;
import com.learninggenie.common.data.enums.ImportTitle;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.io.IOException;
import java.util.*;

/**
 * 异步同步学生信息测试类
 */
@RunWith(MockitoJUnitRunner.class)
public class SyncChildUtilTest {

    /**
     * 测试解析学生信息
     */
    @Test
    public void testSyncChild() throws IOException {
        // 数据准备
        // 模拟 EXCEL 数据
        ExcelData excelData = new ExcelData();
        String excelStr = "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";
        excelData.setExcelStr(excelStr);
        excelData.setExcelSuffix("xlsx");
        // timeZone
        String timeZone = "Asia/Shanghai";
        // stageId
        String stageId = UUID.randomUUID().toString();
        // 标题名字
        Map<ImportTitle, List<String>> promisTitleNameMap = new EnumMap<ImportTitle, List<String>>(ImportTitle.class) {{
            put(ImportTitle.CHILD_ID, new ArrayList<String>() {{
                add("FM: Person ID");
            }});
            put(ImportTitle.SITE, new ArrayList<String>() {{
                add("ST: Center Name");
            }});
            put(ImportTitle.CLASS, new ArrayList<String>() {{
                add("ST: Class Name");
            }});
            put(ImportTitle.FIRST_NAME, new ArrayList<String>() {{
                add("FM: First Name");
            }});
            put(ImportTitle.LAST_NAME, new ArrayList<String>() {{
                add("FM: Last Name");
            }});
            put(ImportTitle.BIRTH_DATE, new ArrayList<String>() {{
                add("FM: DOB");
            }});
            put(ImportTitle.ENROLLMENT_DATE, new ArrayList<String>() {{
                add("ST: Status Start Date");
            }});
            put(ImportTitle.HISPANIC, new ArrayList<String>() {{
                add("FM: Hispanic or Latino");
            }});
            put(ImportTitle.RACE, new ArrayList<String>(){{add("FM: Race");}});
            put(ImportTitle.LANGUAGE, new ArrayList<String>() {{
                add("FM: Primary Language");
            }});
            put(ImportTitle.PROGRAM, new ArrayList<String>() {{
                add("ST: Program");
            }});
            put(ImportTitle.IEP, new ArrayList<String>() {{
                add("IEP/IFSP:Form Date");
            }});
            put(ImportTitle.STATUS, new ArrayList<String>() {{
                add("ST: Status");
            }});

            put(ImportTitle.PARENT_FIRST_NAME, new ArrayList<String>() {{
                add("Parent First Name");
                add("Primary Adult First Name");
            }});

            put(ImportTitle.PARENT_LAST_NAME, new ArrayList<String>() {{
                add("Parent Last Name");
                add("Primary Adult Last Name");
            }});

            put(ImportTitle.SECOND_PARENT_FIRST_NAME, new ArrayList<String>() {{
                add("Secondary Adult First Name");
            }});

            put(ImportTitle.SECOND_PARENT_LAST_NAME, new ArrayList<String>() {{
                add("Secondary Adult Last Name");
            }});

            put(ImportTitle.FAMILY_MEMBER_EMAIL_ONE, new ArrayList<String>() {{
                add("parent1 Email");
                add("parent email");
                add("parent email 1");
                add("primary parent email");
                add("primary adult email");
            }});
            put(ImportTitle.FAMILY_MEMBER_EMAIL_TWO, new ArrayList<String>() {{
                add("parent2 Email");
                add("parent email 2");
                add("Secondary Parent Email");
                add("Secondary Adult Email");
            }});
        }};
        // 必须有的列
        List<ImportTitle> promisRequiredCols = new ArrayList<ImportTitle>() {{
            add(ImportTitle.CHILD_ID);
            add(ImportTitle.SITE);
            add(ImportTitle.CLASS);
            add(ImportTitle.LAST_NAME);
            add(ImportTitle.BIRTH_DATE);
            // first name is required
            add(ImportTitle.FIRST_NAME);
            add(ImportTitle.GENDER);
        }};
        // needEntryDate
        boolean needEntryDate = false;
        // 模拟 ParseExcelModel
        ParseExcelModel parseExcelModel = new ParseExcelModel();
        parseExcelModel.setExcelData(excelData);
        parseExcelModel.setTimeZone(timeZone);
        parseExcelModel.setStageId(stageId);
        parseExcelModel.setResource(ImportResource.PROMIS);
        parseExcelModel.setTitleNameMap(promisTitleNameMap);
        parseExcelModel.setRequiredCols(promisRequiredCols);
        parseExcelModel.setNeedEntryDate(needEntryDate);
        // 机构 Id
        String agencyId = UUID.randomUUID().toString();

        // 调用方法
        ImportStructure parse = SyncChildUtil.parseExcel_V2(parseExcelModel, agencyId);

        // 验证结果
        assert parse.getCenters().size() == 1;
    }
}
