package com.learninggenie.api.util;

import com.learninggenie.api.model.ImportDataViewModel;
import com.learninggenie.api.model.importdata.ImportStructure;
import com.learninggenie.common.data.model.ImportEnrollmentModel;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.util.Base64Utils;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * 导入 PowerSchool Excel工具类测试
 */
@RunWith(MockitoJUnitRunner.class)
public class ImportPowerExcelUtilTest {

    /**
     * 测试导入 PowerSchool Excel
     */
    @Test
    public void testImportPowerExcel() throws IOException {
        // 数据准备
        // 导入内容
        String importContent = "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";
        InputStream inputStream = new ByteArrayInputStream(Base64Utils.decodeFromString(importContent));
        // excel 后缀
        String excelSuffix = "xlsx";
        // timeZone
        String timeZone = "Asia/Shanghai";
        // stageId
        String stageId = UUID.randomUUID().toString();
        // needEntryDate
        boolean needEntryDate = false;

        // 调用方法
        ImportDataViewModel parse = ImportPowerExcelUtil.parse(inputStream, excelSuffix, timeZone, stageId, needEntryDate);

        // 验证结果
        assert parse.getCenters().size() == 1;
    }

    /**
     * 测试导入 PowerSchool Excel
     */
    @Test
    public void testImportPowerExcel1() throws IOException {
        // 数据准备
        // 导入内容
        String importContent = "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";
        InputStream inputStream = new ByteArrayInputStream(Base64Utils.decodeFromString(importContent));
        // excel 后缀
        String excelSuffix = "xlsx";
        // timeZone
        String timeZone = "Asia/Shanghai";
        // stageId
        String stageId = UUID.randomUUID().toString();
        // needEntryDate
        boolean needEntryDate = false;
        // 导入类型
        String resource = "childPlus";
        // 机构 Id
        String agencyId = UUID.randomUUID().toString();

        // 调用方法
        ImportStructure importStructure = ImportPowerExcelUtil.parse_V2(inputStream, excelSuffix, timeZone, stageId, needEntryDate, resource, agencyId);

        // 验证结果
        assert importStructure.getCenters().size() == 1;
    }

    /**
     * 测试导入 PowerSchool 解析 Excel 的过滤重复小孩逻辑
     */
    @Test
    public void testImportPowerParsesV2FilterDuplicateChildren() throws IOException {
        // 数据准备
        // 导入内容 学校数 1，班级数 1，孩子数 3 其中存在两个重复小孩
        String importContent = "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";
        InputStream inputStream = new ByteArrayInputStream(Base64Utils.decodeFromString(importContent));
        // excel 后缀
        String excelSuffix = "xlsx";
        // timeZone
        String timeZone = "Asia/Shanghai";
        // stageId
        String stageId = UUID.randomUUID().toString();
        // needEntryDate
        boolean needEntryDate = false;
        // 导入类型
        String resource = "PowerSchool_Import";
        // 机构 Id
        String agencyId = UUID.randomUUID().toString();
        // 调用方法
        ImportStructure importStructure = ImportPowerExcelUtil.parse_V2(inputStream, excelSuffix, timeZone, stageId, needEntryDate, resource, agencyId);
        // 验证结果
        assert importStructure.getCenters().size() == 1; // 验证学校数量
        assert importStructure.getCenters().get(0).getGroups().size() == 1; // 验证班级数量
        assert importStructure.getCenters().get(0).getGroups().get(0).getChildren().size() == 2; // 验证过滤后孩子数量
    }

    /**
     * 测试导入 PowerSchool Excel
     */
    @Test
    public void testImportPowerExcel2() throws IOException {
        // 数据准备
        // 导入小孩模型列表
        List<ImportEnrollmentModel> importEnrollmentModels = new ArrayList<>();
        // 导入内容
        String importContent = "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";
        InputStream inputStream = new ByteArrayInputStream(Base64Utils.decodeFromString(importContent));
        // excel 后缀
        String excelSuffix = "xlsx";
        // timeZone
        String timeZone = "Asia/Shanghai";
        // stageId
        String stageId = UUID.randomUUID().toString();
        // needEntryDate
        boolean needEntryDate = false;
        // 导入类型
        String resource = "childPlus";
        // 机构 Id
        String agencyId = UUID.randomUUID().toString();

        // 调用方法
        ImportStructure importStructure = ImportPowerExcelUtil.parse_V3(importEnrollmentModels, inputStream, excelSuffix, timeZone, stageId, needEntryDate, resource, agencyId);

        // 验证结果
        assert importStructure.getCenters().size() == 1;
    }

    /**
     * 测试导入 PowerSchool 解析 Excel 的过滤重复小孩逻辑
     */
    @Test
    public void testImportPowerParsesV3FilterDuplicateChildren() throws IOException {
        // 数据准备
        // 导入小孩模型列表
        List<ImportEnrollmentModel> importEnrollmentModels = new ArrayList<>();
        // 导入内容 学校数 1，班级数 1，孩子数 3 其中存在两个重复小孩
        String importContent = "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";
        InputStream inputStream = new ByteArrayInputStream(Base64Utils.decodeFromString(importContent));
        // excel 后缀
        String excelSuffix = "xlsx";
        // timeZone
        String timeZone = "Asia/Shanghai";
        // stageId
        String stageId = UUID.randomUUID().toString();
        // needEntryDate
        boolean needEntryDate = false;
        // 导入类型
        String resource = "PowerSchool_Import";
        // 机构 Id
        String agencyId = UUID.randomUUID().toString();
        // 调用方法
        ImportStructure importStructure = ImportPowerExcelUtil.parse_V3(importEnrollmentModels, inputStream, excelSuffix, timeZone, stageId, needEntryDate, resource, agencyId);
        // 验证结果
        assert importStructure.getCenters().size() == 1; // 验证学校数量
        assert importStructure.getCenters().get(0).getGroups().size() == 1; // 验证班级数量
        assert importStructure.getCenters().get(0).getGroups().get(0).getChildren().size() == 2; // 验证过滤后孩子数量
    }
}
