package com.learninggenie.api.util;

import com.google.gson.Gson;
import com.learninggenie.api.model.importdata.ImportCheckParamModel;
import com.learninggenie.api.model.importdata.MappedColumn;
import com.learninggenie.api.model.importdata.ParseOtherDataRequest;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 第三方导入 CSV 工具类测试类
 */
@RunWith(MockitoJUnitRunner.class)
public class OtherCSVUtilTest {

    @Test
    public void testImportCSV() {
        // 数据准备
        // 必须字段
        String metaValue = "[{\"column\":\"First Name\",\"mappedBy\":\"ChildName (First)\",\"columnNum\":0},{\"column\":\"Middle Name\",\"mappedBy\":\"ChildName (Middle)\",\"columnNum\":1},{\"column\":\"Last Name\",\"mappedBy\":\"ChildName (Last)\",\"columnNum\":2},{\"column\":\"Date of Birth\",\"mappedBy\":\"Birthday\",\"columnNum\":3},{\"column\":\"Class Name\",\"mappedBy\":\"Class Name\",\"columnNum\":4},{\"column\":\"Center Name\",\"mappedBy\":\"Center Name\",\"columnNum\":5},{\"column\":\"Gender\",\"mappedBy\":\"Gender\",\"columnNum\":6},{\"column\":\"Enrollment Date\",\"mappedBy\":\"Enrollment Date\",\"columnNum\":7},{\"column\":\"Parent1 Name\",\"mappedBy\":\"Teacher First Name\",\"columnNum\":9},{\"column\":\"Parent1 Email\",\"mappedBy\":\"Teacher Email\",\"columnNum\":10},{\"column\":\"Parent2 Name\",\"mappedBy\":\"Teacher Last Name\",\"columnNum\":11},{\"column\":\"Parent2 Email\",\"mappedBy\":\"Primary Adult Email\",\"columnNum\":12},{\"column\":\"Statewide Student Identifier\",\"mappedBy\":\"Statewide Student Identifier\",\"columnNum\":13},{\"column\":\"External ID\",\"mappedBy\":\"External ID\",\"columnNum\":14},{\"column\":\"Hispanic\",\"mappedBy\":\"Hispanic\",\"columnNum\":15},{\"column\":\"Race\",\"mappedBy\":\"Race\",\"columnNum\":16},{\"column\":\"Language\",\"mappedBy\":\"Language\",\"columnNum\":17},{\"column\":\"ELD\",\"mappedBy\":\"ELD\",\"columnNum\":18},{\"column\":\"Program Name\",\"mappedBy\":\"Program Name\",\"columnNum\":19},{\"column\":\"IEP/IFSP\",\"mappedBy\":\"IEP/IFSP\",\"columnNum\":20}]";
        List<MappedColumn> mappedColumns = Arrays.asList(new Gson().fromJson(metaValue, MappedColumn[].class));
        // timeZone
        String timeZone = "Asia/Shanghai";
        // 模拟文件 base64
        String fileBase64Value = "Rmlyc3QlMjBOYW1lJTJDTWlkZGxlJTIwTmFtZSUyQ0xhc3QlMjBOYW1lJTJDRGF0ZSUyMG9mJTIwQmlydGglMkNDbGFzcyUyME5hbWUlMkNDZW50ZXIlMjBOYW1lJTJDR2VuZGVyJTJDRW5yb2xsbWVudCUyMERhdGUlMkNXaXRoZHJhd24lMjBEYXRlJTJDUGFyZW50MSUyME5hbWUlMkNQYXJlbnQxJTIwRW1haWwlMkNQYXJlbnQyJTIwTmFtZSUyQ1BhcmVudDIlMjBFbWFpbCUyQ1N0YXRld2lkZSUyMFN0dWRlbnQlMjBJZGVudGlmaWVyJTJDRXh0ZXJuYWwlMjBJRCUyQ0hpc3BhbmljJTJDUmFjZSUyQ0xhbmd1YWdlJTJDRUxEJTJDUHJvZ3JhbSUyME5hbWUlMkNJRVAlMkZJRlNQJTBEJTBBTWFyayUyQ0lhbiUyQ0Vtb3RvJTJDMDglMkYxNCUyRjIwMTUlMkMxQiUyQ0dhbGF4eSUyMEtpZHMlMkNNYWxlJTJDMDglMkYxMCUyRjIwMjAlMkMlMkMlMkMlMkMlMkMlMkMxMjM0NTY3ODkwJTJDMTIzNDU2Nzg5MCUyQ05vJTJDSmFwYW5lc2UlMkNFbmdsaXNoJTJDWWVzJTJDRnVsbCUyMGRheSUyMEtpbmRlcmdhcnRlbiUyRlRyYW5zaXRpb25hbCUyMEtpbmRlcmdhcnRlbiUyQ1llcyUwRCUwQVJvc2ElMkMlMkNMZWUlMkMxMiUyRjMxJTJGMjAxNiUyQzFBJTJDR2FsYXh5JTIwS2lkcyUyQ01hbGUlMkMwOCUyRjEwJTJGMjAyMCUyQyUyQ0lyZW5lJTIwJTIwTGVlJTJDbGVhcm5pbmcuaXJlbmVsZWUlNDBnbWFpbC5jb20lMkMlMkMlMkMlMkMlMkNObyUyQ0tvcmVhbiUyQ0VuZ2xpc2glMkNObyUyQ0hlYWQlMjBTdGFydCUyQ1llcyUwRCUwQVNvcGhpYSUyQ0pveSUyQ01hcmtzJTJDMTElMkYxNSUyRjIwMTUlMkMxQiUyQ0dhbGF4eSUyMEtpZHMlMkNGZW1hbGUlMkMwOCUyRjEwJTJGMjAyMCUyQyUyQyUyQyUyQyUyQyUyQyUyQyUyQ05vJTJDV2hpdGUlMkNFbmdsaXNoJTJDTm8lMkNGdWxsJTIwZGF5JTIwS2luZGVyZ2FydGVuJTJGVHJhbnNpdGlvbmFsJTIwS2luZGVyZ2FydGVuJTJDTm8lMEQlMEFXZW5keSUyQ0FsZXhhJTJDTWFydGluZXolMkMwOSUyRjE1JTJGMjAxNSUyQzFBJTJDR2FsYXh5JTIwS2lkcyUyQ0ZlbWFsZSUyQzA5JTJGMTUlMkYyMDIwJTJDJTJDJTJDJTJDJTJDJTJDJTJDJTJDWWVzJTJDT3RoZXIlMkNFbmdsaXNoJTJDWWVzJTJDSGVhZCUyMFN0YXJ0JTJDWWVzJTBEJTBBSmFja3NvbiUyQyUyQ1dhbmclMkMwNSUyRjEwJTJGMjAxNiUyQzFBJTJDR2FsYXh5JTIwS2lkcyUyQ01hbGUlMkMwOCUyRjEwJTJGMjAyMCUyQyUyQyUyQ2hlbnJ5d2FuZyU0MGdtYWlsLmNvbSUyQyUyQyUyQyUyQyUyQ05vJTJDQ2hpbmVzZSUyQ0VuZ2xpc2glMkNObyUyQ0hlYWQlMjBTdGFydCUyQ05vJTBEJTBBJTJDJTJDJTJDJTJDJTJDJTJDJTJDJTJDJTJDJTJDJTJDJTJDJTJDJTJDJTJDJTJDJTJDJTJDJTJDJTJDJTBEJTBBJTJDJTJDJTJDJTJDJTJDJTJDJTJDJTJDJTJDJTJDJTJDJTJDJTJDJTJDJTJDJTJDJTJDJTJDJTJDJTJDJTBEJTBB";
        // 模拟 ImportCheckParamModel
        ImportCheckParamModel toCheckParam = new ImportCheckParamModel();
        toCheckParam.setMappedColumn(mappedColumns);
        toCheckParam.setTimeZone(timeZone);
        toCheckParam.setFileBase64Value(fileBase64Value);

        // 调用方法
        ParseOtherDataRequest parseOtherDataRequest = OtherCSVUtil.parseOther(toCheckParam);
    }

    /**
     * 测试导入关于 other 的 csv 导入
     */
    @Test
    public void testImportCSV_aboutOther() {
        // 封装 mappedColumn
        MappedColumn mappedColumn = new MappedColumn("ChildName (First)", "ChildName (First)", 0);
        MappedColumn mappedColumn1 = new MappedColumn("ChildName (Middle)", "ChildName (Middle)", 1);
        MappedColumn mappedColumn2 = new MappedColumn("ChildName (Last)", "ChildName (Last)", 2);
        MappedColumn mappedColumn3 = new MappedColumn("Birthday", "Birthday", 3);
        MappedColumn mappedColumn4 = new MappedColumn("Class Name", "Class Name", 4);
        MappedColumn mappedColumn5 = new MappedColumn("Site Name", "Center Name", 5);
        MappedColumn mappedColumn6 = new MappedColumn("Site ID", "Site Id", 6);
        MappedColumn mappedColumn7 = new MappedColumn("Gender", "Gender", 7);
        MappedColumn mappedColumn8 = new MappedColumn("Enrollment Date", "Enrollment Date", 8);
        MappedColumn mappedColumn9 = new MappedColumn("Parent1 Name", "Primary Adult First Name", 10);
        MappedColumn mappedColumn10 = new MappedColumn("Parent1 Email", "Primary Adult Email", 11);
        MappedColumn mappedColumn11 = new MappedColumn("Statewide Student Identifier", "Statewide Student Identifier", 14);
        MappedColumn mappedColumn12 = new MappedColumn("External ID", "External ID", 15);
        MappedColumn mappedColumn13 = new MappedColumn("Hispanic", "Hispanic", 16);
        MappedColumn mappedColumn14 = new MappedColumn("Race", "Race", 17);
        MappedColumn mappedColumn15 = new MappedColumn("Language", "Language", 18);
        MappedColumn mappedColumn16 = new MappedColumn("ELD", "ELD", 19);
        MappedColumn mappedColumn17 = new MappedColumn("Program Name", "Program Name", 20);
        MappedColumn mappedColumn18 = new MappedColumn("IEP/IFSP", "IEP/IFSP", 21);
        MappedColumn mappedColumn19 = new MappedColumn("Teacher First Name", "Teacher First Name", 22);
        MappedColumn mappedColumn20 = new MappedColumn("Teacher Last Name", "Teacher Last Name", 23);
        MappedColumn mappedColumn21 = new MappedColumn("Teacher ID", "Teacher ID", 24);
        MappedColumn mappedColumn22 = new MappedColumn("Teacher Email", "Teacher Email", 25);

        List<MappedColumn> mappedColumns = new ArrayList<>();
        mappedColumns.add(mappedColumn);
        mappedColumns.add(mappedColumn1);
        mappedColumns.add(mappedColumn2);
        mappedColumns.add(mappedColumn3);
        mappedColumns.add(mappedColumn4);
        mappedColumns.add(mappedColumn5);
        mappedColumns.add(mappedColumn6);
        mappedColumns.add(mappedColumn7);
        mappedColumns.add(mappedColumn8);
        mappedColumns.add(mappedColumn9);
        mappedColumns.add(mappedColumn10);
        mappedColumns.add(mappedColumn11);
        mappedColumns.add(mappedColumn12);
        mappedColumns.add(mappedColumn13);
        mappedColumns.add(mappedColumn14);
        mappedColumns.add(mappedColumn15);
        mappedColumns.add(mappedColumn16);
        mappedColumns.add(mappedColumn17);
        mappedColumns.add(mappedColumn18);
        mappedColumns.add(mappedColumn19);
        mappedColumns.add(mappedColumn20);
        mappedColumns.add(mappedColumn21);
        mappedColumns.add(mappedColumn22);

        // timeZone
        String timeZone = "Asia/Shanghai";
        // 模拟文件 base64
        String fileBase64Value = "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";
        // 模拟 ImportCheckParamModel
        ImportCheckParamModel toCheckParam = new ImportCheckParamModel();
        toCheckParam.setMappedColumn(mappedColumns);
        toCheckParam.setTimeZone(timeZone);
        toCheckParam.setFileBase64Value(fileBase64Value);

        // 获取 mappedColumns 中的 mappedBy 不为空的字段
        // 定义为 attrs 变量
        List<String> attrs = mappedColumns.stream().map(MappedColumn::getMappedBy).filter(Objects::nonNull).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        // 设置 attrs
        toCheckParam.setAttrs(attrs);
        // 调用方法
        ParseOtherDataRequest parseOtherDataRequest = OtherCSVUtil.parseOther(toCheckParam);
        // 断言 parseOtherDataRequest 不为空
        Assertions.assertNotNull(parseOtherDataRequest);
    }

    @Test
    public void testImportCsv_aboutClassId() {
        // 数据准备
        // 必须字段
        String metaValue = "[{\"column\":\"First Name\",\"mappedBy\":\"ChildName (First)\",\"displayName\":\"学生名称\",\"columnNum\":0},{\"column\":\"Middle Name\",\"mappedBy\":\"ChildName (Middle)\",\"displayName\":\"学生中间名\",\"columnNum\":1},{\"column\":\"Last Name\",\"mappedBy\":\"ChildName (Last)\",\"displayName\":\"学生的姓氏\",\"columnNum\":2},{\"column\":\"Date of Birth\",\"mappedBy\":\"Birthday\",\"displayName\":\"出生日期\",\"columnNum\":3},{\"column\":\"Class Name\",\"mappedBy\":\"Class Name\",\"displayName\":\"班级\",\"columnNum\":4},{\"column\":\"Site Name\",\"mappedBy\":\"Center Name\",\"displayName\":\"学校名称\",\"columnNum\":5},{\"column\":\"Site ID\",\"mappedBy\":\"Site Id\",\"displayName\":\"学校 ID\",\"columnNum\":6},{\"column\":\"Class ID\",\"mappedBy\":\"Class ID\",\"displayName\":\"班级 ID\",\"columnNum\":7},{\"column\":\"Gender\",\"mappedBy\":\"Gender\",\"displayName\":\"性别\",\"columnNum\":8},{\"column\":\"Enrollment Date\",\"mappedBy\":\"Enrollment Date\",\"displayName\":\"入学日期\",\"columnNum\":9},{\"column\":\"Language\",\"mappedBy\":\"Language\",\"displayName\":\"语言\",\"columnNum\":21},{\"column\":\"ELD\",\"mappedBy\":\"ELD\",\"displayName\":\"ELD\",\"columnNum\":22},{\"column\":\"IEP/IFSP\",\"mappedBy\":\"IEP/IFSP\",\"displayName\":\"IEP/IFSP\",\"columnNum\":24},{\"column\":\"Teacher First Name\",\"mappedBy\":\"Teacher First Name\",\"displayName\":\"老师名称\",\"columnNum\":25},{\"column\":\"Teacher Last Name\",\"mappedBy\":\"Teacher Last Name\",\"displayName\":\"老师的姓氏\",\"columnNum\":26},{\"column\":\"Teacher Email\",\"mappedBy\":\"Teacher Email\",\"displayName\":\"老师邮箱/手机号\",\"columnNum\":28}]";
        List<MappedColumn> mappedColumns = Arrays.asList(new Gson().fromJson(metaValue, MappedColumn[].class));
        // timeZone
        String timeZone = "Asia/Shanghai";
        // 模拟文件 base64
        String fileBase64Value = "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";

        // 模拟 ImportCheckParamModel
        ImportCheckParamModel toCheckParam = new ImportCheckParamModel();
        toCheckParam.setMappedColumn(mappedColumns);
        toCheckParam.setTimeZone(timeZone);
        toCheckParam.setFileBase64Value(fileBase64Value);

        // 获取 mappedColumns 中的 mappedBy 不为空的字段
        // 定义为 attrs 变量
        List<String> attrs = mappedColumns.stream().map(MappedColumn::getMappedBy).filter(Objects::nonNull).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        // 设置 attrs
        toCheckParam.setAttrs(attrs);
        // 调用方法
        ParseOtherDataRequest parseOtherDataRequest = OtherCSVUtil.parseOther(toCheckParam);
        // 断言 parseOtherDataRequest 不为空
        Assertions.assertNotNull(parseOtherDataRequest);
    }
}
