package com.learninggenie.api.util;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * 解析 Boolean 值工具类测试类
 */
@RunWith(MockitoJUnitRunner.class)
public class BooleanUtilTest {

    /**
     * 测试 Boolean 解析工具类
     * case: 全部返回 true 的情况
     */
    @Test
    public void testBooleanParser1() {

        // 获取返回值
        Boolean isTrue_1 = BooleanUtil.isTrue("1");
        Boolean isTrue_t = BooleanUtil.isTrue("T");
        Boolean isTrue = BooleanUtil.isTrue("true");
        Boolean isTrue_yes = BooleanUtil.isTrue("yes");
        Boolean isTrue_y = BooleanUtil.isTrue("y");
        Boolean aTrue = BooleanUtil.isTrue(1);

        // 断言
        Assert.assertEquals(true, isTrue_1);
        Assert.assertEquals(true, isTrue_t);
        Assert.assertEquals(true, isTrue);
        Assert.assertEquals(true, isTrue_yes);
        Assert.assertEquals(true, isTrue_y);
        Assert.assertEquals(true, aTrue);
    }

    /**
     * 测试 Boolean 解析工具类
     * case: 全部返回 false 的情况
     */
    @Test
    public void testBooleanParser2() {

        // 获取返回值
        Boolean isTrue_1 = BooleanUtil.isTrue("0");
        Boolean isTrue_t = BooleanUtil.isTrue("F");
        Boolean isTrue = BooleanUtil.isTrue("false");
        Boolean isTrue_yes = BooleanUtil.isTrue("no");
        Boolean isTrue_y = BooleanUtil.isTrue("n");
        Boolean aTrue = BooleanUtil.isTrue(0);

        // 断言
        Assert.assertEquals(false, isTrue_1);
        Assert.assertEquals(false, isTrue_t);
        Assert.assertEquals(false, isTrue);
        Assert.assertEquals(false, isTrue_yes);
        Assert.assertEquals(false, isTrue_y);
        Assert.assertEquals(false, aTrue);
    }

    /**
     * 测试 Boolean 解析工具类
     * case: 部分返回 true, 部分返回 false
     */
    @Test
    public void testBooleanParser3() {

        // 获取返回值
        Boolean isTrue_1 = BooleanUtil.isTrue("1");
        Boolean isTrue_t = BooleanUtil.isTrue("T");
        Boolean isTrue = BooleanUtil.isTrue("true");
        Boolean isTrue_yes = BooleanUtil.isTrue("no");
        Boolean isTrue_y = BooleanUtil.isTrue("n");
        Boolean aTrue = BooleanUtil.isTrue(0);

        // 断言
        Assert.assertEquals(true, isTrue_1);
        Assert.assertEquals(true, isTrue_t);
        Assert.assertEquals(true, isTrue);
        Assert.assertEquals(false, isTrue_yes);
        Assert.assertEquals(false, isTrue_y);
        Assert.assertEquals(false, aTrue);
    }
}
