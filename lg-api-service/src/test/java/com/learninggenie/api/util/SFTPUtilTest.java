package com.learninggenie.api.util;

import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.SftpException;
import com.learninggenie.common.utils.SFTPUtil;
import org.junit.Test;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.InputStream;
import java.util.Vector;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * Created by 韩雪磊 on 2023/8/16.
 * 测试SFTPUtil
 */
@ExtendWith(MockitoExtension.class)
@RunWith(MockitoJUnitRunner.class)
public class SFTPUtilTest {

    private static final String SFTP_HOST = "test.sftp.lgsvc.net";

    private static final String PASSWORD = "yfjxe182";

    private static final String USERNAME = "EX_D89EEF66B0B14A3C8F012C8AD9F4BE93";

    @Mock
    private ChannelSftp channelSftp;

    @InjectMocks
    private SFTPUtil sftp = new SFTPUtil(USERNAME, PASSWORD, SFTP_HOST, 22);

    @BeforeEach
    public void loginSftp() {
        sftp.login();
    }

    @AfterEach
    public void logoutSftp() {
        sftp.logout();
    }

    @Test
    public void testUpload() throws Exception {
        String basePath = "base";
        String directory = "dir";
        String sftpFileName = "file";
        InputStream input = mock(InputStream.class);

        sftp.upload(basePath, directory, sftpFileName, input);

        verify(channelSftp).put(input, sftpFileName);
    }

    @Test
    public void testUploadAndArchive() throws Exception {
        String basePath = "base";
        String directory = "dir";
        String sftpFileName = "file";
        String archiveDirectory = "archive";
        InputStream input = mock(InputStream.class);

        sftp.uploadAndArchive(basePath, directory, sftpFileName, archiveDirectory, input);

        verify(channelSftp).put(input, sftpFileName);
    }

    @Test
    public void testEnterDirectory_existingDirectory() throws Exception {
        String basePath = "base";
        String directory = "dir";

        sftp.enterDirectory(basePath, directory);

        verify(channelSftp).cd(basePath);
        verify(channelSftp).cd(directory);
    }

    @Test
    public void testEnterDirectory_nonExistingDirectory() throws Exception {
        String basePath = "base";
        String directory = "dir/subdir";


        sftp.enterDirectory(basePath, directory);

        verify(channelSftp).cd(basePath);
        verify(channelSftp).cd("dir/subdir");
    }

    @Test
    public void testMoveFiles_existingFile() throws Exception {
        String sourcePath = "source";
        String destinationPath = "destination";

        when(channelSftp.ls(anyString())).thenReturn(new Vector<>());

        sftp.moveFiles(sourcePath, destinationPath);

        verify(channelSftp).ls(sourcePath);
    }

    @Test
    public void testMoveFiles_nonExistingFile() throws Exception {
        String sourcePath = "source";
        String destinationPath = "destination";

        when(channelSftp.ls(anyString())).thenThrow(new SftpException(1, "No such file"));

        sftp.moveFiles(sourcePath, destinationPath);

        verify(channelSftp).ls(sourcePath);
    }

    @Test
    public void testMoveFiles_subdirectory() throws Exception {
        String sourcePath = "source/subdir";
        String destinationPath = "destination/subdir";

        when(channelSftp.ls(eq("source/subdir"))).thenReturn(new Vector<>());

        sftp.moveFiles(sourcePath, destinationPath);

        verify(channelSftp).ls(sourcePath);
    }
}
