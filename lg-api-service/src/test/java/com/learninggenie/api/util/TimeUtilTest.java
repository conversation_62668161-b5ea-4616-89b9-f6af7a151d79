package com.learninggenie.api.util;

import com.learninggenie.common.utils.RateUtil;
import com.learninggenie.common.utils.TimeUtil;
import junit.framework.TestCase;
import org.junit.Test;
import org.junit.jupiter.api.BeforeEach;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.Month;
import java.util.Date;

public class TimeUtilTest extends TestCase {
    @Test
    public void testGetUtcTime(){
        System.out.println(TimeUtil.getUTCTimeStr());
    }

    @Test
    public void testParseDate(){
        String s1 = "02/28/10";
        Date d1 = TimeUtil.parseDate(s1);
        System.out.println(d1);
        String s2 = "02/28/2010";
        Date d2 = TimeUtil.parseDate(s2);
        System.out.println(d2);
        String s3 = "02-28-10";
        Date d3 = TimeUtil.parseDate(s3);
        System.out.println(d3);
        String s4 = "02-28-2010";
        Date d4 = TimeUtil.parseDate(s4);
        System.out.println(d4);
        String s5 = "02-28-2010 10:00:29.948";
        Date d5 = TimeUtil.parseDateTime(s5);
        System.out.println(d5);
        String s6 = "02/28/2010 10:00:29.948";
        Date d6 = TimeUtil.parseDateTime(s6);
        System.out.println(d6);
        String s7 = "02/28/10 10:00:29.948";
        Date d7 = TimeUtil.parseDateTime(s7);
        System.out.println(d7);
        String s8 = "02-28-10 10:00:29.948";
        Date d8 = TimeUtil.parseDateTime(s8);
        System.out.println(d8);

    }

    @Test
    public void test_checkFinalRateTime() {
        Date result = RateUtil.checkFinalRateTime(TimeUtil.parseDateTime("2017-08-27 20:12:22.111"));
        assertTrue(result.getTime() == TimeUtil.parseDateTime("2017-08-25 20:12:22.111").getTime());

        result = RateUtil.checkFinalRateTime(TimeUtil.parseDateTime("2017-11-01 10:12:22.111"));
        assertTrue(result.getTime() == TimeUtil.parseDateTime("2017-11-01 10:12:22.111").getTime());

        result = RateUtil.checkFinalRateTime(TimeUtil.parseDateTime("2017-11-04 10:12:22.111"));
        assertTrue(result.getTime() == TimeUtil.parseDateTime("2017-11-03 10:12:22.111").getTime());

        result = RateUtil.checkFinalRateTime(TimeUtil.parseDateTime("2017-11-02 10:12:22.111"));
        assertTrue(result.getTime() == TimeUtil.parseDateTime("2017-11-02 10:12:22.111").getTime());
    }

    @Test
    public void test_isWeekend() {
        assertTrue(!TimeUtil.isWeekend(TimeUtil.parseDateTime("2017-11-24 20:12:22.111")));
        assertTrue(TimeUtil.isWeekend(TimeUtil.parseDateTime("2017-11-25 20:12:22.111")));
        assertTrue(TimeUtil.isWeekend(TimeUtil.parseDateTime("2017-11-26 20:12:22.111")));
        assertTrue(!TimeUtil.isWeekend(TimeUtil.parseDateTime("2017-11-27 20:12:22.111")));
        assertTrue(!TimeUtil.isWeekend(TimeUtil.parseDateTime("2017-11-28 20:12:22.111")));
    }

    // Example date to be used in tests
    private LocalDate testDate;
    private LocalDate date;
    @BeforeEach
    public void setUp() {
        testDate = LocalDate.of(2023, 4, 5);
        // 初始化测试数据
        date = LocalDate.of(2021, Month.JANUARY, 1);
    }

    @Test
    public void testGetFirstWeekDateReturnsCorrectSunday() {
        // Sunday, April 2, 2023
        LocalDate expectedSunday = LocalDate.of(2023, 4, 2);
        LocalDate result = TimeUtil.getFirstWeekDate(testDate);
        assertEquals(expectedSunday, result);
    }

    @Test
    public void testGetFirstWeekDateWhenDateIsSunday() {
        // Assuming testDate is set to a Sunday
        LocalDate sundayDate = LocalDate.of(2023, 4, 9); // Sunday, April 9, 2023
        LocalDate expectedSunday = LocalDate.of(2023, 4, 9);
        LocalDate result = TimeUtil.getFirstWeekDate(sundayDate);
        assertEquals(expectedSunday, result);
    }

    @Test
    public void testGetFirstWeekDateAtStartOfMonth() {
        // Monday, April 3, 2023 (assuming to be the closest Monday to the start of month)
        LocalDate startDateOfMonth = LocalDate.of(2023, 4, 3);
        LocalDate expectedSunday = LocalDate.of(2023, 4, 2); // Sunday, April 2, 2023
        LocalDate result = TimeUtil.getFirstWeekDate(startDateOfMonth);
        assertEquals(expectedSunday, result);
    }

    @Test
    public void testGetFirstWeekDateInFirstWeekOfMonth() {
        // Assuming testDate is in the first week of the month
        LocalDate dateInFirstWeek = LocalDate.of(2023, 4, 1); // Saturday, April 1, 2023
        LocalDate expectedSunday = LocalDate.of(2023, 3, 26);
        LocalDate result = TimeUtil.getFirstWeekDate(dateInFirstWeek);
        assertEquals(expectedSunday, result);
    }

    @Test
    public void testGetFirstWeekDateForLeapYear() {
        // Assuming testDate is set to a date in a leap year
        LocalDate leapYearDate = LocalDate.of(2024, 2, 15); // Thursday, February 15, 2024
        LocalDate expectedSunday = LocalDate.of(2024, 2, 11); // Sunday, February 11, 2024
        LocalDate result = TimeUtil.getFirstWeekDate(leapYearDate);
        assertEquals(expectedSunday, result);
    }

    @Test
    public void testGetFinalWeekDate_ReturnsSaturdayOfSameMonth() {
        // Arrange
        LocalDate expected = LocalDate.of(2021, Month.JANUARY, 2);

        // Act
        LocalDate actual = TimeUtil.getFinalWeekDate(date);

        // Assert
        assertEquals(expected, actual);
    }

    @Test
    public void testGetFinalWeekDate_ReturnsSaturdayOfNextMonth() {
        // Arrange
        LocalDate dateInMiddleOfMonth = LocalDate.of(2021, Month.FEBRUARY, 15);
        LocalDate expected = LocalDate.of(2021, Month.FEBRUARY, 20);

        // Act
        LocalDate actual = TimeUtil.getFinalWeekDate(dateInMiddleOfMonth);

        // Assert
        assertEquals(expected, actual);
    }

    @Test
    public void testGetFinalWeekDate_WhenDateIsAlreadySaturday() {
        // Arrange
        LocalDate saturdayDate = LocalDate.of(2021, Month.JANUARY, 2);

        // Act
        LocalDate actual = TimeUtil.getFinalWeekDate(saturdayDate);

        // Assert
        assertEquals(saturdayDate, actual);
    }

}
