package com.learninggenie.api.util;

import com.learninggenie.api.model.ChildPlusData;
import com.learninggenie.api.model.importdata.ImportStructure;
import com.learninggenie.common.data.model.ImportEnrollmentModel;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * child plus 工具类测试
 */
@RunWith(MockitoJUnitRunner.class)
public class ChildPlusCSVUtilTest {

    /**
     * 测试解析 csv
     */
    @Test
    public void testParseCSV() {
        // 数据准备
        // 导入内容
        String content = "QWdlbmN5IE5hbWUsU2l0ZSBOYW1lLENsYXNzIE5hbWUsQ2hpbGROYW1lIChMYXN0KSxDaGlsZE5hbWUgKEZpcnN0KSxCaXJ0aGRheSxDaGlsZFBsdXMgSUQsSGlzcGFuaWMsR2VuZGVyIENvZGUsUmFjZSxSYWNlIEFtZXJpY2FuIEluZGlhbiBPciBBbGFza2FuIE5hdGl2ZSxSYWNlIEFzaWFuLFJhY2UgQmxhY2sgb3IgQWZyaWNhbiBBbWVyaWNhbiwsUmFjZSBIaXNwYW5pYyxSYWNlIE11bHRpLVJhY2lhbCxSYWNlIE5hdGl2ZSBIYXdhaWlhbiBvciBPdGhlciBQYWNpZmljIElzbGFuZGVyLCxSYWNlIE90aGVyLFJhY2UgT3RoZXIgRGVzY3JpcHRpb24sUmFjZSBXaGl0ZSxFbnJvbGxtZW50IFN0YXR1cyxFbnJvbGxlZCBEYXRlLElFUCBEaXNhYmlsaXR5IENvZGUgRGVzY3JpcHRpb24sUHJvZ3JhbSBOYW1lLFBhcnRpY2lwYXRpb25ZZWFyLFByaW1hcnkgTGFuZ3VhZ2UsSG9tZSBMYW5ndWFnZSxQcmltYXJ5IEFkdWx0IEZpcnN0IE5hbWUsUHJpbWFyeSBBZHVsdCBMYXN0IE5hbWUsUHJpbWFyeSBBZHVsdCBFbWFpbCxTZWNvbmRhcnkgQWR1bHQgRmlyc3QgTmFtZSxTZWNvbmRhcnkgQWR1bHQgTGFzdCBOYW1lLFNlY29uZGFyeSBBZHVsdCBFbWFpbA0KTGVhcm5pbmcgR2VuaWUgQWNhZGVteSxDYXJsc2JhZCxIUyBQTSAxLFN0dWRlbnQsT25lLDExLzE5LzIwMTUsMzcyNzIyLE5vLE0sT3RoZXIsMCwwLDAsLDAsMCwwLCwxLEhpc3BhbmljLDAsRW5yb2xsZWQsOC85LzIwMTcsLEhlYWQgU3RhcnQsMSwsRW5nbGlzaCwsLCwsLA0K";
        // timeZone
        String timeZone = "Asia/Shanghai";
        // stageId
        String stageId = UUID.randomUUID().toString();
        // needEntryDate
        boolean needEntryDate = false;
        // 导入类型
        String resource = "childPlus";
        // 机构 Id
        String agencyId = UUID.randomUUID().toString();

        // 方法调用
        ImportStructure importStructure = ChildPlusCSVUtil.parseCSV(content, timeZone, stageId, needEntryDate, resource, agencyId);

        // 验证结果
        assert importStructure.getCenters().size() == 1;
    }

    /**
     * 测试解析 csv 过滤重复小孩逻辑
     */
    @Test
    public void testParseCSVFilterDuplicateChildren() {
        // 数据准备
        // 导入内容 学校数 1，班级数 1，孩子数 3 其中存在两个重复小孩
        String content = "QWdlbmN5IE5hbWUsU2l0ZSBOYW1lLENsYXNzIE5hbWUsQ2hpbGROYW1lIChMYXN0KSxDaGlsZE5hbWUgKEZpcnN0KSxCaXJ0aGRheSxDaGlsZFBsdXMgSUQsSGlzcGFuaWMsR2VuZGVyIENvZGUsUmFjZSxSYWNlIEFtZXJpY2FuIEluZGlhbiBPciBBbGFza2FuIE5hdGl2ZSxSYWNlIEFzaWFuLFJhY2UgQmxhY2sgb3IgQWZyaWNhbiBBbWVyaWNhbiwsUmFjZSBIaXNwYW5pYyxSYWNlIE11bHRpLVJhY2lhbCxSYWNlIE5hdGl2ZSBIYXdhaWlhbiBvciBPdGhlciBQYWNpZmljIElzbGFuZGVyLCxSYWNlIE90aGVyLFJhY2UgT3RoZXIgRGVzY3JpcHRpb24sUmFjZSBXaGl0ZSxFbnJvbGxtZW50IFN0YXR1cyxFbnJvbGxlZCBEYXRlLElFUCBEaXNhYmlsaXR5IENvZGUgRGVzY3JpcHRpb24sUHJvZ3JhbSBOYW1lLFBhcnRpY2lwYXRpb25ZZWFyLFByaW1hcnkgTGFuZ3VhZ2UsSG9tZSBMYW5ndWFnZSxQcmltYXJ5IEFkdWx0IEZpcnN0IE5hbWUsUHJpbWFyeSBBZHVsdCBMYXN0IE5hbWUsUHJpbWFyeSBBZHVsdCBFbWFpbCxTZWNvbmRhcnkgQWR1bHQgRmlyc3QgTmFtZSxTZWNvbmRhcnkgQWR1bHQgTGFzdCBOYW1lLFNlY29uZGFyeSBBZHVsdCBFbWFpbA0KTGVhcm5pbmcgR2VuaWUgQWNhZGVteSxDYXJsc2JhZCxIUyBQTSAxLFN0dWRlbnQsT25lLDExLzE5LzIwMTUsMzcyNzIxLE5vLE0sT3RoZXIsMCwwLDAsLDAsMCwwLCwxLEhpc3BhbmljLDAsRW5yb2xsZWQsOC85LzIwMTcsLEhlYWQgU3RhcnQsMSwsRW5nbGlzaCwsLCwsLA0KTGVhcm5pbmcgR2VuaWUgQWNhZGVteSxDYXJsc2JhZCxIUyBQTSAxLFN0dWRlbnQsT25lLDExLzE5LzIwMTUsLE5vLE0sT3RoZXIsMCwwLDAsLDAsMCwwLCwxLEhpc3BhbmljLDAsRW5yb2xsZWQsOC85LzIwMTcsLEhlYWQgU3RhcnQsMSwsRW5nbGlzaCwsLCwsLA0KTGVhcm5pbmcgR2VuaWUgQWNhZGVteSxDYXJsc2JhZCxIUyBQTSAxLFN0dWRlbnQsT25lLDExLzE5LzIwMTUsMzcyNzIzLE5vLE0sT3RoZXIsMCwwLDAsLDAsMCwwLCwxLEhpc3BhbmljLDAsRW5yb2xsZWQsOC85LzIwMTcsLEhlYWQgU3RhcnQsMSwsRW5nbGlzaCwsLCwsLA0K";
        // timeZone
        String timeZone = "Asia/Shanghai";
        // stageId
        String stageId = UUID.randomUUID().toString();
        // needEntryDate
        boolean needEntryDate = false;
        // 导入类型
        String resource = "childPlus";
        // 机构 Id
        String agencyId = UUID.randomUUID().toString();
        // 方法调用
        ImportStructure importStructure = ChildPlusCSVUtil.parseCSV(content, timeZone, stageId, needEntryDate, resource, agencyId);
        // 验证结果
        assert importStructure.getCenters().size() == 1; // 验证学校数量
        assert importStructure.getCenters().get(0).getGroups().size() == 1; // 验证班级数量
        assert importStructure.getCenters().get(0).getGroups().get(0).getChildren().size() == 2; // 验证过滤后孩子数量
    }

    /**
     * 测试解析 csv3
     */
    @Test
    public void testParseCSV3() {
        // 数据准备
        // 导入小孩模型列表
        List<ImportEnrollmentModel> importEnrollmentModels = new ArrayList<>();
        // 导入内容
        String content = "QWdlbmN5IE5hbWUsU2l0ZSBOYW1lLENsYXNzIE5hbWUsQ2hpbGROYW1lIChMYXN0KSxDaGlsZE5hbWUgKEZpcnN0KSxCaXJ0aGRheSxDaGlsZFBsdXMgSUQsSGlzcGFuaWMsR2VuZGVyIENvZGUsUmFjZSxSYWNlIEFtZXJpY2FuIEluZGlhbiBPciBBbGFza2FuIE5hdGl2ZSxSYWNlIEFzaWFuLFJhY2UgQmxhY2sgb3IgQWZyaWNhbiBBbWVyaWNhbiwsUmFjZSBIaXNwYW5pYyxSYWNlIE11bHRpLVJhY2lhbCxSYWNlIE5hdGl2ZSBIYXdhaWlhbiBvciBPdGhlciBQYWNpZmljIElzbGFuZGVyLCxSYWNlIE90aGVyLFJhY2UgT3RoZXIgRGVzY3JpcHRpb24sUmFjZSBXaGl0ZSxFbnJvbGxtZW50IFN0YXR1cyxFbnJvbGxlZCBEYXRlLElFUCBEaXNhYmlsaXR5IENvZGUgRGVzY3JpcHRpb24sUHJvZ3JhbSBOYW1lLFBhcnRpY2lwYXRpb25ZZWFyLFByaW1hcnkgTGFuZ3VhZ2UsSG9tZSBMYW5ndWFnZSxQcmltYXJ5IEFkdWx0IEZpcnN0IE5hbWUsUHJpbWFyeSBBZHVsdCBMYXN0IE5hbWUsUHJpbWFyeSBBZHVsdCBFbWFpbCxTZWNvbmRhcnkgQWR1bHQgRmlyc3QgTmFtZSxTZWNvbmRhcnkgQWR1bHQgTGFzdCBOYW1lLFNlY29uZGFyeSBBZHVsdCBFbWFpbA0KTGVhcm5pbmcgR2VuaWUgQWNhZGVteSxDYXJsc2JhZCxIUyBQTSAxLFN0dWRlbnQsT25lLDExLzE5LzIwMTUsMzcyNzIyLE5vLE0sT3RoZXIsMCwwLDAsLDAsMCwwLCwxLEhpc3BhbmljLDAsRW5yb2xsZWQsOC85LzIwMTcsLEhlYWQgU3RhcnQsMSwsRW5nbGlzaCwsLCwsLA0K";
        // timeZone
        String timeZone = "Asia/Shanghai";
        // stageId
        String stageId = UUID.randomUUID().toString();
        // needEntryDate
        boolean needEntryDate = false;
        // 导入类型
        String resource = "childPlus";
        // 机构 Id
        String agencyId = UUID.randomUUID().toString();

        // 方法调用
        ImportStructure importStructure = ChildPlusCSVUtil.parseCSV3(importEnrollmentModels, content, timeZone, stageId, needEntryDate, resource, agencyId);

        // 验证结果
        assert importStructure.getCenters().size() == 1;
    }

    /**
     * 测试解析 csv3 过滤重复小孩逻辑
     */
    @Test
    public void testParseCSV3FilterDuplicateChildren() {
        // 数据准备
        // 导入小孩模型列表
        List<ImportEnrollmentModel> importEnrollmentModels = new ArrayList<>();
        // 导入内容 学校数 1，班级数 1，孩子数 3 其中存在两个重复小孩
        String content = "QWdlbmN5IE5hbWUsU2l0ZSBOYW1lLENsYXNzIE5hbWUsQ2hpbGROYW1lIChMYXN0KSxDaGlsZE5hbWUgKEZpcnN0KSxCaXJ0aGRheSxDaGlsZFBsdXMgSUQsSGlzcGFuaWMsR2VuZGVyIENvZGUsUmFjZSxSYWNlIEFtZXJpY2FuIEluZGlhbiBPciBBbGFza2FuIE5hdGl2ZSxSYWNlIEFzaWFuLFJhY2UgQmxhY2sgb3IgQWZyaWNhbiBBbWVyaWNhbiwsUmFjZSBIaXNwYW5pYyxSYWNlIE11bHRpLVJhY2lhbCxSYWNlIE5hdGl2ZSBIYXdhaWlhbiBvciBPdGhlciBQYWNpZmljIElzbGFuZGVyLCxSYWNlIE90aGVyLFJhY2UgT3RoZXIgRGVzY3JpcHRpb24sUmFjZSBXaGl0ZSxFbnJvbGxtZW50IFN0YXR1cyxFbnJvbGxlZCBEYXRlLElFUCBEaXNhYmlsaXR5IENvZGUgRGVzY3JpcHRpb24sUHJvZ3JhbSBOYW1lLFBhcnRpY2lwYXRpb25ZZWFyLFByaW1hcnkgTGFuZ3VhZ2UsSG9tZSBMYW5ndWFnZSxQcmltYXJ5IEFkdWx0IEZpcnN0IE5hbWUsUHJpbWFyeSBBZHVsdCBMYXN0IE5hbWUsUHJpbWFyeSBBZHVsdCBFbWFpbCxTZWNvbmRhcnkgQWR1bHQgRmlyc3QgTmFtZSxTZWNvbmRhcnkgQWR1bHQgTGFzdCBOYW1lLFNlY29uZGFyeSBBZHVsdCBFbWFpbA0KTGVhcm5pbmcgR2VuaWUgQWNhZGVteSxDYXJsc2JhZCxIUyBQTSAxLFN0dWRlbnQsT25lLDExLzE5LzIwMTUsMzcyNzIxLE5vLE0sT3RoZXIsMCwwLDAsLDAsMCwwLCwxLEhpc3BhbmljLDAsRW5yb2xsZWQsOC85LzIwMTcsLEhlYWQgU3RhcnQsMSwsRW5nbGlzaCwsLCwsLA0KTGVhcm5pbmcgR2VuaWUgQWNhZGVteSxDYXJsc2JhZCxIUyBQTSAxLFN0dWRlbnQsT25lLDExLzE5LzIwMTUsLE5vLE0sT3RoZXIsMCwwLDAsLDAsMCwwLCwxLEhpc3BhbmljLDAsRW5yb2xsZWQsOC85LzIwMTcsLEhlYWQgU3RhcnQsMSwsRW5nbGlzaCwsLCwsLA0KTGVhcm5pbmcgR2VuaWUgQWNhZGVteSxDYXJsc2JhZCxIUyBQTSAxLFN0dWRlbnQsT25lLDExLzE5LzIwMTUsMzcyNzIzLE5vLE0sT3RoZXIsMCwwLDAsLDAsMCwwLCwxLEhpc3BhbmljLDAsRW5yb2xsZWQsOC85LzIwMTcsLEhlYWQgU3RhcnQsMSwsRW5nbGlzaCwsLCwsLA0K";
        // timeZone
        String timeZone = "Asia/Shanghai";
        // stageId
        String stageId = UUID.randomUUID().toString();
        // needEntryDate
        boolean needEntryDate = false;
        // 导入类型
        String resource = "childPlus";
        // 机构 Id
        String agencyId = UUID.randomUUID().toString();
        // 方法调用
        ImportStructure importStructure = ChildPlusCSVUtil.parseCSV3(importEnrollmentModels, content, timeZone, stageId, needEntryDate, resource, agencyId);
        // 验证结果
        assert importStructure.getCenters().size() == 1; // 验证学校数量
        assert importStructure.getCenters().get(0).getGroups().size() == 1; // 验证班级数量
        assert importStructure.getCenters().get(0).getGroups().get(0).getChildren().size() == 2; // 验证过滤后孩子数量
    }

    /**
     * 测试解析
     */
    @Test
    public void testParse() throws Exception {
        // 数据准备
        // 导入内容
        String content = "QWdlbmN5IE5hbWUsU2l0ZSBOYW1lLENsYXNzIE5hbWUsQ2hpbGROYW1lIChMYXN0KSxDaGlsZE5hbWUgKEZpcnN0KSxCaXJ0aGRheSxDaGlsZFBsdXMgSUQsSGlzcGFuaWMsR2VuZGVyIENvZGUsUmFjZSxSYWNlIEFtZXJpY2FuIEluZGlhbiBPciBBbGFza2FuIE5hdGl2ZSxSYWNlIEFzaWFuLFJhY2UgQmxhY2sgb3IgQWZyaWNhbiBBbWVyaWNhbiwsUmFjZSBIaXNwYW5pYyxSYWNlIE11bHRpLVJhY2lhbCxSYWNlIE5hdGl2ZSBIYXdhaWlhbiBvciBPdGhlciBQYWNpZmljIElzbGFuZGVyLCxSYWNlIE90aGVyLFJhY2UgT3RoZXIgRGVzY3JpcHRpb24sUmFjZSBXaGl0ZSxFbnJvbGxtZW50IFN0YXR1cyxFbnJvbGxlZCBEYXRlLElFUCBEaXNhYmlsaXR5IENvZGUgRGVzY3JpcHRpb24sUHJvZ3JhbSBOYW1lLFBhcnRpY2lwYXRpb25ZZWFyLFByaW1hcnkgTGFuZ3VhZ2UsSG9tZSBMYW5ndWFnZSxQcmltYXJ5IEFkdWx0IEZpcnN0IE5hbWUsUHJpbWFyeSBBZHVsdCBMYXN0IE5hbWUsUHJpbWFyeSBBZHVsdCBFbWFpbCxTZWNvbmRhcnkgQWR1bHQgRmlyc3QgTmFtZSxTZWNvbmRhcnkgQWR1bHQgTGFzdCBOYW1lLFNlY29uZGFyeSBBZHVsdCBFbWFpbA0KTGVhcm5pbmcgR2VuaWUgQWNhZGVteSxDYXJsc2JhZCxIUyBQTSAxLFN0dWRlbnQsT25lLDExLzE5LzIwMTUsMzcyNzIyLE5vLE0sT3RoZXIsMCwwLDAsLDAsMCwwLCwxLEhpc3BhbmljLDAsRW5yb2xsZWQsOC85LzIwMTcsLEhlYWQgU3RhcnQsMSwsRW5nbGlzaCwsLCwsLA0K";
        // timeZone
        String timeZone = "Asia/Shanghai";
        // stageId
        String stageId = UUID.randomUUID().toString();
        // needEntryDate
        boolean needEntryDate = false;

        // 方法调用
        ChildPlusData parse = ChildPlusCSVUtil.parse(content, timeZone, stageId, needEntryDate);

        // 验证结果
        assert parse.getCenters().size() == 1;
    }
}
