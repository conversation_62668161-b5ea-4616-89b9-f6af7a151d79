package com.learninggenie.api.util;

import com.learninggenie.api.model.ImportDataViewModel;
import com.learninggenie.api.model.importdata.ImportStructure;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.util.Base64Utils;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.UUID;

/**
 * 导入 track excel 工具类测试
 */
@RunWith(MockitoJUnitRunner.class)
public class ImportTrackExcelUtilTest {

    /**
     * 测试解析  track excel
     */
    @Test
    public void testParse() throws IOException {
        // 数据准备
        // 导入内容
        String importContent = "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";
        InputStream inputStream = new ByteArrayInputStream(Base64Utils.decodeFromString(importContent));
        // excel 后缀
        String excelSuffix = "xlsx";
        // timeZone
        String timeZone = "Asia/Shanghai";
        // stageId
        String stageId = UUID.randomUUID().toString();
        // needEntryDate
        boolean needEntryDate = false;

        // 调用方法
        ImportDataViewModel parse = ImportTrackExcelUtil.parse(inputStream, excelSuffix, timeZone, stageId, needEntryDate);

        // 验证结果
        assert parse.getCenters().size() == 2;
    }

    /**
     * 测试解析 v2 版本的 track excel
     */
    @Test
    public void testParse_V2() throws IOException {
        // 数据准备
        // 导入内容
        String importContent = "UEsDBAoAAAAAAIdO4kAAAAAAAAAAAAAAAAAJAAAAZG9jUHJvcHMvUEsDBBQAAAAIAIdO4kAUNQ70PAEAAEkCAAAQAAAAZG9jUHJvcHMvYXBwLnhtbJ2SwUoDMRRF94L/ELJv0xYRKTMpQhU3YinVfcy8sYFMEvJeh9ZvceNC8A9c+TcKfoaZGdCpunJ3X+7l5ryQbLatLKshovEu5+PhiDNw2hfG3eX8enU+OOEMSblCWe8g5ztAPpOHB9ki+gCRDCBLFQ5zviYKUyFQr6FSOEy2S07pY6UojfFO+LI0GuZebypwJCaj0bGALYEroBiEr0LeNU5r+m9p4XXDhzerXUjAMjsNwRqtKG0pL42OHn1J7GyrwWaib2YXoJrlF8pElFlN0xo0+cjQ3Kf1J5zdKoSmNue1ikY5SvVNrBtabQNSlO8vT2+vDx+Pz5lIfnfWyn60r82RHLeBJPaDTUHHkYx9wpUhC3hVLlSkP4DHfeCWocPtcOaAJkKxBNxYwqVHgjiY/OJtXyDd/OMu8f0H5CdQSwMEFAAAAAgAh07iQIi2tYVJAQAAYwIAABEAAABkb2NQcm9wcy9jb3JlLnhtbH2SX0vDMBTF3wW/Q8l7m7Qb2wxth3/YkwPBieJbSO62YJOGJNrt25u2W+1QhLzknnN/OfeSfHlQVfQF1slaFyhNCIpA81pIvSvQy2YVL1DkPNOCVbWGAh3BoWV5fZVzQ3lt4cnWBqyX4KJA0o5yU6C994Zi7PgeFHNJcOggbmurmA9Xu8OG8Q+2A5wRMsMKPBPMM9wCYzMQ0Qkp+IA0n7bqAIJjqECB9g6nSYp/vB6scn82dMrIqaQ/mjDTKe6YLXgvDu6Dk4OxaZqkmXQxQv4Uv60fn7tRY6nbXXFAZS445RaYr235zCzb53hUabdXMefXYdFbCeLuWN4KJbV03rYtOf5tCMRugB4LIgqRaD/AWXmd3D9sVqjMSLqIyTwmsw2ZUULCeW/fv+hvI/YFdUrxLzGbxOSmI2Z0OqfTxYh4BpRd7stvUX4DUEsDBBQAAAAIAIdO4kBYiU5sKgEAABECAAATAAAAZG9jUHJvcHMvY3VzdG9tLnhtbKWRTU+DQBCG7yb+B7J32A+6FhqgARYS40ETa6+GwNKSsLtkd6k2xv/uNrUaD170OHknzzwzk6xfxegduDaDkinAAQIel63qBrlLwdOm9iPgGdvIrhmV5Ck4cgPW2fVV8qDVxLUduPEcQpoU7K2dVhCads9FYwIXS5f0SovGulLvoOr7oeVMtbPg0kKC0A1sZ2OV8KcvHDjzVgf7V2Sn2pOd2W6Ok9PNkk/40euFHboUvDFaMkYR9UkVlz5GuPDjMF76KEKIFKSs47x6B950aibAk41wq9893jtsN7e2mIex23Lt0Ae7GqcXY3VGECU+JoG7YYApJiiB32ECLw7/tAkvNrfl9sf4uqyiguYsCpdkEZIoWpQ5q6ua0QjnRbx4xuQ3IXi61vmX2QdQSwMECgAAAAAAh07iQAAAAAAAAAAAAAAAAAMAAAB4bC9QSwMECgAAAAAAh07iQAAAAAAAAAAAAAAAAA4AAAB4bC93b3Jrc2hlZXRzL1BLAwQUAAAACACHTuJAcNIREeAFAADTGQAAGAAAAHhsL3dvcmtzaGVldHMvc2hlZXQxLnhtbI2ZXXOiShCG70/V+Q8U9wvCgNGUurXRNd+bnM35uCY4RiogHiAx+++3ZwDpHukke7Hi+9LvzPQ8gJrJ17cstV5lUSb5dmp7zsC25DbOV8n2aWr/8/fyy8i2yirarqI038qp/UuW9tfZn39M9nnxXG6krCxI2JZTe1NVu1PXLeONzKLSyXdyC846L7KogrfFk1vuChmtdFGWuv5gMHSzKNnadcJp8ZmMfL1OYrnI45dMbqs6pJBpVMH8y02yK9u0t9Wn8lZFtIe1tvNBU1zUziHPC47mlyVxkZf5unLiPHPrqR2vcuyOyTqz+Ciop1lZVDy/7L5A8A4W95ikSfVLL7edkKy6nP1+7+x3pRNvm1mgBnknrqzmL2WVZ4uoiuzZRO/AfeHOJqsEuqi23irkemp/804fwtAGQ5/ybyL3JTq2qujxQaYyruQKWLEtxcBjnj+rEy9BGqhwfYKKjOIqeZVzmaZT+8obA0f/61HUMQzhHsbAx+14S83NfWGt5Dp6Sat5nv6XrKrN1B45o5PQbvWf+f5CJk+bCiYkHD33OE8hBf63sgSY9m0ri970675O8MaOUBGxbkoT66k5HYpEUwSvbdGJ4/nvF4GrR4LXpsj3nA8GOmlq4LUdaOAMPxgIeqkHgtd2oEHdlXeW5LVrUgftWMG7XfDA1gOpg3ak4Yd98NpGqANU5g0P/2C570112I4LB22A//EK22Z6XTf9kSMOow6HH4wLN7t6vXDQjjtyws9PvN0ZhXsbEDqqD8fLdWtQ9WWgrszZpMj3Fty14MrSPAeO4qDcRer26p1CJhCqTvgGZwDwJbx/nQ0m7itcQXHjnWHPo94cez71FtgT1PuOvYB6S+yF1DvH3pB6F9g7od4l9kbUu8LemHrX2POMxtwQ0+jMLTGN1vwAE3YAbiSHpntGh+56TjEadd9zitGvv3pOMdr2s+cUo3sPPad0TXQBsgNpsKQOr+EBL7JSo8dn2PSNHs+JafR4QUyjx9+JaXR3SUyjr+dgqutDXwyBFwqjYxe+dvwgMFZySVKNrbgiphF5TUyj/TfE7Bqvr9xbMLvJCmE28AcpNiZ8h01htP6+Thb60Yr3GO723R6Hhz0GuaPZGOgMm8LYxjk2fcNcENPcY2wKw1wS0wDgHMyubd7IvPwuRL3HcKund4RLkmqQc4VN36i8xqYw6LjBpm/uMZjdZHv2GBebtN4Rs0OLXLbwNG629OQUnubNnf9cyepCUB9k1GPiFgmkvnuqkHolk3okkHp4KveNr2RSjwRSD0/hvnolk3okkHr1naR+KpL5K5nUI4HUw8O5r17JpB4JpN6Db0l9AVonCVihEeom3bMGr755o03ECo1QV3xfRH0nwBFIoREK1b6IFuEDSupDY9scGsHAqD4ytgU1jVihEQyP6mOTEYEUGsEg6SEEm1kghUYwVKoPk8YskEIjGDA9BGIzC6TQCIZN/f2JosXR6TN0ap1EYIXMwmfo1DqNQLzSCIZOH7FY9wIrNIKh00csNhFIoREMnf4RnVihEQyd+rM57QVHp8/QqXUawdHpM3RqnUZwdPoMnVqnERydPkOn1mkER6dg6NQ6icAK2RHB0Kl1GsHRKRg6tU4jEK90FgydArFY04kVGsHQKY7oxAqNYOgUiMVmFkihEQydArHYRCCFRjB0CsRiE4EUGsHQKRCLTQRSaARDp0AsNhFIIREBQ6fWCRdYoREMnQFisZ4FVmgEQ2eAWGwikEIjGDqDIzqxQiMYOoMjOrFCIxg6A8RisxCk0AiGzgCx2EQghUYwdAaIxSYCKTSCoTNALDYRSKERDJ0BYrGJQAqJCBk6tU7oxAqNYOgMj+jECo1g6AwRi/VCsEIjGDrDIzqxQiMYOsMjOrFCIxg64Sdw4xMfVuqI+tfr+me7XfQkb6PiKdmWVirX8LUafsIdj8UgbP8BmUX9I3WPU+U7VaF+KH7MK/iZvn23gb+WSPgeN3CgLes8r9o38Km89pZahLfu4c8xs99QSwMECgAAAAAAh07iQAAAAAAAAAAAAAAAAAkAAAB4bC90aGVtZS9QSwMEFAAAAAgAh07iQBGNcDeHBgAAkhsAABMAAAB4bC90aGVtZS90aGVtZTEueG1s7VlPbxtFFL8j8R1Ge29tJ3YaR3Wq2LEbaNNGsVvU43g93p16dmc1M07qG2qPSEiIgnpBQlw4IKBSK4FE+TQpRaVI/Qq8mdld78RrkpQIBNSHZHfmN+//e/Nm9vKVexFDB0RIyuOWV7tY9RCJfT6icdDybg16F9Y9JBWOR5jxmLS8GZHelc1337mMN1RIIoJgfSw3cMsLlUo2KhXpwzCWF3lCYpgbcxFhBa8iqIwEPgS6EausVKtrlQjT2EMxjoDszfGY+gQ9//Gnl1898jYz6l0GLGIl9YDPRF/TJs4Sgx1NahohZ7LDBDrArOUBoxE/HJB7ykMMSwUTLa9qfl5l83IFb6SLmFqytrCuZ37punTBaLJieIpgmDOt9erNS9s5fQNgahHX7XY73VpOzwCw74OmVpYizXpvvdbOaBZA9nGRdqfaqNZdfIH+6oLMzXa73WimsliiBmQf6wv49epafWvFwRuQxTcW8PX2Vqez5uANyOLXFvC9S821uos3oJDReLKA1g7t9VLqOWTM2U4pfB3g69UUPkdBNOTRpVmMeayWxVqE73LRA4AGMqxojNQsIWPsQxh3cDQUFGsGeIPgwowd8uXCkOaFpC9oolre+wmGlJjTe/3s29fPnqDXzx4f3X96dP+HowcPju5/b2k5C3dwHBQXvvr6k9+/+BD99uTLVw8/K8fLIv6X7z56/vOn5UDIoLlELz5//OvTxy8effzym4cl8C2Bh0X4gEZEohvkEO3zCHQzhnElJ0NxthWDEFNnBQ6Bdgnprgod4I0ZZmW4NnGNd1tA8SgDXp3edWTth2KqaAnna2HkAHc5Z20uSg1wTfMqWHgwjYNy5mJaxO1jfFDGu4Njx7XdaQJVMwtKx/adkDhi7jEcKxyQmCik5/iEkBLt7lDq2HWX+oJLPlboDkVtTEtNMqBDJ5Dmi3ZoBH6ZlekMrnZss3sbtTkr03qbHLhISAjMSoQfEOaY8SqeKhyVkRzgiBUNfh2rsEzI/kz4RVxXKvB0QBhH3RGRsmzNTQH6Fpx+DUO9KnX7LptFLlIoOimjeR1zXkRu80knxFFShu3TOCxi35MTCFGM9rgqg+9yN0P0O/gBx0vdfZsSx90nF4JbNHBEmgeInpmKEl9eJdyJ3/6MjTExVQZKulOpIxr/WdlmFOq25fC2bLe8LdjEypJn51ixXob7F5bobTyN9whkxeIW9bZCv63Q3n++Qi/L5fOvy/NSDFVaNyS21zadd7S08R5Txvpqxsh1aXpvCRvQqAeDep05dZL8IJaE8KgzGRg4uEBgswYJrj6gKuyHOIG+veZpIoFMSQcSJVzCedEMl9LWeOj9lT1tNvQ5xFYOidUuH9nhVT2cHTdyMkaqwJxpM0armsBpma1eSomCbm/CrKaFOjW3mhHNFEWHW66yNrE5l4PJc9VgMLcmdDYI+iGw8hqc+zVrOO9gRkba7tZHmVuMF87TRTLEI5L6SOu96KOacVIWKwuKaD1sMOiz4wlWK3BrarJ/gdtpnFRkV1/CLvPeX/FSFsFzLwG14+nI4mJyshgdtrxmY6XhIR8nLW8MR2V4jBLwutTNJGYBXDj5StiwPzGZTZbPvdnMFHOToAa3H9buCwo7dSARUm1jGdrQMFNpCLBYc7LyrzTArOelQEk1Op0Uq+sQDP+YFGBH17VkPCa+Kjq7MKJtZ1/TUsqnioh+ODpEQzYV+xjcr0MV9BlRCTcepiLoF7ie09Y2U25xTpOueClmcHYcsyTEabnVKZplsoWbgpTLYN4K4oFupbIb5c6uikn5c1KlGMb/M1X0fgJXEKsj7QEfrocFRjpTWh4XKuRQhZKQ+j0BjYOpHRAtcMUL0xBUcElt/gtyoP/bnLM0TFrDSVLt0wAJCvuRCgUhe1CWTPSdQKyW7l2WJEsJmYgqiCsTK/aQHBA20DVwTe/tHgoh1E01ScuAwR2PP/c9zaBhoJucYr45lSzfe20O/N2dj01mUMqtw6ahyeyfi5i3B/Nd1a43y7O9t6iInpi3WfUsK4BZYStopmn/hiKccau1FWtB45VGJhx4cVFjGMwbogQukpD+A/sfFT4jJoz1hjrg+1BbEXy/0MQgbCCqL9jGA+kCaQeH0DjZQRtMmpQ1bdo6aatlm/U5d7o532PG1pKdxt9nNHbenLnsnFw8T2OnFnZsbceWmho8ezxFYWicHWSMY8ynsuLHLD68C47ehs8GU6akpW1Am38AUEsDBBQAAAAIAIdO4kCoG02I+gkAADVJAAANAAAAeGwvc3R5bGVzLnhtbNVcW28bxxV+L5D/sFijfSgqkXvhZR1RrkVpgQBpEMAuUKANDIpcStsuueru0pASBHDjuGpTuEDhtm6DAE0TOM5DrbZp0Rhp7PwZk5ae+hd6ZvYyZ8jZ5VomObYFmHs75zuXb87M7Ay5celw4CnXnSB0/WFL1darquIMu37PHe611B9etdeaqhJGnWGv4/lDp6UeOaF6afOVb22E0ZHnXNl3nEgBFcOwpe5H0cHFSiXs7juDTrjuHzhDuNP3g0EngtNgrxIeBE6nFxKhgVfRq9V6ZdBxh2qs4eJhYM1oGbjdwA/9frTe9QcVv993u86MHq1eCZzrLvHBUjc3hqOBPYhCpeuPhlFLNbNLSnzntR5c1FQlNq3t98Cva8p3lQvfu3Chek15lRz/ZA2ffefnIz96dS3+oE98/5qiVlIorFef1hsL/e+be/EBhpm5hVFnbsYXShlhTBuRoK5Xp/xjFzjtly4VO2lO658xlkYv1T5zN/Ez936BMZUku5sbfX/IkqxXIcvkyuZG+LZyveMBmTWSoa7v+YESASchy/TKsDNw4ifGJ7958ugOfWq/E4RA5VjQMMk1SuTkyYE79ANysRJjTCHpy0YaEXsEfgV7uy3Vtqvwz7aJEXOdq5ZzbQ5gEwCbVNWiAAu8i/0r5Z1WNnW7OKBNopsjinFutM5PBURxMRqiZZK+hk3+zg0p5CbnYG2BDs5Ho8FbUDjno6FwJq38/MkTohUw07ANu1FfYuKQcwlXCKSxTK7MQtqXG9vVco39fO0PQSYpXDBaQQolOEfdnVs3S4eywDkYYWmLzlwBnNWGbmHBzaEQrl5bhXeL5iQdQ4QwXHE9LxulGgYZwMCVzY2DThQ5wdCGEyU5vnp0AMOXIQzBSbGpxM/NeXov6BxpOi395QRC33N7xIq9Nh00JRXHhn/tNsHdTW64w55z6MAouk4HShVkcFnjcrHabctaEZZuw99qsC7XyN9qsNr1Hbu9sxosYEZjdVg7W9ayeZi0dcrrJdI9g1Eil8xRq+sNy7KaWr3ZbFqmoa0evwb4ltG06jqYUV02VWf9NwC+Uas1a5qlm9qyS0CCvyI3a6rcNCN8KWlG+FLSTIcky2/NdclpRvhS0ozwpaS5seQ+LykaDclpRvhS0ozwpaSZvq1ZfmuGF99S+2aELyXNCF9Kmlc0BLAkpxnhS0kzwn/ONNNJJkxrd/2gBwtKSrL8opE38/G1zQ3P6UcwkQzcvX3yGfkHZFrpRxEs7mxu9NzOnj/seHBYSSXSTyIJK1Gw6NRSo31YNErf2Caz1C2d/JEeoEIeTTBKSlB7qDklBcDw1O6SErGT830EB0TRQSjdn2W+Z8PoOGIkjMtCyIqhSSYqZsOsNsyaXo8jviDnBk7PHQ1mvcuwBQwFopRzG0VwhjzsBXxZ8iCJcuRBAiXJgyQW4SN7cVzWRyRRzkckUNJHJPGsPvb80a7nZGSZeT0u8nKuzKyfc0UEns6VKevrnGIgxrFtWJ2j75hLtw0eR9hSuFIz32fu8SJ3k0IP/UbX8bwrpMD/qJ91HiZZzz/sowV22KBAFl/JGj45hHekyWHcUcQn4A8nZNIF8ViKLPyLpZTOwYF3ZIN2qjs+g0fZ2Rbt2tj5Zc/dGw4cLPBm4EdON6LbKUjP10kfIbssIrdLVqa7IOEEtK867E+bStfWXwpTYeiQpuJFDyrd7fFSBNWkG0heClNRUyRGFzWqN0aDXSew6V4g1nzsVTcuZDGpCC+XxajcQbhZuQNu01KVE2OunM0tWNOVE0WMFKaVR6zAHnir+ELZA6+/Xih74D2N0B4gTxFfuDb5PHyBFwhCfGh5K8En3ZOIr8BjqfjQpErjP/OAo6C9wEQ8CwjkgBUQaEgFBj0PBWALXgYJYWeQ0FaWBYm6Ig5yiV6ivgRQmJfQApflZV4xhkZXAGkvrHFredUXWt1qDEDlFg5Z0Iub19azjuCLGhSqsHAoxQRUZOFQigmozsKhDBNQYSNFTrYJUPRkmACL5lmx1bgR4gqbBLaB62RWZwNmA9cDyDGB6xFWZwLHBlkFErNBUoXEbJBUIbEJkiokZgN8VUJ6fdIl1UiUCl1SicQmSKqQHBsklUjOBkk1EqdCUonEJkiqkFwmJJVIzgZJNRKlwoBjGUM4bIKkCokzYSy5RFbwkk+8AITXfs619KMc9sVrQLCnNluuQHGeznQqLprF0kU1bmGJKEpWo9CrjmKd8QITPE7nyfGUPD1DL1u0cyweIXPQaxCxOVDwqAEcJMx2aVIgDWhBjl+Oy5KmkO+wtNTxw4en99+HL/EmgVd2R64HW6eyNMwI3L755NHt8a9+efbh71MxwnYmFn+DLF0PTHBO/31//PAXqQChJhOg+6WncZ7++TGATP6egZDulsnQzb/TMmNk24+rb6VowB0kSfeTTkvG5iEZ0qkxNLo5cUbmP7fO7jye/PZeikN6ISYTfy9xKgzjL784Pfnm7O7J0w/fP52WJ10Ik6dbqKYxJ//6/Oz4gxSQ1HsmALNnYMC0xOmDz8a/+2Dyx+PJR39L5UiNRnK0ZUzLTT4+PvvkT6kEnZkjEWH4T+9/CsZNbtzn0ch7RARXE/IjhlPg0ZiJdA6MAIVZS4SATYkQzxFNmLZECB5NhHh6aMK8JULwaCLE8wPqrij0j++Mb2Xs0Hh6wEA+R+T4qwyFZwSMu0UiJ399enI3E+E5AeNkgcjk0xuTv9wb3/7D+NbNycdfZ7I8L3RhomLKz8jSeQnLly5s05N/Hk9u/DeFo9MIJCLk1Pjeo+x5vmroQkqMvzzJnufZoAvZcHbjvScPH2QiPBd0IRfGX391+o+bwPHxg7tnn3x0+usvGG3hG1uY67qQF3r128qaUqiG5wqMaARZNOer4fljCPlTn6+G5xQMLATWCNzJ2iWEAUcFvi0nUJAblUwNHdgxwsTfFp+uW7lRYWr4emQIeZcbFaYGjlBZM8R0nOVKVnggDJwCIT9zo8LU8Jw1hJzNjQpTwzPXEDI3NypMDc9cU8hcAVeyymrwnIWf+ngWrjA1PGdhY4ZATW5UmBqeubAVSaAmNyqZGggDTrUprI6CqEBO4v6G/OIJIpsp5GwuV5ganrOmkLO5UWFqeOaaQubmRoWpgfhgp4TMFUQFKJZEBVRhBULO5kaFqeE5WxNyNjcqTA3P3JqQublRYWp45tYoc9lkC4b2UQf2TtLNdtnYHvjVc/qdkRddzW62VHb8A7o5F8icPPWme92PqIqWyo5fJzu7YZwCHHcOo9dD2IgNn8oocFvqOztbDWt7x9bXmtWt5pppOLU1q7a1vVYz21vb27ZV1avtdyEz5PeELh5q5vl+DahqVaz4d4Vga51mXgw9+M2gIHE2Mf4Ku9ZS0UlsPrG+AmbH/1MnKmH2e0eb/wdQSwMEFAAAAAgAh07iQEvyPo/MAQAA6QQAABQAAAB4bC9zaGFyZWRTdHJpbmdzLnhtbI1UwWrcMBC9F/oPg+4bO00pIdgOG+9uU9g0Jk4PPQ72xBbII1eSt918feVdSqnkQo5+72n0ZubJ2e2vQcGBjJWac3F5kQogbnQrucvFt+fd6lqAdcgtKs2UiyNZcVu8f5dZ68CfZZuL3rnxJkls09OA9kKPxJ550WZA5z9Nl9jRELa2J3KDSj6k6adkQMkCGj2xy8XHKwETyx8TlWfg6loUmZVF5op15w0ds8QVWTIjZ7QkdmS+4kARo9DaJ62HkKiM7gxGcNlL1e7RulB/InbSxMxG3y2Kv2wW4T1yN2EXO51v3rqeZSNd3OHMfiZuySxWfcJmuWLt0LgNuoitjBzQHGHdTsrBqTNYmuC/unk0b5Bt/UJVaLSmRvvovOHOUPnfW0Ph4r3ffYo6uNdGvmq2cNokbOhASo+DTw6c4xPYnfN8Y0c/11z4wFoyBxLFLDWoIBAX5Zwz43MGa1jB+iHi66qKsHmnIfjI0abSS1itoB6Rpe1D/b30Hn1mQBvYo5OsQ8XOv0MVFl3uDkqcGrQSOSxyp5AbDNFKcoT9mVCFjXyRTXjEUfyCnn9GptN07nnLnVro+SHup9iR8g8nclN72z29/nWR+F9V8RtQSwMEFAAAAAgAh07iQF8GLkdBAQAAAAIAAA8AAAB4bC93b3JrYm9vay54bWyNkdFPwjAQxt9N/B+avsPGAJGFjUTRyIshRPG5tjfW0LVLr3P433vbgvjo0/W7a3/57utqfa4M+wKP2tmMT8YxZ2ClU9oeM/7+9jy65wyDsEoYZyHj34B8nd/erFrnT5/OnRgBLGa8DKFOowhlCZXAsavB0qRwvhKBpD9GWHsQCkuAUJkoieO7qBLa8oGQ+v8wXFFoCRsnmwpsGCAejAhkH0tdI89XhTZwGDZioq5fRUW+z4YzIzA8KR1AZXxK0rVwbcw580390GhD0+U0TniU/y658yS6bQ8aWrz2O8labZVrP7QKZcaTxXJGqKH3AvpYBoo1mS7mHS/6w+iDIFZfme1dbgC1B7UHbEzAvcMAfpTQD3ShbcnYhFymmg5+qyY98YKRwsidZ13pL85m84SMSGdl4z2F9UiTjMf9o8vn5T9QSwMECgAAAAAAh07iQAAAAAAAAAAAAAAAAAYAAABfcmVscy9QSwMEFAAAAAgAh07iQHs4drz/AAAA3wIAAAsAAABfcmVscy8ucmVsc62Sz0rEMBDG74LvEOa+TXcVEdl0LyLsTWR9gJhM/9AmE5JZ7b69QVEs1LoHj5n55pvffGS7G90gXjGmjryCdVGCQG/Idr5R8Hx4WN2CSKy91QN5VHDCBLvq8mL7hIPmPJTaLiSRXXxS0DKHOymTadHpVFBAnzs1Rac5P2Mjgza9blBuyvJGxp8eUE08xd4qiHu7BnE4hbz5b2+q687gPZmjQ88zK+RUkZ11bJAVjIN8o9i/EPVFBgY5z3J1Psvvd0qHrK1mLQ1FXIWYU4rc5Vy/cSyZx1xOH4oloM35QNPT58LBkdFbtMtIOoQlouv/JDLHxOSWeT41X0hy8i2rd1BLAwQKAAAAAACHTuJAAAAAAAAAAAAAAAAACQAAAHhsL19yZWxzL1BLAwQUAAAACACHTuJA5fCiGO0AAAC6AgAAGgAAAHhsL19yZWxzL3dvcmtib29rLnhtbC5yZWxzrZLPasMwDMbvg72D0X1x0o0xRp1exqDXrXsAYyt/aGIHS1ubt5/IoVmgdJdcDJ+Ev+8ny9vdue/UDyZqYzBQZDkoDC76NtQGvg7vDy+giG3wtosBDYxIsCvv77Yf2FmWS9S0AylxCWSgYR5etSbXYG8piwMG6VQx9ZZFploP1h1tjXqT5886/fWAcuGp9t5A2vsnUIdxkOT/vWNVtQ7fovvuMfCVCE2NTeg/Ocl4JMY21cgGFuVMiEFfh3lcFYbHTl5zppj0rfjNmvEsO8I5fZJ6OotbDMWaDKeYjtQg8sxxKZFsSzoXGL34ceUvUEsDBBQAAAAIAIdO4kCo8VpzZwEAAA0FAAATAAAAW0NvbnRlbnRfVHlwZXNdLnhtbK2Uy04CMRSG9ya+w6RbM1NwYYxhYOFlqSTiA9T2wDT0lp6C8PaeKWACQYGMm0k67fm///y9DEYra4olRNTe1axf9VgBTnql3axmH5OX8p4VmIRTwngHNVsDstHw+mowWQfAgqod1qxJKTxwjrIBK7DyARzNTH20ItEwzngQci5mwG97vTsuvUvgUplaDTYcPMFULEwqnlf0e+MkgkFWPG4WtqyaiRCMliKRU7506oBSbgkVVeY12OiAN2SD8aOEduZ3wLbujaKJWkExFjG9Cks2uPJyHH1AToaqv1WO2PTTqZZAGgtLEVTQtqxAlYEkISYNP57/ZEsf4XL4LqO2+mLiApO3lzMPGpZZ5kz4ynBsRAT1niKdSOxMxxBBKGwAkjXVnvbuqByLvfWR1gb+3UAWPUFOdKmA52+/cwBZ5gTwy8f5p/fzzrDDtCn1ygrtzuDnLULafarp3vW+kba/LLzzwfNjNvwGUEsBAhQAFAAAAAgAh07iQKjxWnNnAQAADQUAABMAAAAAAAAAAQAgAAAAzyEAAFtDb250ZW50X1R5cGVzXS54bWxQSwECFAAKAAAAAACHTuJAAAAAAAAAAAAAAAAABgAAAAAAAAAAABAAAAA3HwAAX3JlbHMvUEsBAhQAFAAAAAgAh07iQHs4drz/AAAA3wIAAAsAAAAAAAAAAQAgAAAAWx8AAF9yZWxzLy5yZWxzUEsBAhQACgAAAAAAh07iQAAAAAAAAAAAAAAAAAkAAAAAAAAAAAAQAAAAAAAAAGRvY1Byb3BzL1BLAQIUABQAAAAIAIdO4kAUNQ70PAEAAEkCAAAQAAAAAAAAAAEAIAAAACcAAABkb2NQcm9wcy9hcHAueG1sUEsBAhQAFAAAAAgAh07iQIi2tYVJAQAAYwIAABEAAAAAAAAAAQAgAAAAkQEAAGRvY1Byb3BzL2NvcmUueG1sUEsBAhQAFAAAAAgAh07iQFiJTmwqAQAAEQIAABMAAAAAAAAAAQAgAAAACQMAAGRvY1Byb3BzL2N1c3RvbS54bWxQSwECFAAKAAAAAACHTuJAAAAAAAAAAAAAAAAAAwAAAAAAAAAAABAAAABkBAAAeGwvUEsBAhQACgAAAAAAh07iQAAAAAAAAAAAAAAAAAkAAAAAAAAAAAAQAAAAgyAAAHhsL19yZWxzL1BLAQIUABQAAAAIAIdO4kDl8KIY7QAAALoCAAAaAAAAAAAAAAEAIAAAAKogAAB4bC9fcmVscy93b3JrYm9vay54bWwucmVsc1BLAQIUABQAAAAIAIdO4kBL8j6PzAEAAOkEAAAUAAAAAAAAAAEAIAAAAMsbAAB4bC9zaGFyZWRTdHJpbmdzLnhtbFBLAQIUABQAAAAIAIdO4kCoG02I+gkAADVJAAANAAAAAAAAAAEAIAAAAKYRAAB4bC9zdHlsZXMueG1sUEsBAhQACgAAAAAAh07iQAAAAAAAAAAAAAAAAAkAAAAAAAAAAAAQAAAAxwoAAHhsL3RoZW1lL1BLAQIUABQAAAAIAIdO4kARjXA3hwYAAJIbAAATAAAAAAAAAAEAIAAAAO4KAAB4bC90aGVtZS90aGVtZTEueG1sUEsBAhQAFAAAAAgAh07iQF8GLkdBAQAAAAIAAA8AAAAAAAAAAQAgAAAAyR0AAHhsL3dvcmtib29rLnhtbFBLAQIUAAoAAAAAAIdO4kAAAAAAAAAAAAAAAAAOAAAAAAAAAAAAEAAAAIUEAAB4bC93b3Jrc2hlZXRzL1BLAQIUABQAAAAIAIdO4kBw0hER4AUAANMZAAAYAAAAAAAAAAEAIAAAALEEAAB4bC93b3Jrc2hlZXRzL3NoZWV0MS54bWxQSwUGAAAAABEAEQAHBAAAZyMAAAAA";
        InputStream inputStream = new ByteArrayInputStream(Base64Utils.decodeFromString(importContent));
        // excel 后缀
        String excelSuffix = "xlsx";
        // timeZone
        String timeZone = "Asia/Shanghai";
        // stageId
        String stageId = UUID.randomUUID().toString();
        // needEntryDate
        boolean needEntryDate = false;
        // 导入类型
        String resource = "childPlus";
        // 机构 Id
        String agencyId = UUID.randomUUID().toString();

        // 调用方法
        ImportStructure importStructure = ImportTrackExcelUtil.parse_V2(inputStream, excelSuffix, timeZone, stageId, needEntryDate, resource, agencyId);

        // 验证结果
        assert importStructure.getCenters().size() == 2;
    }

    /**
     * 测试解析 v3 版本的 track excel
     */
    @Test
    public void testParse_V3() throws IOException {
        // 数据准备
        // 导入内容
        String importContent = "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";
        InputStream inputStream = new ByteArrayInputStream(Base64Utils.decodeFromString(importContent));
        // excel 后缀
        String excelSuffix = "xlsx";
        // timeZone
        String timeZone = "Asia/Shanghai";
        // stageId
        String stageId = UUID.randomUUID().toString();
        // needEntryDate
        boolean needEntryDate = false;
        // 导入类型
        String resource = "childPlus";
        // 机构 Id
        String agencyId = UUID.randomUUID().toString();

        // 调用方法
        ImportStructure importStructure = ImportTrackExcelUtil.parse_V3(inputStream, excelSuffix, timeZone, stageId, needEntryDate, resource, agencyId);

        // 验证结果
        assert importStructure.getCenters().size() == 2;
    }
}
