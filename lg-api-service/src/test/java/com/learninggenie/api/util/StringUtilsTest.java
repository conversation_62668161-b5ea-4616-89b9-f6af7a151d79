package com.learninggenie.api.util;

import com.learninggenie.api.util.sync.AttrUtil;
import org.junit.Assert;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by miao on 7/7/2016.
 */
public class StringUtilsTest {
    @Test
    public void excapeUrl() throws Exception {
        System.out.println(StringUtils.excapeUrl("Preschool/PM"));
    }

    @Test
    public void testCheckEld() {
        List<String> languages = new ArrayList<>();
        Assert.assertTrue(AttrUtil.isEld(languages) == null);
        languages.add("English");
        Assert.assertFalse(AttrUtil.isEld(languages));
        languages.add("Spanish");
        Assert.assertTrue(AttrUtil.isEld(languages));
        languages = new ArrayList<>();
        languages.add("Spanish");
        Assert.assertTrue(AttrUtil.isEld(languages));
    }

}