package com.learninggenie.api.util;

import com.learninggenie.common.data.model.CenterEntity;
import junit.framework.TestCase;
import org.junit.Assert;
import org.junit.Test;

import java.util.List;

public class OnCareXmlUtilTest extends TestCase {
    @Test
    public void testParesCenters() throws Exception {
        String xml = "<Output>\n" +
                "  <Status>\n" +
                "    <StatusCode>1</StatusCode>\n" +
                "    <Description>Success</Description>\n" +
                "  </Status>\n" +
                "  <Locations>\n" +
                "    <OCOLocationID>800</OCOLocationID>\n" +
                "    <OCOLocationName>Sandbox Veracity OLD</OCOLocationName>\n" +
                "    <Address>111, Test Street</Address>\n" +
                "    <City>California</City>\n" +
                "    <State>CA</State>\n" +
                "    <Zip>95023</Zip>\n" +
                "    <LocationLogo>https://www.oncareoffice.com/images/centers/1154_logo.jpg</LocationLogo>\n" +
                "  </Locations>\n" +
                " \n" +
                "</Output>";
        List<CenterEntity> list = OnCareXmlUtil.parseCenterXml(xml, "", "");
        Assert.assertTrue(list.size() == 1);
    }

    @Test
    public void testParseGroupXml() throws Exception {
        String xml = "<Output>\n" +
                "  <Status>\n" +
                "    <StatusCode>1</StatusCode>\n" +
                "    <Description>Success</Description>\n" +
                "  </Status>\n" +
                "  <Classrooms>\n" +
                "    <OCOClassroomId>5313</OCOClassroomId>\n" +
                "    <ClassroomName>Class A</ClassroomName>\n" +
                "    <OCOLocationId>861</OCOLocationId>\n" +
                "    <ClassroomStatus>1</ClassroomStatus>\n" +
                "  </Classrooms>\n" +
                " \n" +
                "</Output>";
        List list = OnCareXmlUtil.parseGroupXml(xml,"123","456");
        Assert.assertTrue(list.size() == 1);
    }

    @Test
    public void testParseTeacherXml() throws Exception {
        String xml = "<Output>\n" +
                "  <Status>\n" +
                "    <StatusCode>1</StatusCode>\n" +
                "    <Description>Success</Description>\n" +
                "  </Status>\n" +
                "  <Staff>\n" +
                "    <OCOStaffId>4</OCOStaffId>\n" +
                "    <LocationId>621</LocationId>\n" +
                "    <FirstName>TeacherFN</FirstName>\n" +
                "    <LastName>TeacherLN</LastName>\n" +
                "    <Gender>Female</Gender>\n" +
                "    <Dateofbirth>06/28/1980</Dateofbirth>\n" +
                "    <HireDate>03/19/2007</HireDate>\n" +
                "    <ExtStaffId>0</ExtStaffId>\n" +
                "    <ClassRoom>Chickadees</ClassRoom>\n" +
                "    <Address>111, Test Street</Address>\n" +
                "    <City>California</City>\n" +
                "    <State>CA</State>\n" +
                "    <Zip>95014</Zip>\n" +
                "    <TelePhone>0444444444</TelePhone>\n" +
                "    <CellPhone>2064251256</CellPhone>\n" +
                "    <StaffStatus>Active</StaffStatus>\n" +
                "    <StaffPosition>Head Teacher</StaffPosition>\n" +
                "    <StaffEmail><EMAIL></StaffEmail>\n" +
                "    <LastModifiedDateTime>2014-05-14T06:56:55.29-07:00</LastModifiedDateTime>\n" +
                "    <ClassroomId>3506</ClassroomId>\n" +
                "  </Staff>\n" +
                " \n" +
                "</Output>";
        List list = OnCareXmlUtil.parseTeachers(xml);
        Assert.assertTrue(list.size() == 1);
    }

    @Test
    public void testParseChildXml() throws Exception {
        String xml = "<Output>\n" +
                "  <Status>\n" +
                "    <StatusCode>1</StatusCode>\n" +
                "    <Description>Success</Description>\n" +
                "  </Status>\n" +
                "  <Child>\n" +
                "    <OCOFamilyId>4114</OCOFamilyId>\n" +
                "    <OCOChildId>5619</OCOChildId>\n" +
                "    <OCOLocationId>621</OCOLocationId>\n" +
                "    <FirstName>Sally</FirstName>\n" +
                "    <LastName>Acree</LastName>\n" +
                "    <Gender>Girl</Gender>\n" +
                "    <Dateofbirth>08/31/2007</Dateofbirth>\n" +
                "    <ChildStatus>Incoming</ChildStatus>\n" +
                "    <EnrollmentStartDate>11/19/2007</EnrollmentStartDate>\n" +
                "    <ExtChildId>0</ExtChildId>\n" +
                "    <PrimaryClassRoom>Purple Room</PrimaryClassRoom>\n" +
                "    <ScondaryClassRoom>Falcons</ScondaryClassRoom>\n" +
                "    <LastModifiedDateTime>2014-10-13T11:31:01.107-07:00</LastModifiedDateTime>\n" +
                "    <PrimaryClassRoomId>3508</PrimaryClassRoomId>\n" +
                "    <SecondaryClassRoomId>3509</SecondaryClassRoomId>\n" +
                "  </Child>\n" +
                "</Output>";
        List list = OnCareXmlUtil.parseChildren(xml);
        Assert.assertTrue(list.size() == 1);
    }

    @Test
    public void testParseParentXml() throws Exception {
        String xml = "<Output>\n" +
                "  <Status>\n" +
                "    <StatusCode>1</StatusCode>\n" +
                "    <Description>Success</Description>\n" +
                "  </Status>\n" +
                "  <Parent>\n" +
                "    <OCOFamilyId>4114</OCOFamilyId>\n" +
                "    <OCOParentId>6556</OCOParentId>\n" +
                "    <OCOLocationId>621</OCOLocationId>\n" +
                "    <FirstName>Joshua</FirstName>\n" +
                "    <LastName>Acree</LastName>\n" +
                "    <Email><EMAIL></Email>\n" +
                "    <Address>11, Test Drive</Address>\n" +
                "    <City>California</City>\n" +
                "    <State>CA</State>\n" +
                "    <Zip>95014</Zip>\n" +
                "    <HomePhone>0444444444</HomePhone>\n" +
                "    <CellPhone>0444444444</CellPhone>\n" +
                "    <ExtParentId>0</ExtParentId>\n" +
                "    <LastModifiedDateTime>2012-12-01T00:00:00-08:00</LastModifiedDateTime>\n" +
                "    <ParentStatus>Active</ParentStatus>\n" +
                "  </Parent>\n" +
                "</Output>";
        List list = OnCareXmlUtil.parseParents(xml);
        Assert.assertTrue(list.size() == 1);
    }
}
