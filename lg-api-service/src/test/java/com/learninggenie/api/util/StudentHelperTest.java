package com.learninggenie.api.util;

import org.joda.time.DateTime;
import org.junit.Test;

import java.text.SimpleDateFormat;
import java.util.Date;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

/**
 * Created by miao on 3/30/2016.
 */
public class StudentHelperTest {

    @Test
    public void testIsNewPeriodIncludeToday() throws Exception {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy/MM/dd");
        Date from = simpleDateFormat.parse("2015/01/01");
        Date to = simpleDateFormat.parse("2015/01/05");
        Date now = simpleDateFormat.parse("2015/01/01");
        DateTime nowDateTime = new DateTime(now);
        assertTrue(new StudentHelper().isNewPeriodIncludeToday(from, to , nowDateTime));

        from = simpleDateFormat.parse("2015/01/01");
        to = simpleDateFormat.parse("2015/01/05");
        now = simpleDateFormat.parse("2015/01/05");
        nowDateTime = new DateTime(now);
        assertTrue(new StudentHelper().isNewPeriodIncludeToday(from, to , nowDateTime));

        from = simpleDateFormat.parse("2015/01/01");
        to = simpleDateFormat.parse("2015/01/05");
        now = simpleDateFormat.parse("2015/01/04");
        nowDateTime = new DateTime(now);
        assertTrue(new StudentHelper().isNewPeriodIncludeToday(from, to , nowDateTime));

        from = simpleDateFormat.parse("2015/01/01");
        to = simpleDateFormat.parse("2015/01/05");
        now = simpleDateFormat.parse("2014/12/31");
        nowDateTime = new DateTime(now);
        assertFalse(new StudentHelper().isNewPeriodIncludeToday(from, to , nowDateTime));

        from = simpleDateFormat.parse("2015/01/01");
        to = simpleDateFormat.parse("2015/01/05");
        now = simpleDateFormat.parse("2015/01/06");
        nowDateTime = new DateTime(now);
        assertFalse(new StudentHelper().isNewPeriodIncludeToday(from, to , nowDateTime));
    }
}