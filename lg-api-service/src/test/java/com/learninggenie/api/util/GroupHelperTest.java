package com.learninggenie.api.util;

import com.learninggenie.api.exception.BusinessException;
import com.learninggenie.common.data.entity.GroupPeriodEntity;
import org.joda.time.DateTime;
import org.junit.Test;

import java.text.ParseException;
import java.text.SimpleDateFormat;

/**
 * Created by miao on 4/1/2016.
 */
public class GroupHelperTest {

    @Test(expected = BusinessException.class)
    public void testThrowExceptionIfPeriodHasPastToday() throws Exception {
        final SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
        GroupPeriodEntity groupPeriodEntity = new GroupPeriodEntity();
        groupPeriodEntity.setToAtLocal(simpleDateFormat.parse("20160103"));
        GroupHelper helper = new GroupHelper() {
            @Override
            public DateTime getCurrentTime(String timeZone) {
                try {
                    return new DateTime(simpleDateFormat.parse("20160104"));
                } catch (ParseException e) {
                    e.printStackTrace();
                }
                return null;
            }
        };
        helper.throwExceptionIfPeriodHasPassedToday(groupPeriodEntity, "timezone");
    }

    @Test
    public void testNotThrowExceptionIfPeriodEndToday() throws Exception {
        final SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
        GroupPeriodEntity groupPeriodEntity = new GroupPeriodEntity();
        groupPeriodEntity.setToAtLocal(simpleDateFormat.parse("20160104"));
        GroupHelper helper = new GroupHelper() {
            @Override
            public DateTime getCurrentTime(String timeZone) {
                try {
                    return new DateTime(simpleDateFormat.parse("20160104"));
                } catch (ParseException e) {
                    e.printStackTrace();
                }
                return null;
            }
        };
        helper.throwExceptionIfPeriodHasPassedToday(groupPeriodEntity, "timezone");
    }


    @Test
    public void testNotThrowExceptionIfPeriodAfterToday() throws Exception {
        final SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
        GroupPeriodEntity groupPeriodEntity = new GroupPeriodEntity();
        groupPeriodEntity.setToAtLocal(simpleDateFormat.parse("20160105"));
        GroupHelper helper = new GroupHelper() {
            @Override
            public DateTime getCurrentTime(String timeZone) {
                try {
                    return new DateTime(simpleDateFormat.parse("20160104"));
                } catch (ParseException e) {
                    e.printStackTrace();
                }
                return null;
            }
        };
        helper.throwExceptionIfPeriodHasPassedToday(groupPeriodEntity, "timezone");
    }
}