package com.learninggenie.api.util;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;

class Base64UtilsTest {

    @Test
    public void testConvertFileToBase64String() throws IOException {
        // 创建一个包含一些数据的样本输入流
        String inputData = "Hello, World!";
        InputStream inputStream = new ByteArrayInputStream(inputData.getBytes());

        // 调用待测试的方法
        String base64String = Base64Utils.convertFileToBase64String(inputStream);

        // 验证结果
        Assertions.assertNotNull(base64String); // 断言结果不为空
        Assertions.assertFalse(base64String.isEmpty()); // 断言结果不为空字符串
    }

}