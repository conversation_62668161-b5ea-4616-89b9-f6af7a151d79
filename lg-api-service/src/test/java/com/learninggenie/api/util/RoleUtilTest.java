package com.learninggenie.api.util;

import com.learninggenie.common.utils.RoleUtil;
import org.junit.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

/**
 * 角色工具类测试
 */
@ExtendWith(MockitoExtension.class)
@RunWith(MockitoJUnitRunner.class)
public class RoleUtilTest {

    /**
     * 测试比较角色级别方法
     */
    @Test
    public void testCompareRole() {
        // 测试机构拥有者和机构管理员的比较，预期结果为 false，因为他们的级别相同
        assertFalse(RoleUtil.compareRole("AGENCY_OWNER", "AGENCY_ADMIN"));

        // 测试机构拥有者和园长的比较，预期结果为 true，因为机构拥有者的级别高于园长
        assertTrue(RoleUtil.compareRole("AGENCY_OWNER", "SITE_ADMIN"));

        // 测试园长和教师的比较，预期结果为 true，因为园长的级别高于教师
        assertTrue(RoleUtil.compareRole("SITE_ADMIN", "COLLABORATOR"));

        // 测试教师和教师的比较，预期结果为 false，因为他们的级别相同
        assertFalse(RoleUtil.compareRole("COLLABORATOR", "COLLABORATOR"));

        // 测试空角色和机构拥有者的比较，预期结果为 false，因为空角色的级别为 0
        assertFalse(RoleUtil.compareRole("", "AGENCY_OWNER"));
    }
}
