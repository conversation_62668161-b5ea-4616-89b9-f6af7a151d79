package com.learninggenie.api.config;

import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockitoAnnotations;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.support.AnnotationConfigContextLoader;

@RunWith(SpringJUnit4ClassRunner.class)
//@RunWith(MockitoJUnitRunner.class)
@ContextConfiguration(classes= SpringConfigTest.class, loader=AnnotationConfigContextLoader.class)
public class TestBase {
    public AnnotationConfigApplicationContext ctx = new AnnotationConfigApplicationContext(SpringConfigTest.class);
    @Before
    public void start(){
        MockitoAnnotations.initMocks(this);
    }
    @Ignore
    @Test
     public void testNothing(){

    }
}
