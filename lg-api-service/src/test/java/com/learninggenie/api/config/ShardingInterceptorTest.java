package com.learninggenie.api.config;

import com.learninggenie.api.config.sharding.ShardingInterceptor;
import com.learninggenie.common.sharding.ShardingProvider;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import javax.servlet.http.HttpServletRequest;
import java.util.UUID;

/**
 * 分表配置拦截器测试
 */
@RunWith(MockitoJUnitRunner.class)
public class ShardingInterceptorTest {

    /**
     * Header 中用户 ID 的键值
     */
    private static final String HEADER_UID = "X-UID";

    @InjectMocks
    private ShardingInterceptor shardingInterceptor;

    @Mock
    private ShardingProvider shardingProvider;

    /**
     * 测试分表拦截器预处理方法
     */
    @Test
    public void testPreHandle() throws Exception {
        // Mock HttpServletRequest
        HttpServletRequest request = Mockito.mock(HttpServletRequest.class);

        // Header 中有 UID 且格式正确的情况
        Mockito.when(request.getHeader(HEADER_UID)).thenReturn(UUID.randomUUID().toString());
        // 执行处理方法
        shardingInterceptor.preHandle(request, null, null);
        // 验证会调用 setShardingHintWithUser 方法
        Mockito.verify(shardingProvider).setShardingHintWithUser(Mockito.anyString());

        // 清除调用信息
        Mockito.clearInvocations(request, shardingProvider);
        // Header 中有 UID 但格式不正确的情况
        Mockito.when(request.getHeader(HEADER_UID)).thenReturn("123");
        // 执行处理方法
        shardingInterceptor.preHandle(request, null, null);
        // 验证不会调用 setShardingHintWithUser 方法
        Mockito.verify(shardingProvider, Mockito.never()).setShardingHintWithUser(Mockito.anyString());

        // 清除调用信息
        Mockito.clearInvocations(request, shardingProvider);
        // Header 中没有 UID，请求方式为 OPTIONS 的情况
        Mockito.when(request.getHeader(HEADER_UID)).thenReturn(null);
        Mockito.when(request.getMethod()).thenReturn("OPTIONS");
        // 执行处理方法
        shardingInterceptor.preHandle(request, null, null);
        // 验证不会调用 setShardingHintWithUser 方法
        Mockito.verify(shardingProvider, Mockito.never()).setShardingHintWithUser(Mockito.anyString());

        // 清除调用信息
        Mockito.clearInvocations(request, shardingProvider);
        // Header 中没有 UID，请求方式不是 OPTIONS 的情况
        Mockito.when(request.getHeader(HEADER_UID)).thenReturn(null);
        Mockito.when(request.getMethod()).thenReturn("GET");
        // 执行处理方法
        shardingInterceptor.preHandle(request, null, null);
        // 验证不会调用 setShardingHintWithUser 方法
        Mockito.verify(shardingProvider, Mockito.never()).setShardingHintWithUser(Mockito.anyString());
    }

}
