package com.learninggenie.api.mapper;

import com.learninggenie.common.data.mapper.FormMapper;
import com.learninggenie.common.data.model.form.common.FormModel;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.sql.*;

/**
 * Create by hxl 2023/04/23.
 * FormMapper 测试
 */
@RunWith(MockitoJUnitRunner.class)
public class FormMapperTest {
    @InjectMocks
    private FormMapper formMapper;

    @Mock
    private ResultSetMetaData resultSetMetaData;

    @Mock
    private ResultSet resultSet;

    /**
     * 测试 mapper.
     */
    @Test
    public void testMapperForm() throws SQLException {
        // 列数
        Mockito.when(resultSetMetaData.getColumnCount()).thenReturn(20);

        // 列名
        final String id = "Id";
        Mockito.when(resultSetMetaData.getColumnName(1)).thenReturn(id);
        final String name = "Name";
        Mockito.when(resultSetMetaData.getColumnName(2)).thenReturn(name);
        final String description = "Description";
        Mockito.when(resultSetMetaData.getColumnName(3)).thenReturn(description);
        final String headerImg = "HeaderImg";
        Mockito.when(resultSetMetaData.getColumnName(4)).thenReturn(headerImg);
        final String type = "Type";
        Mockito.when(resultSetMetaData.getColumnName(5)).thenReturn(type);
        final String status = "Status";
        Mockito.when(resultSetMetaData.getColumnName(6)).thenReturn(status);
        final String isAnonymous = "IsAnonymous";
        Mockito.when(resultSetMetaData.getColumnName(7)).thenReturn(isAnonymous);
        final String fromDate = "FromDate";
        Mockito.when(resultSetMetaData.getColumnName(8)).thenReturn(fromDate);
        final String toDate = "ToDate";
        Mockito.when(resultSetMetaData.getColumnName(9)).thenReturn(toDate);
        final String toDateAtUtc = "ToDateAtUtc";
        Mockito.when(resultSetMetaData.getColumnName(10)).thenReturn(toDateAtUtc);
        final String createAtUtc = "CreateAtUtc";
        Mockito.when(resultSetMetaData.getColumnName(11)).thenReturn(createAtUtc);
        final String updateAtUtc = "UpdateAtUtc";
        Mockito.when(resultSetMetaData.getColumnName(12)).thenReturn(updateAtUtc);
        final String createBy = "CreateUserId";
        Mockito.when(resultSetMetaData.getColumnName(13)).thenReturn(createBy);
        final String createUserName = "CreateUserName";
        Mockito.when(resultSetMetaData.getColumnName(14)).thenReturn(createUserName);
        final String role = "Role";
        Mockito.when(resultSetMetaData.getColumnName(15)).thenReturn(role);
        final String scope = "Scope";
        Mockito.when(resultSetMetaData.getColumnName(16)).thenReturn(scope);
        final String isDeleted = "IsDeleted";
        Mockito.when(resultSetMetaData.getColumnName(17)).thenReturn(isDeleted);
        final String lang = "Lang";
        Mockito.when(resultSetMetaData.getColumnName(18)).thenReturn(lang);
        final String disableAnonymous = "DisableAnonymous";
        Mockito.when(resultSetMetaData.getColumnName(19)).thenReturn(disableAnonymous);
        final String sendCount = "SendCount";
        Mockito.when(resultSetMetaData.getColumnName(20)).thenReturn(sendCount);

        // 设置结果集元数据
        Mockito.when(resultSet.getMetaData()).thenReturn(resultSetMetaData);

        final String createUserID = "8629de0e-1783-4886-96ff-2f11f6fedd35";
        final String createUserNameValue = "7322f47b6ba449659ab6cd4132db653a";
        final String roleValue = "PARENT";
        final String scopeValue = "scope";
        final String langValue = "EN";
        final String number = "1";
        final String testForm = "test form";
        final String testDescription = "test description";
        final String testHeaderImage = "test header image";
        final String testType = "test type";
        final String testStatus = "test status";
        final long time = 1234567890L;
        final long time1 = 2345678901L;
        final long time2 = 3456789012L;
        final long time3 = 4567890123L;
        final long time4 = 5678901234L;
        // 映射列
        Mockito.when(resultSet.getString(id)).thenReturn(number);
        Mockito.when(resultSet.getString(name)).thenReturn(testForm);
        Mockito.when(resultSet.getString(description)).thenReturn(testDescription);
        Mockito.when(resultSet.getString(headerImg)).thenReturn(testHeaderImage);
        Mockito.when(resultSet.getString(type)).thenReturn(testType);
        Mockito.when(resultSet.getString(status)).thenReturn(testStatus);
        Mockito.when(resultSet.getBoolean(isAnonymous)).thenReturn(false);
        Mockito.when(resultSet.getTimestamp(fromDate)).thenReturn(new Timestamp(time));
        Mockito.when(resultSet.getTimestamp(toDate)).thenReturn(new Timestamp(time1));
        Mockito.when(resultSet.getTimestamp(toDateAtUtc)).thenReturn(new Timestamp(time2));
        Mockito.when(resultSet.getTimestamp(createAtUtc)).thenReturn(new Timestamp(time3));
        Mockito.when(resultSet.getTimestamp(updateAtUtc)).thenReturn(new Timestamp(time4));
        Mockito.when(resultSet.getString(createBy)).thenReturn(createUserID);
        Mockito.when(resultSet.getString(createUserName)).thenReturn(createUserNameValue);
        Mockito.when(resultSet.getString(role)).thenReturn(roleValue);
        Mockito.when(resultSet.getString(scope)).thenReturn(scopeValue);
        Mockito.when(resultSet.getBoolean(isDeleted)).thenReturn(false);
        Mockito.when(resultSet.getString(lang)).thenReturn(langValue);
        Mockito.when(resultSet.getBoolean(disableAnonymous)).thenReturn(false);
        Mockito.when(resultSet.getInt(sendCount)).thenReturn(6);

        // 执行
        final FormModel formModel = FormMapper.ROW_MAPPER_FORM.mapRow(resultSet, 1);

        // 验证
        Assert.assertEquals(number, formModel.getId());
        Assert.assertEquals(testForm, formModel.getName());
        Assert.assertEquals(testDescription, formModel.getDescription());
        Assert.assertEquals(testHeaderImage, formModel.getHeaderImg());
        Assert.assertEquals(testType, formModel.getType());
        Assert.assertEquals(testStatus, formModel.getStatus());
        Assert.assertFalse(formModel.isAnonymous());
        Assert.assertEquals(new Date(time), formModel.getFromDate());
        Assert.assertEquals(new Date(time1), formModel.getToDate());
        Assert.assertEquals(new Timestamp(time2), formModel.getToDateAtUtc());
        Assert.assertEquals(new Timestamp(time3), formModel.getCreateAtUtc());
        Assert.assertEquals(new Timestamp(time4), formModel.getUpdateAtUtc());
        Assert.assertEquals(createUserID, formModel.getCreateUserId());
        Assert.assertEquals(createUserNameValue, formModel.getCreateUserName());
        Assert.assertFalse(formModel.isDisableAnonymous());
        Assert.assertEquals(roleValue, formModel.getRole());
        Assert.assertFalse(formModel.isDeleted());
        Assert.assertEquals(scopeValue, formModel.getScope());
        Assert.assertEquals(langValue, formModel.getLang());
        Assert.assertEquals(6, formModel.getSendCount());
    }
}
