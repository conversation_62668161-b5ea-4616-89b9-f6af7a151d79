package com.learninggenie.api.mapper;

import com.learninggenie.common.data.entity.GroupMetaDataEntity;
import com.learninggenie.common.data.mapper.GroupMapper;
import com.learninggenie.common.data.model.GroupPeriodModel;
import com.learninggenie.common.utils.TimeUtil;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.sql.Date;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;

/**
 * Create by hxl 2023/04/23.
 * GroupMapperTest 测试
 */
@RunWith(MockitoJUnitRunner.class)
public class GroupMapperTest {

    @InjectMocks
    private GroupMapper groupMapper;

    @Mock
    private ResultSetMetaData resultSetMetaData;

    @Mock
    private ResultSet resultSet;

    /**
     * 测试 mapper.
     */
    @Test
    public void testMapperGetGroupPeriods() throws SQLException {
        // 列数
        Mockito.when(resultSetMetaData.getColumnCount()).thenReturn(7);

        // 列名
        final String groupId = "GroupId";
        Mockito.when(resultSetMetaData.getColumnName(1)).thenReturn(groupId);
        final String periodId = "Id";
        Mockito.when(resultSetMetaData.getColumnName(2)).thenReturn(periodId);
        final String periodGroupId = "PeriodGroupId";
        Mockito.when(resultSetMetaData.getColumnName(3)).thenReturn(periodGroupId);
        final String fromAtLocal = "fromAtLocal";
        Mockito.when(resultSetMetaData.getColumnName(4)).thenReturn(fromAtLocal);
        final String toAtLocal = "toAtLocal";
        Mockito.when(resultSetMetaData.getColumnName(5)).thenReturn(toAtLocal);
        final String alias = "Alias";
        Mockito.when(resultSetMetaData.getColumnName(6)).thenReturn(alias);
        final String active = "Active";
        Mockito.when(resultSetMetaData.getColumnName(7)).thenReturn(active);

        // 设置结果集元数据
        Mockito.when(resultSet.getMetaData()).thenReturn(resultSetMetaData);

        final String testGroupId = "123456";
        final String testPeriodId = "7890";
        final String testPeriodGroupId = "0987";
        final Date testFromAtLocal = new Date(TimeUtil.getUtcNow().getTime());
        final Date testToAtLocal = new Date(TimeUtil.getUtcNow().getTime());
        final String testAlias = "Test Alias";
        final boolean testActive = true;

        // 映射列
        Mockito.when(resultSet.getString(groupId)).thenReturn(testGroupId);
        Mockito.when(resultSet.getString(periodId)).thenReturn(testPeriodId);
        Mockito.when(resultSet.getString(periodGroupId)).thenReturn(testPeriodGroupId);
        Mockito.when(resultSet.getDate(fromAtLocal)).thenReturn(testFromAtLocal);
        Mockito.when(resultSet.getDate(toAtLocal)).thenReturn(testToAtLocal);
        Mockito.when(resultSet.getString(alias)).thenReturn(testAlias);
        Mockito.when(resultSet.getBoolean(active)).thenReturn(testActive);

        // 执行
        final GroupPeriodModel groupPeriodModel = GroupMapper.MAPPER_GET_GROUP_PERIODS.mapRow(resultSet, 1);

        // 验证
        Assert.assertEquals(testGroupId, groupPeriodModel.getGroupId());
        Assert.assertEquals(testPeriodId, groupPeriodModel.getPeriodId());
        Assert.assertEquals(testPeriodGroupId, groupPeriodModel.getPeriodGroupId());
        Assert.assertEquals(testFromAtLocal, groupPeriodModel.getFromAtLocal());
        Assert.assertEquals(testToAtLocal, groupPeriodModel.getToAtLocal());
        Assert.assertEquals(testAlias, groupPeriodModel.getAlias());
        Assert.assertEquals(testActive, groupPeriodModel.getActive());
    }

    @Test
    public void testGetGroupsMetadata() throws SQLException {
        // 列数
        // 列名
        final String id = "Id";
        final String metaKey = "MetaKey";
        final String metaValue = "MetaValue";
        final String groupId = "GroupId";

        // 设置结果集元数据

        final String testId = "123456";
        final String testMetaKey = "TestKey";
        final String testMetaValue = "TestValue";
        final String testGroupId = "7890";

        // 映射列
        Mockito.when(resultSet.getString(id)).thenReturn(testId);
        Mockito.when(resultSet.getString(metaKey)).thenReturn(testMetaKey);
        Mockito.when(resultSet.getString(metaValue)).thenReturn(testMetaValue);
        Mockito.when(resultSet.getString(groupId)).thenReturn(testGroupId);

        // 执行
        final GroupMetaDataEntity entity = GroupMapper.GET_GROUPS_METADATA.mapRow(resultSet, 1);

        // 验证
        Assert.assertEquals(testId, entity.getId());
        Assert.assertEquals(testMetaKey, entity.getMetaKey());
        Assert.assertEquals(testMetaValue, entity.getMetaValue());
        Assert.assertEquals(testGroupId, entity.getGroup().getId());
    }

    @Test
    public void testMapperCenterGroupModel() throws SQLException {
        // 列数
        Mockito.when(resultSetMetaData.getColumnCount()).thenReturn(7);

        // 列名
        final String stageId = "stageId";
        Mockito.when(resultSetMetaData.getColumnName(1)).thenReturn(stageId);
        final String centerId = "centerId";
        Mockito.when(resultSetMetaData.getColumnName(2)).thenReturn(centerId);
        final String centerName = "centerName";
        Mockito.when(resultSetMetaData.getColumnName(3)).thenReturn(centerName);
        final String groupId = "groupId";
        Mockito.when(resultSetMetaData.getColumnName(4)).thenReturn(groupId);
        final String groupName = "groupName";
        Mockito.when(resultSetMetaData.getColumnName(5)).thenReturn(groupName);
        final String isDeleted = "IsDeleted";
        Mockito.when(resultSetMetaData.getColumnName(6)).thenReturn(isDeleted);
        final String groupIsInactive = "GroupIsInactive";
        Mockito.when(resultSetMetaData.getColumnName(7)).thenReturn(groupIsInactive);
        final String stageId2 = "StageId";

        // 设置结果集元数据
        Mockito.when(resultSet.getMetaData()).thenReturn(resultSetMetaData);

        final String stageIdValue = "stageIdValue";
        final String centerIdValue = "centerIdValue";
        final String centerNameValue = "centerNameValue";
        final String groupIdValue = "groupIdValue";
        final String groupNameValue = "groupNameValue";
        final boolean isDeletedValue = false;
        final boolean groupIsInactiveValue = true;
        final String stageId2Value = "stageId2Value";

        // 映射列
        Mockito.when(resultSet.getString(stageId)).thenReturn(stageIdValue);
        Mockito.when(resultSet.getString(centerId)).thenReturn(centerIdValue);
        Mockito.when(resultSet.getString(centerName)).thenReturn(centerNameValue);
        Mockito.when(resultSet.getString(groupId)).thenReturn(groupIdValue);
        Mockito.when(resultSet.getBoolean(isDeleted)).thenReturn(isDeletedValue);
        Mockito.when(resultSet.getBoolean(groupIsInactive)).thenReturn(groupIsInactiveValue);
        Mockito.when(resultSet.getString(stageId2)).thenReturn(stageId2Value);

        // 执行
        final com.learninggenie.common.data.model.GroupEntity model = GroupMapper.MAPPER_CENTER_GROUP_MODEL.mapRow(resultSet, 1);

        // 验证
        Assert.assertEquals(stageId2Value, model.getStage().getId());
        Assert.assertEquals(centerIdValue, model.getCenterId());
        Assert.assertEquals(centerNameValue, model.getCenterName());
        Assert.assertEquals(groupIdValue, model.getId());
        Assert.assertEquals(centerNameValue, model.getName());
        Assert.assertEquals(isDeletedValue, model.isDeleted());
        Assert.assertEquals(groupIsInactiveValue, model.isInactive());
        Assert.assertEquals(stageId2Value, model.getStage().getId());
    }
}
