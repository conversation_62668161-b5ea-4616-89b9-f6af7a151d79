package com.learninggenie.api.mapper;

import com.learninggenie.common.data.mapper.ScoreMapper;
import com.learninggenie.common.data.model.StudentScoreModel;
import com.learninggenie.common.data.model.sftp.HasScoreStudent;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Timestamp;

/**
 * Create by hxl 2023/04/23.
 * ScoreMapperTest 测试
 */
@RunWith(MockitoJUnitRunner.class)
public class ScoreMapperTest {

    @InjectMocks
    private ScoreMapper centerMapper;

    @Mock
    private ResultSetMetaData resultSetMetaData;

    @Mock
    private ResultSet resultSet;

    /**
     * 测试 mapper.
     */
    @Test
    public void testStudentScoreModelRowMapper() throws SQLException {
        // 列数
        // 列名
        final String id = "Id";
        final String studentId = "EnrollmentId";
        final String noteId = "NoteId";
        final String domainId = "DomainId";
        final String levelId = "LevelId";
        final String comment = "Comment";
        final String groupId = "GroupId";
        final String createAtUtc = "CreateAtUtc";
        final String alias = "Alias";
        final String frameworkId = "FrameworkId";

        // 设置结果集元数据

        final String idValue = "idValue";
        final String studentIdValue = "studentIdValue";
        final String noteIdValue = "noteIdValue";
        final String domainIdValue = "domainIdValue";
        final String levelIdValue = "levelIdValue";
        final String commentValue = "commentValue";
        final String groupIdValue = "groupIdValue";
        final long createAtUtcValue = 1234567890L;
        final String aliasValue = "aliasValue";
        final String frameworkIdValue = "frameworkIdValue";

        // 映射列
        Mockito.when(resultSet.getString(id)).thenReturn(idValue);
        Mockito.when(resultSet.getString(studentId)).thenReturn(studentIdValue);
        Mockito.when(resultSet.getString(noteId)).thenReturn(noteIdValue);
        Mockito.when(resultSet.getString(domainId)).thenReturn(domainIdValue);
        Mockito.when(resultSet.getString(levelId)).thenReturn(levelIdValue);
        Mockito.when(resultSet.getString(comment)).thenReturn(commentValue);
        Mockito.when(resultSet.getString(groupId)).thenReturn(groupIdValue);
        Mockito.when(resultSet.getTimestamp(createAtUtc)).thenReturn(new Timestamp(createAtUtcValue));
        Mockito.when(resultSet.getString(alias)).thenReturn(aliasValue);
        Mockito.when(resultSet.getString(frameworkId)).thenReturn(frameworkIdValue);

        // 执行
        final StudentScoreModel model = ScoreMapper.studentScoreModelRowMapper.mapRow(resultSet, 1);

        // 验证
        Assert.assertEquals(idValue, model.getId());
        Assert.assertEquals(studentIdValue, model.getStudentId());
        Assert.assertEquals(noteIdValue, model.getNoteId());
        Assert.assertEquals(domainIdValue, model.getDomainId());
        Assert.assertEquals(levelIdValue, model.getLevelId());
        Assert.assertEquals(commentValue, model.getComment());
        Assert.assertEquals(groupIdValue, model.getGroupId());
        Assert.assertEquals(new Timestamp(createAtUtcValue), model.getCreateAtUtcDate());
        Assert.assertEquals(aliasValue, model.getAlias());
        Assert.assertEquals(frameworkIdValue, model.getFrameworkId());
    }

    @Test
    public void testHasScoreStudentRowMapper() throws SQLException {
        // 列数

        // 列名
        final String enrollmentId = "EnrollmentId";
        final String groupId = "GroupId";
        final String centerId = "CenterId";
        final String centerName = "CenterName";
        final String groupName = "GroupName";
        final String stageId = "StageId";
        final String levelId = "LevelId";

        // 设置结果集元数据

        final String enrollmentIdValue = "enrollmentIdValue";
        final String groupIdValue = "groupIdValue";
        final String centerIdValue = "centerIdValue";
        final String centerNameValue = "centerNameValue";
        final String groupNameValue = "groupNameValue";
        final String stageIdValue = "stageIdValue";
        final String levelIdValue = "levelIdValue";

        // 映射列
        Mockito.when(resultSet.getString(enrollmentId)).thenReturn(enrollmentIdValue);
        Mockito.when(resultSet.getString(groupId)).thenReturn(groupIdValue);
        Mockito.when(resultSet.getString(centerId)).thenReturn(centerIdValue);
        Mockito.when(resultSet.getString(centerName)).thenReturn(centerNameValue);
        Mockito.when(resultSet.getString(groupName)).thenReturn(groupNameValue);
        Mockito.when(resultSet.getString(stageId)).thenReturn(stageIdValue);
        Mockito.when(resultSet.getString(levelId)).thenReturn(levelIdValue);

        // 执行
        final HasScoreStudent model = ScoreMapper.HAS_SCORE_STUDENT_ROW_MAPPER.mapRow(resultSet, 1);

        // 验证
        Assert.assertEquals(enrollmentIdValue, model.getEnrollmentId());
        Assert.assertEquals(groupIdValue, model.getGroupId());
        Assert.assertEquals(centerIdValue, model.getCenterId());
        Assert.assertEquals(centerNameValue, model.getCenterName());
        Assert.assertEquals(groupNameValue, model.getGroupName());
        Assert.assertEquals(stageIdValue, model.getStageId());
        Assert.assertEquals(levelIdValue, model.getLevelId());
    }

    @Test
    public void testHasScoreStudentRowMapper1() throws SQLException {
        // 列数

        // 列名
        final String enrollmentId = "EnrollmentId";
        final String groupId = "GroupId";
        final String centerId = "CenterId";
        final String centerName = "CenterName";
        final String groupName = "GroupName";
        final String stageId = "StageId";
        final String snapshotId = "SnapshotId";

        // 设置结果集元数据

        final String enrollmentIdValue = "enrollmentIdValue";
        final String groupIdValue = "groupIdValue";
        final String centerIdValue = "centerIdValue";
        final String centerNameValue = "centerNameValue";
        final String groupNameValue = "groupNameValue";
        final String stageIdValue = "stageIdValue";
        final String snapshotIdValue = "snapshotIdValue";

        // 映射列
        Mockito.when(resultSet.getString(enrollmentId)).thenReturn(enrollmentIdValue);
        Mockito.when(resultSet.getString(groupId)).thenReturn(groupIdValue);
        Mockito.when(resultSet.getString(centerId)).thenReturn(centerIdValue);
        Mockito.when(resultSet.getString(centerName)).thenReturn(centerNameValue);
        Mockito.when(resultSet.getString(groupName)).thenReturn(groupNameValue);
        Mockito.when(resultSet.getString(stageId)).thenReturn(stageIdValue);
        Mockito.when(resultSet.getString(snapshotId)).thenReturn(snapshotIdValue);

        // 执行
        final HasScoreStudent model = ScoreMapper.HAS_SCORE_STUDENT_ROW_MAPPER1.mapRow(resultSet, 1);

        // 验证
        Assert.assertEquals(enrollmentIdValue, model.getEnrollmentId());
        Assert.assertEquals(groupIdValue, model.getGroupId());
        Assert.assertEquals(centerIdValue, model.getCenterId());
        Assert.assertEquals(centerNameValue, model.getCenterName());
        Assert.assertEquals(groupNameValue, model.getGroupName());
        Assert.assertEquals(stageIdValue, model.getStageId());
        Assert.assertEquals(snapshotIdValue, model.getSnapshotId());
    }

}
