package com.learninggenie.api.mapper;

import com.learninggenie.common.data.entity.CenterEntity;
import com.learninggenie.common.data.mapper.CenterMapper;
import com.learninggenie.common.utils.TimeUtil;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.sql.*;
import java.util.Date;

/**
 * Create by hxl 2023/04/23.
 * CenterMapperTest 测试
 */
@RunWith(MockitoJUnitRunner.class)
public class CenterMapperTest {

    @InjectMocks
    private CenterMapper centerMapper;

    @Mock
    private ResultSetMetaData resultSetMetaData;

    @Mock
    private ResultSet resultSet;

    /**
     * 测试 mapper.
     */
    @Test
    public void testMapperCenter() throws SQLException {
        // 列数
        Mockito.when(resultSetMetaData.getColumnCount()).thenReturn(13);

        // 列名
        final String id = "Id";
        Mockito.when(resultSetMetaData.getColumnName(1)).thenReturn(id);
        final String name = "Name";
        Mockito.when(resultSetMetaData.getColumnName(2)).thenReturn(name);
        final String centerTimeZone = "CenterTimeZone";
        Mockito.when(resultSetMetaData.getColumnName(3)).thenReturn(centerTimeZone);
        final String sendReportTime = "SendReportTime";
        Mockito.when(resultSetMetaData.getColumnName(4)).thenReturn(sendReportTime);
        final String createAtUtc = "CreateAtUtc";
        Mockito.when(resultSetMetaData.getColumnName(5)).thenReturn(createAtUtc);
        final String userId = "UserId";
        Mockito.when(resultSetMetaData.getColumnName(6)).thenReturn(userId);
        final String firstName = "FirstName";
        Mockito.when(resultSetMetaData.getColumnName(7)).thenReturn(firstName);
        final String lastName = "LastName";
        Mockito.when(resultSetMetaData.getColumnName(8)).thenReturn(lastName);
        final String logoMediaId = "MediaId";
        Mockito.when(resultSetMetaData.getColumnName(9)).thenReturn(logoMediaId);
        final String relativePath = "RelativePath";
        Mockito.when(resultSetMetaData.getColumnName(10)).thenReturn(relativePath);
        final String metaValue = "MetaValue";
        Mockito.when(resultSetMetaData.getColumnName(11)).thenReturn(metaValue);
        final String isTraining = "IsTraining";
        Mockito.when(resultSetMetaData.getColumnName(12)).thenReturn(isTraining);
        final String agencyId = "AgencyId";
        Mockito.when(resultSetMetaData.getColumnName(13)).thenReturn(agencyId);

        // 设置结果集元数据
        Mockito.when(resultSet.getMetaData()).thenReturn(resultSetMetaData);

        final String testId = "123456";
        final String testName = "Test Center";
        final String testTimeZone = "GMT+8";
        final Date testReportTime = TimeUtil.getUtcNow();
        final Timestamp testCreateAtUtc = new Timestamp(System.currentTimeMillis());
        final String testUserId = "7890";
        final String testFirstName = "John";
        final String testLastName = "Doe";
        final String testMediaId = "5678";
        final String testRelativePath = "/path/to/logo.jpg";
        final String testMetaValue = "Test Meta";
        final boolean testIsTraining = true;
        final String testAgencyId = "9876";

        // 映射列
        Mockito.when(resultSet.getString(id)).thenReturn(testId);
        Mockito.when(resultSet.getString(name)).thenReturn(testName);
        Mockito.when(resultSet.getString(centerTimeZone)).thenReturn(testTimeZone);
        Mockito.when(resultSet.getTime(sendReportTime)).thenReturn(new Time(testReportTime.getTime()));
        Mockito.when(resultSet.getTimestamp(createAtUtc)).thenReturn(testCreateAtUtc);
        Mockito.when(resultSet.getString(userId)).thenReturn(testUserId);
        Mockito.when(resultSet.getString(firstName)).thenReturn(testFirstName);
        Mockito.when(resultSet.getString(lastName)).thenReturn(testLastName);
        Mockito.when(resultSet.getString(logoMediaId)).thenReturn(testMediaId);
        Mockito.when(resultSet.getString(relativePath)).thenReturn(testRelativePath);
        Mockito.when(resultSet.getString(metaValue)).thenReturn(testMetaValue);
        Mockito.when(resultSet.getBoolean(isTraining)).thenReturn(testIsTraining);
        Mockito.when(resultSet.getString(agencyId)).thenReturn(testAgencyId);

        // 执行
        final CenterEntity centerEntity = CenterMapper.MAPPER_CENTER.mapRow(resultSet, 1);

        // 验证
        Assert.assertEquals(testId, centerEntity.getId());
        Assert.assertEquals(testName, centerEntity.getName());
        Assert.assertEquals(testTimeZone, centerEntity.getCenterTimeZone());
        Assert.assertEquals(testReportTime, centerEntity.getSendReportTime());
        Assert.assertEquals(testCreateAtUtc, centerEntity.getCreateAtUtc());
        Assert.assertEquals(testUserId, centerEntity.getUser().getId());
        Assert.assertEquals(testFirstName, centerEntity.getUser().getFirstName());
        Assert.assertEquals(testLastName, centerEntity.getUser().getLastName());
        Assert.assertEquals(testMediaId, centerEntity.getLogoMedia().getId());
        Assert.assertEquals(testRelativePath, centerEntity.getLogoMedia().getRelativePath());
        Assert.assertEquals(testMetaValue, centerEntity.getMetaValue());
        Assert.assertEquals(testIsTraining, centerEntity.isTraining());
        Assert.assertEquals(testAgencyId, centerEntity.getAgencies().iterator().next().getId());
    }
}
