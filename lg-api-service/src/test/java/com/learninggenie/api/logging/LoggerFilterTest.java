package com.learninggenie.api.logging;

import org.junit.Test;

import static org.junit.Assert.assertEquals;

/**
 * Created by mili on 9/9/2016.
 */
public class LoggerFilterTest {
    @Test
    public void removeUUIDTest() throws Exception {
        assertEquals("EMPTY", LoggerFilter.removeUUID(""));
        assertEquals("EMPTY", LoggerFilter.removeUUID(null));
        assertEquals("/api/", LoggerFilter.removeUUID("/api/"));
        assertEquals("/api/vi", LoggerFilter.removeUUID("/api/vi"));
        assertEquals("/api/v1/test1", LoggerFilter.removeUUID("/api/v1/test1"));
        assertEquals("/api/vi/test1/test2", LoggerFilter.removeUUID("/api/vi/test1/test2"));
        assertEquals("/api/v1/test1/uuid/test2", LoggerFilter.removeUUID("/api/v1/test1/ddc35b98-b976-e611-bcde-06538d331ea1/test2"));
        assertEquals("/api/v1/test1/test2/uuid", LoggerFilter.removeUUID("/api/v1/test1/test2/ddc35b98-b976-e611-bcde-06538d331ea1"));
        assertEquals("/api/v1/test1/uuid/test2/uuid", LoggerFilter.removeUUID("/api/v1/test1/ddc35b98-b976-e611-bcde-06538d331ea1/test2/ddc35b98-b976-e611-bcde-06538d331ea1"));
        assertEquals("/api/v1/test1/uuid/test2/uuid/test3", LoggerFilter.removeUUID("/api/v1/test1/ddc35b98-b976-e611-bcde-06538d331ea1/test2/ddc35b98-b976-e611-bcde-06538d331ea1/test3"));
        assertEquals("/api/v1/test1/uuid/test2/uuid/test3", LoggerFilter.removeUUID("/api/v1/test1/DDC35B98-B976-e611-bcde-06538d331ea1/test2/ddc35b98-b976-e611-bcde-06538d331ea1/test3"));
        assertEquals("https://api.learning-genie.com/api/v1/students/uuid/scores/statistics", LoggerFilter.removeUUID("https://api.learning-genie.com/api/v1/students/633d0471-7b48-e611-bcde-06538d331ea1/scores/statistics"));
    }
}