package com.learninggenie.api.dao;

import com.learninggenie.api.config.TestBase;
import com.learninggenie.common.data.entity.CreditChangeType;
import com.learninggenie.common.data.entity.CreditsRecordEntity;
import com.learninggenie.common.data.entity.MetaDataKey;
import com.learninggenie.common.data.entity.UserMetaDataEntity;
import com.learninggenie.common.data.repository.CreditRecordRepository;
import com.learninggenie.common.data.repository.UserMetaDataRepository;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@Ignore
public class CreditsRecordEntityDaoTest extends TestBase {
    @Autowired
    UserMetaDataRepository userMetaDataRepository;
    @Autowired
    CreditRecordRepository creditRecordRepository;
    @Test
    public void testFindByMetaKeyAndMetaValue(){
        UserMetaDataEntity usersMetaDataEntity = userMetaDataRepository.findTop1ByMetaKeyAndMetaValue(MetaDataKey.STRIPE_CUSTOMER_ID.toString(), "123");
        System.out.println(usersMetaDataEntity);
    }
    @Test
    public void testgetCreditRecord() throws Exception{
        List<CreditsRecordEntity> list = creditRecordRepository.findByUserIdOrderByCreateAtUtcDesc("29154AA5-2529-E511-98B5-02678A71958D");
        for(CreditsRecordEntity entity:list){
            System.out.print(entity.getCredits());
            System.out.println(entity.getChange().equals(CreditChangeType.INCREASE));
        }

    }
}
