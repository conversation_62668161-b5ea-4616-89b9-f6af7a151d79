package com.learninggenie.api.dao;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.toolkit.ChainWrappers;
import com.learninggenie.common.data.dao.training.impl.TrainingResourceDaoImpl;
import com.learninggenie.common.data.entity.TrainingMediaEntity;
import com.learninggenie.common.data.mapper.mybatisplus.training.TrainingMapper;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;

/**
 * 培训视频数据库查询测试类
 */
@RunWith(MockitoJUnitRunner.class)
public class TrainingResourceDaoImplTest {

    @InjectMocks
    private TrainingResourceDaoImpl trainingResourceDao;

    @Mock
    private TrainingMapper trainingMapper;

    /**
     * 测试查通过 ID 获取培训视频信息
     */
    @Test
    public void testGetTrainingMediasByIdsWithDeleted() {
        // 模拟 Lambda 查询静态类
        try (MockedStatic<ChainWrappers> chainWrappersMocked = mockStatic(ChainWrappers.class)) {
            LambdaQueryChainWrapper<TrainingMediaEntity> wrapper = new LambdaQueryChainWrapper<>(trainingMapper);
            // 模拟链式查询包装类
            chainWrappersMocked.when(() -> ChainWrappers.lambdaQueryChain(any())).thenReturn(wrapper);
            // 执行查询
            List<TrainingMediaEntity> entities = trainingResourceDao.getTrainingMediasByIdsWithDeleted(new ArrayList<>());
            // 验证
            Assert.assertEquals(0, entities.size());
        }
    }

    /**
     * 测试通过模块名和等级获取视频 ID
     */
    @Test
    public void testGetMediaIdsByModuleAndTypesWithDeleted() {
        // 模拟 Lambda 查询静态类
        try (MockedStatic<ChainWrappers> chainWrappersMocked = mockStatic(ChainWrappers.class)) {
            LambdaQueryChainWrapper<TrainingMediaEntity> wrapper = new LambdaQueryChainWrapper<>(trainingMapper);
            // 模拟链式查询包装类
            chainWrappersMocked.when(() -> ChainWrappers.lambdaQueryChain(any())).thenReturn(wrapper);
            // 执行查询
            List<String> results = trainingResourceDao.getMediaIdsByModuleAndTypesWithDeleted(null, new ArrayList<>());
            // 验证
            Assert.assertEquals(0, results.size());
        }
    }

}
