package com.learninggenie.api.dao;

import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.toolkit.ChainWrappers;
import com.learninggenie.common.data.dao.lesson2.LessonVersionDao;
import com.learninggenie.common.data.dao.lesson2.impl.LessonVersionDaoImpl;
import com.learninggenie.common.data.entity.lesson2.LessonVersionEntity;
import com.learninggenie.common.data.mapper.mybatisplus.lesson2.LessonVersionMapper;
import org.apache.ibatis.builder.MapperBuilderAssistant;
import org.junit.AfterClass;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.Mockito.*;

/**
 * 课程版本 Dao 层测试类
 */
@RunWith(MockitoJUnitRunner.class)
public class LessonVersionDaoImplTest {
    private static MockedStatic<ChainWrappers> chainWrappersMockedStatic;

    @InjectMocks
    private LessonVersionDaoImpl lessonVersionDao;

    @Mock
    private LessonVersionMapper lessonVersionMapper;

    /**
     * 初始化 TableInfoHelper.
     */
    @BeforeClass
    public static void beforeClass() {
        chainWrappersMockedStatic = mockStatic(ChainWrappers.class);
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), LessonVersionEntity.class);
    }

    /**
     * 关闭 chainWrappersMockedStatic.
     */
    @AfterClass
    public static void afterClass() {
        chainWrappersMockedStatic.close();
    }

    /**
     * 测试获取最新版本课程
     */
    @Test
    public void testGetLastVersion() {
        // 模拟课程版本实体
        List<LessonVersionEntity> lessonVersionEntities = new ArrayList<>();
        LessonVersionEntity lessonVersionEntity1 = new LessonVersionEntity(); // 课程最新版本
        lessonVersionEntity1.setId("lastVersionId");
        lessonVersionEntity1.setLessonId("lessonId");
        lessonVersionEntities.add(lessonVersionEntity1);
        LessonVersionEntity lessonVersionEntity2 = new LessonVersionEntity(); // 课程旧版本
        lessonVersionEntity2.setId("lastVersionId2");
        lessonVersionEntity2.setLessonId("lessonId");
        lessonVersionEntities.add(lessonVersionEntity2);

        // 模拟 Lambda 查询静态类
        LambdaQueryChainWrapper<LessonVersionEntity> lambdaQuery = new LambdaQueryChainWrapper<>(lessonVersionMapper);
        chainWrappersMockedStatic.when(() -> ChainWrappers.lambdaQueryChain(lessonVersionMapper)).thenReturn(lambdaQuery);
        // 模拟链式查询包装类
        when(lambdaQuery.eq(LessonVersionEntity::getLessonId, "lessonId")
                .orderByDesc(LessonVersionEntity::getCreateAtUtc)
                .list())
                .thenReturn(lessonVersionEntities);

        // 执行查询
        LessonVersionEntity lessonVersion = lessonVersionDao.getLastByLessonId("lessonId");

        // 验证不为空
        assertNotNull(lessonVersion);
        // 验证结果，课程最新版本 ID 应为 lastVersionId
        assertEquals("lastVersionId", lessonVersion.getId());
    }
}
