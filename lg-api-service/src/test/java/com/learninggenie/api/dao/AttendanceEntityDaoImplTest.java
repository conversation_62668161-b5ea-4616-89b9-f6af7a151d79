package com.learninggenie.api.dao;

import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.toolkit.ChainWrappers;
import com.learninggenie.common.data.dao.impl.AttendanceEntityDaoImpl;
import com.learninggenie.common.data.entity.AttendanceEntity;
import com.learninggenie.common.data.mapper.mybatisplus.dhc.AttendanceMapper;
import com.learninggenie.common.data.model.form.common.HealthCheckModel;
import com.learninggenie.common.utils.TimeUtil;
import org.apache.ibatis.builder.MapperBuilderAssistant;
import org.junit.Assert;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AttendanceEntityDaoImplTest {
    @InjectMocks
    private AttendanceEntityDaoImpl attendanceEntityDao;

    @Mock
    private AttendanceMapper attendanceMapper;

    @Mock
    private JdbcTemplate jdbcTemplate;

    @Mock
    @Qualifier("shardingJdbcTemplate")
    private JdbcTemplate shardingTemplate;

    private MockedStatic<ChainWrappers> chainWrappersMockedStatic;

    /**
     * 初始化 TableInfoHelper
     */
    @BeforeAll
    public static void beforeClass() {
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), AttendanceEntity.class);
    }

    /**
     * 初始化 chainWrappersMockedStatic
     */
    @BeforeEach
    public void beforeMethod() {
        chainWrappersMockedStatic = mockStatic(ChainWrappers.class);
    }

    /**
     * 关闭 chainWrappersMockedStatic
     */
    @AfterEach
    public void afterMethod() {
        chainWrappersMockedStatic.close();
    }

    /**
     * 测试 getHealthCheckByCenterAndGroup
     */
    @Test
    void testFindNoHealthCheckChildByGroupIds() {
        AttendanceMapper mapper = this.attendanceMapper;
        ReflectionTestUtils.setField(attendanceEntityDao, "baseMapper", mapper);
        final LambdaQueryChainWrapper<AttendanceEntity> lambdaQuery = new LambdaQueryChainWrapper<>(mapper);
        chainWrappersMockedStatic.when(() -> ChainWrappers.lambdaQueryChain(mapper)).thenReturn(lambdaQuery);
        List<HealthCheckModel> healthCheckModels = new ArrayList<>();
        healthCheckModels.add(new HealthCheckModel());
        when(shardingTemplate.query(anyString(), any(Object[].class), any(RowMapper.class))).thenReturn(healthCheckModels);
        List<HealthCheckModel> noHealthCheckChildByGroupIds = attendanceEntityDao.findNoHealthCheckChildByGroupIds(Arrays.asList("001"), TimeUtil.getUtcNow());

        Assert.assertEquals(healthCheckModels.size(), noHealthCheckChildByGroupIds.size());
    }

    /**
     * 测试 listSignAttendanceByChildIds
     */
    @Test
    void testListSignAttendanceByChildIds() {
        AttendanceMapper mapper = this.attendanceMapper;
        ReflectionTestUtils.setField(attendanceEntityDao, "baseMapper", mapper);
        final LambdaQueryChainWrapper<AttendanceEntity> lambdaQuery = new LambdaQueryChainWrapper<>(mapper);
        chainWrappersMockedStatic.when(() -> ChainWrappers.lambdaQueryChain(mapper)).thenReturn(lambdaQuery);
        List<AttendanceEntity> attendanceEntities = new ArrayList<>();
        attendanceEntities.add(new AttendanceEntity());
        when(attendanceMapper.selectList(any())).thenReturn(attendanceEntities);
        List<AttendanceEntity> attendanceEntityList = attendanceEntityDao.listSignAttendanceByChildIds(Arrays.asList("001"), "2023-09-06");

        Assert.assertEquals(attendanceEntities.size(), attendanceEntityList.size());
    }
}