package com.learninggenie.api.dao;

import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.baomidou.mybatisplus.extension.toolkit.ChainWrappers;
import com.learninggenie.common.data.dao.impl.HealthQuarantineDaoImpl;
import com.learninggenie.common.data.mapper.mybatisplus.dhc.HealthQuarantineMapper;
import com.learninggenie.common.data.model.FormsResponseRecord;
import com.learninggenie.common.data.model.HealthQuarantineEntity;
import com.learninggenie.common.utils.TimeUtil;
import org.apache.ibatis.builder.MapperBuilderAssistant;
import org.junit.Assert;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class HealthQuarantineDaoImplTest {
    @InjectMocks
    private HealthQuarantineDaoImpl healthQuarantineDao;

    @Mock
    private HealthQuarantineMapper healthQuarantineMapper;

    @Mock
    private JdbcTemplate jdbcTemplate;

    private MockedStatic<ChainWrappers> chainWrappersMockedStatic;

    /**
     * 初始化 TableInfoHelper
     */
    @BeforeAll
    public static void beforeClass() {
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), FormsResponseRecord.class);
    }

    /**
     * 初始化 chainWrappersMockedStatic
     */
    @BeforeEach
    public void beforeMethod() {
        chainWrappersMockedStatic = mockStatic(ChainWrappers.class);
    }

    /**
     * 关闭 chainWrappersMockedStatic
     */
    @AfterEach
    public void afterMethod() {
        chainWrappersMockedStatic.close();
    }

    /**
     * 测试 GetChildIdAndReasonByDate 方法
     */
    @Test
    void testGetChildIdAndReasonByDate() {
        List<HealthQuarantineEntity> healthQuarantineEntities = Arrays.asList(new HealthQuarantineEntity());
        when(jdbcTemplate.query(anyString(), any(Object[].class), eq(com.learninggenie.common.data.mapper.HealthQuarantineMapper.HEALTHQRARANTINEROWMAPPER))).thenReturn(healthQuarantineEntities);
        List<HealthQuarantineEntity> quarantines = healthQuarantineDao.getChildIdAndReasonByDate(Arrays.asList("001"), TimeUtil.getUtcNow());
        List<HealthQuarantineEntity> quarantines2 = healthQuarantineDao.getChildIdAndReasonByDate(Collections.emptyList(), TimeUtil.getUtcNow());

        Assert.assertEquals(1, quarantines.size());
        Assert.assertEquals(0, quarantines2.size());
    }

}