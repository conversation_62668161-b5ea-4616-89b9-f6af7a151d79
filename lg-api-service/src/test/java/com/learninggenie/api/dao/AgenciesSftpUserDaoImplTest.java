package com.learninggenie.api.dao;

import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.toolkit.ChainWrappers;
import com.learninggenie.common.data.dao.agencies.impl.AgenciesSftpUserDaoImpl;
import com.learninggenie.common.data.entity.export.AgenciesSftpuser;
import com.learninggenie.common.data.mapper.mybatisplus.agencies.AgenciesSftpuserMapper;
import org.apache.ibatis.builder.MapperBuilderAssistant;
import org.junit.AfterClass;
import org.junit.Test;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import static net.sf.ezmorph.test.ArrayAssertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

/**
 * Create by hxl 2023/08/17.
 * EnrollmentSnapshotDaoImpl 的测试类
 */
@RunWith(MockitoJUnitRunner.class)
public class AgenciesSftpUserDaoImplTest {

    private static final String BASE_MAPPER = "baseMapper";

    private static MockedStatic<ChainWrappers> chainWrappersMockedStatic;

    @InjectMocks
    private AgenciesSftpUserDaoImpl agenciesSftpUserDao;

    @Mock
    private AgenciesSftpuserMapper agenciesSftpuserMapper;

    @BeforeAll
    public static void beforeClass() {
        chainWrappersMockedStatic = mockStatic(ChainWrappers.class);
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), AgenciesSftpuser.class);
    }

    /**
     * 关闭 chainWrappersMockedStatic.
     */
    @AfterClass
    public static void afterClass() {
        if (chainWrappersMockedStatic != null) {
            chainWrappersMockedStatic.close();
        }
    }

    @BeforeEach
    public void setup() {
        // 调用待测试的方法
        ReflectionTestUtils.setField(agenciesSftpUserDao, BASE_MAPPER, agenciesSftpuserMapper);
        final LambdaQueryChainWrapper<AgenciesSftpuser> lambdaUpdate = new LambdaQueryChainWrapper<>(agenciesSftpuserMapper);
        lambdaUpdate.setEntity(new AgenciesSftpuser());
        lambdaUpdate.setEntityClass(AgenciesSftpuser.class);
        chainWrappersMockedStatic.when(() -> ChainWrappers.lambdaQueryChain(agenciesSftpuserMapper)).thenReturn(lambdaUpdate);
    }

    @Test
    public void testGetExportUserByUsername_Success() {
        // Arrange
        String username = "testUser";
        String tenantId = "testTenantId";
        AgenciesSftpuser expectedUser = new AgenciesSftpuser();

        when(agenciesSftpUserDao.getOne(any())).thenReturn(expectedUser);

        // Act
        AgenciesSftpuser actualUser = agenciesSftpUserDao.getExportUserByUsername(username, tenantId);

        // Assert
        assertEquals(expectedUser, actualUser);
    }

    @Test
    public void testGetExportUserByUsername_NoUserFound() {
        // Arrange
        String username = "nonExistingUser";
        String tenantId = "testTenantId";

        when(agenciesSftpUserDao.getOne(any())).thenReturn(null);

        // Act
        AgenciesSftpuser actualUser = agenciesSftpUserDao.getExportUserByUsername(username, tenantId);

        // Assert
        assertNull(actualUser);
    }
}
