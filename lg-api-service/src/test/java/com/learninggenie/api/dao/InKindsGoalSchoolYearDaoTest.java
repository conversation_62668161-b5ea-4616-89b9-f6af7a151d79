package com.learninggenie.api.dao;

import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.toolkit.ChainWrappers;
import com.learninggenie.common.data.dao.inkind.impl.InKindsGoalSchoolYearDaoImpl;
import com.learninggenie.common.data.entity.inkind.InKindsGoalSchoolYear;
import com.learninggenie.common.data.mapper.mybatisplus.inkind.InKindsGoalSchoolYearMapper;
import org.apache.ibatis.builder.MapperBuilderAssistant;
import org.junit.AfterClass;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * InKindsGoalSchoolYearDao 单元测试类.
 */
@RunWith(MockitoJUnitRunner.class)
public class InKindsGoalSchoolYearDaoTest {
    private static MockedStatic<ChainWrappers> chainWrappersMockedStatic;

    @InjectMocks
    private InKindsGoalSchoolYearDaoImpl inKindsGoalSchoolYearDaoImpl;

    @Mock
    private InKindsGoalSchoolYearMapper inKindsGoalSchoolYearMapper;

    /**
     * 初始化 TableInfoHelper.
     */
    @BeforeClass
    public static void beforeClass() {
        chainWrappersMockedStatic = mockStatic(ChainWrappers.class);
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), InKindsGoalSchoolYear.class);
    }

    /**
     * 关闭 chainWrappersMockedStatic.
     */
    @AfterClass
    public static void afterClass() {
        chainWrappersMockedStatic.close();
    }

    /**
     * 测试根据 agencyId 和 schoolYear 查询删除.
     */
    @Test
    public void testDeleteByAgencyIdAndSchoolYear() {
        // 准备测试数据
        final String agencyId = "agencyId";
        final String schoolYear = "schoolYear";

        // 调用待测试的方法
        final LambdaUpdateChainWrapper<InKindsGoalSchoolYear> lambdaUpdate = new LambdaUpdateChainWrapper<>(inKindsGoalSchoolYearMapper);
        lambdaUpdate.setEntity(new InKindsGoalSchoolYear());
        lambdaUpdate.setEntityClass(InKindsGoalSchoolYear.class);
        chainWrappersMockedStatic.when(() -> ChainWrappers.lambdaUpdateChain(inKindsGoalSchoolYearMapper)).thenReturn(lambdaUpdate);

        // 验证结果
        inKindsGoalSchoolYearDaoImpl.deleteByAgencyIdAndSchoolYear(agencyId, schoolYear);
        verify(inKindsGoalSchoolYearMapper, times(1)).update(any(), any());
    }

}
