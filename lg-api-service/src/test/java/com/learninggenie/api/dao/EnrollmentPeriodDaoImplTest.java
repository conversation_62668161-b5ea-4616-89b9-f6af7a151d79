package com.learninggenie.api.dao;

import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.toolkit.ChainWrappers;
import com.learninggenie.common.data.dao.enrollment.impl.EnrollmentPeriodDaoImpl;
import com.learninggenie.common.data.entity.enrollment.EnrollmentPeriodEntity;
import com.learninggenie.common.data.mapper.mybatisplus.enrollment.EnrollmentPeriodMapper;
import org.apache.ibatis.builder.MapperBuilderAssistant;
import org.junit.AfterClass;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.Mockito.mockStatic;

/**
 * Create by hxl 2023/08/17.
 * EnrollmentPeriodDaoImpl 的测试类
 */
@RunWith(MockitoJUnitRunner.class)
public class EnrollmentPeriodDaoImplTest {

    private static final String BASE_MAPPER = "baseMapper";

    private static MockedStatic<ChainWrappers> chainWrappersMockedStatic;

    @InjectMocks
    private EnrollmentPeriodDaoImpl enrollmentPeriodDao;

    @Mock
    private EnrollmentPeriodMapper enrollmentPeriodMapper;

    @BeforeClass
    public static void beforeClass() {
        chainWrappersMockedStatic = mockStatic(ChainWrappers.class);
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), EnrollmentPeriodEntity.class);
    }

    /**
     * 关闭 chainWrappersMockedStatic.
     */
    @AfterClass
    public static void afterClass() {
        if (chainWrappersMockedStatic != null) {
            chainWrappersMockedStatic.close();
        }
    }

    @Before
    public void setup() {
        // 调用待测试的方法
        ReflectionTestUtils.setField(enrollmentPeriodDao, BASE_MAPPER, enrollmentPeriodMapper);
        final LambdaQueryChainWrapper<EnrollmentPeriodEntity> lambdaUpdate = new LambdaQueryChainWrapper<>(enrollmentPeriodMapper);
        lambdaUpdate.setEntity(new EnrollmentPeriodEntity());
        lambdaUpdate.setEntityClass(EnrollmentPeriodEntity.class);
        chainWrappersMockedStatic.when(() -> ChainWrappers.lambdaQueryChain(enrollmentPeriodMapper)).thenReturn(lambdaUpdate);
    }

    @Test
    public void testSelectByEnrollmentIdsAndDateRange_CurrentDate() {
        // 创建Mock对象
        List<String> enrollmentIds = new ArrayList<>();
        enrollmentIds.add("1");
        enrollmentIds.add("2");

        Date currentDate = new Date();
        // 调用被测试方法
        List<EnrollmentPeriodEntity> result = enrollmentPeriodDao.selectByEnrollmentIdsAndDateRange(enrollmentIds, currentDate);

        // 验证行为
        // 断言结果
        assertNotNull(result);
    }

    @Test
    public void testSelectByEnrollmentIdsAndDateRange_BeginTimeEndTime() {
        // 创建Mock对象
        // 创建Mock对象
        List<String> enrollmentIds = new ArrayList<>();
        enrollmentIds.add("1");
        enrollmentIds.add("2");

        Date beginTime = new Date();
        Date endTime = new Date();
        // 调用被测试方法
        List<EnrollmentPeriodEntity> result = enrollmentPeriodDao.selectByEnrollmentIdsAndDateRange(enrollmentIds, beginTime, endTime);

        // 验证行为

        // 断言结果
        assertNotNull(result);
    }

    @Test
    public void testSelectByEnrollmentIdsAndAliasLike() {
        // 创建Mock对象
        List<String> enrollmentIds = new ArrayList<>();
        enrollmentIds.add("1");
        enrollmentIds.add("2");

        String schoolYear = "schoolYear";
        // 调用被测试方法
        List<EnrollmentPeriodEntity> result = enrollmentPeriodDao.selectByEnrollmentIdsAndAliasLike(enrollmentIds, schoolYear);

        // 验证行为

        // 断言结果
        assertNotNull(result);
    }

    @Test
    public void testGetFrameworkIdsByTenantId() {
        // 创建Mock对象
        String tenantId = "tenantId";

        // 调用被测试方法
        List<String> result = enrollmentPeriodDao.getFrameworkIdsByTenantId(tenantId);

        // 断言结果
        assertNotNull(result);
    }

    @Test
    public void testGetCountBySchoolYearAndFrameworkIds() {
        String schoolYear = "schoolYear";
        List<String> scoreFrameworkIds = new ArrayList<>();
        scoreFrameworkIds.add("1");
        scoreFrameworkIds.add("2");
        String agencyId = "agencyId";

        // 调用被测试方法
        long result = enrollmentPeriodDao.getCountBySchoolYearAndFrameworkIds(schoolYear, scoreFrameworkIds, agencyId);


        // 断言结果
        assertEquals(0, result);
    }
}
