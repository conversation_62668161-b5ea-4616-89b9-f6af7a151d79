package com.learninggenie.api.dao;

import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.toolkit.ChainWrappers;
import com.learninggenie.common.data.dao.groups.impl.GroupsMetaDataEntityDaoImpl;
import com.learninggenie.common.data.entity.groups.GroupsMetaDataEntity;
import com.learninggenie.common.data.mapper.mybatisplus.groups.GroupsMateDataMapper;
import org.apache.ibatis.builder.MapperBuilderAssistant;
import org.junit.AfterClass;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.mockStatic;

/**
 * Create by hxl 2023/08/17.
 * GroupsMetaDataEntityDao 的测试类
 */
@RunWith(MockitoJUnitRunner.class)
public class GroupsMetaDataEntityDaoTtest {

    private static final String BASE_MAPPER = "baseMapper";

    private static MockedStatic<ChainWrappers> chainWrappersMockedStatic;

    @InjectMocks
    private GroupsMetaDataEntityDaoImpl groupsMetaDataEntityDao;

    @Mock
    private GroupsMateDataMapper groupsMateDataMapper;

    @BeforeClass
    public static void beforeClass() {
        chainWrappersMockedStatic = mockStatic(ChainWrappers.class);
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), GroupsMetaDataEntity.class);
    }

    /**
     * 关闭 chainWrappersMockedStatic.
     */
    @AfterClass
    public static void afterClass() {
        if (chainWrappersMockedStatic != null) {
            chainWrappersMockedStatic.close();
        }
    }

    @Before
    public void setup() {
        // 调用待测试的方法
        ReflectionTestUtils.setField(groupsMetaDataEntityDao, BASE_MAPPER, groupsMateDataMapper);
        final LambdaQueryChainWrapper<GroupsMetaDataEntity> lambdaUpdate = new LambdaQueryChainWrapper<>(groupsMateDataMapper);
        lambdaUpdate.setEntity(new GroupsMetaDataEntity());
        lambdaUpdate.setEntityClass(GroupsMetaDataEntity.class);
        chainWrappersMockedStatic.when(() -> ChainWrappers.lambdaQueryChain(groupsMateDataMapper)).thenReturn(lambdaUpdate);
    }

    @Test
    public void testSaveMetaData() {
        // 创建Mock对象
        GroupsMetaDataEntity groupsMetaDataEntity = new GroupsMetaDataEntity();
        groupsMetaDataEntity.setGroupId("1");

        // 调用被测试方法
        groupsMetaDataEntityDao.saveMetaData(groupsMetaDataEntity);

    }

    @Test
    public void testUpdateMetaData() {
        // 创建Mock对象
        GroupsMetaDataEntity groupsMetaDataEntity = new GroupsMetaDataEntity();
        groupsMetaDataEntity.setGroupId("1");

        // 调用被测试方法
        groupsMetaDataEntityDao.updateMetaData(groupsMetaDataEntity);

    }

    @Test
    public void testGetPendingRecords() {
        String groupId = "1";
        String metadata = "1";
        // 调用被测试方法
        GroupsMetaDataEntity result = groupsMetaDataEntityDao.getByGroupIdAndMetaKey(groupId, metadata);

        // 验证行为

        // 断言结果
        assertNull(result);
    }

    @Test
    public void testGetBySettingIdAndExportDates() {

        List<String> groupIds = new ArrayList<>();
        String groupId = "1";
        groupIds.add(groupId);
        String metadata = "1";
        // 调用被测试方法
        List<GroupsMetaDataEntity> result = groupsMetaDataEntityDao.getByGroupIdsAndKey(groupIds, metadata);

        // 断言结果
        assertNotNull(result);
    }


}
