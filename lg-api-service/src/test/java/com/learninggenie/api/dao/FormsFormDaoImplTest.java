package com.learninggenie.api.dao;

import com.learninggenie.common.data.dao.impl.FormsFormDaoImpl;
import com.learninggenie.common.data.mapper.FormsFormMapper;
import com.learninggenie.common.data.model.form.MediaFormEntity;
import com.learninggenie.common.utils.TimeUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Date;

import static org.junit.Assert.*;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class FormsFormDaoImplTest {

    @Mock
    private JdbcTemplate jdbcTemplate;

    @InjectMocks
    private FormsFormDaoImpl formsFormDao;


    /**
     * 测试 视频表单获取方法
     */
    @Test
    public void testGetMediaFormByVideo() {
        // 初始化测试数据
        String playListId = "playlist123";
        String videoId = "video123";
        String agencyId = "agency123";
        String mediaId = "media123";
        String formId = "form123";
        Date utcNow = TimeUtil.getUtcNow();
        // 创建期望的返回实体
        MediaFormEntity mediaFormEntity = new MediaFormEntity();
        mediaFormEntity.setFormId(formId);
        mediaFormEntity.setCreateAtUtc(utcNow);
        mediaFormEntity.setUpdateAtUtc(utcNow);
        mediaFormEntity.setDeleted(false);
        mediaFormEntity.setId(mediaId);
        mediaFormEntity.setAgencyId(agencyId);
        mediaFormEntity.setPlayListId(playListId);
        mediaFormEntity.setVideoId(videoId);
        // 配置 Mock ，当调用 jdbcTemplate 的 queryForObject 方法时，返回上面创建的期望实体
        when(jdbcTemplate.queryForObject(FormsFormMapper.GET_MEDIA_FORM_BY_VIDEO, new Object[]{playListId, videoId, agencyId}, FormsFormMapper.MEDIA_FORM_ENTITY_ROW_MAPPER))
                .thenReturn(mediaFormEntity);

        // 执行测试方法
        MediaFormEntity resultEntity = formsFormDao.getMediaFormByVideo(playListId, videoId, agencyId);

        // 验证结果
        assertNotNull(resultEntity);
        assertEquals(mediaId, resultEntity.getId());
        assertEquals(agencyId, resultEntity.getAgencyId());
        assertEquals(playListId, resultEntity.getPlayListId());
        assertEquals(videoId, resultEntity.getVideoId());
        assertEquals(formId, resultEntity.getFormId());
        assertEquals(utcNow, resultEntity.getCreateAtUtc());
        assertEquals(utcNow, resultEntity.getUpdateAtUtc());
        assertFalse(resultEntity.isDeleted());
    }

}
