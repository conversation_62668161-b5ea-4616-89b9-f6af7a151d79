package com.learninggenie.api.dao;

import com.learninggenie.api.config.TestBase;
import com.learninggenie.common.data.dao.ScoreDao;
import com.learninggenie.common.data.model.StudentScoreEntity;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
@Ignore
public class ScoreDaoTest extends TestBase {
    @Autowired
    private ScoreDao scoreDao;

    @Ignore
    @Test
    public void testGetScoreRecords(){
        List<StudentScoreEntity> records=scoreDao.get("FB616A30-D8ED-E411-AF66-02C72B94B99B", "FB616A30-D8ED-E411-AF66-02C72B94B99B");
        System.out.println(records.size());
        for(StudentScoreEntity r:records){
            System.out.println(r.getId());
        }
    }

    @Ignore
    @Test
    public void testSaveScoreRecords(){
        StudentScoreEntity record1=new StudentScoreEntity();
        record1.setId("001001001");
        record1.setStudentId("001");
        record1.setNoteId("001");
        record1.setDomainId("001");
        record1.setLevelId("001");

        StudentScoreEntity record2=new StudentScoreEntity();
        record2.setId("002002002");
        record2.setStudentId("002");
        record2.setNoteId("002");
        record2.setDomainId("002");
        record2.setLevelId("002");

        List<StudentScoreEntity> records=new ArrayList<>();
        records.add(record1);
        records.add(record2);

        scoreDao.batchSave(records, "", "");
    }
    @Ignore
    @Test
    public void getGroupScore(){
        List list = scoreDao.getByGroup("78BC21C1-C9DD-E411-8BF9-02DEE0566A18");
        System.out.println(list.size());
    }
}
