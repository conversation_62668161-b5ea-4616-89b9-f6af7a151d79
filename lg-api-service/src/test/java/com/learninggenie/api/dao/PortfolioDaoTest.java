package com.learninggenie.api.dao;

import com.learninggenie.api.config.TestBase;
import com.learninggenie.common.data.dao.PortfolioDao;
import com.learninggenie.common.data.model.ScoreTemplateEntity;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
@Ignore
public class PortfolioDaoTest extends TestBase{
    @Autowired
    private PortfolioDao portfolioDao;

    @Ignore
    @Test
    public void testGetScoreTemplate(){
        ScoreTemplateEntity entity=portfolioDao.loadScoreTemplate("001");
        System.out.println(entity.getDomainLevelsJson());
        Assert.assertNotNull(entity);
    }

    @Ignore
    @Test
    public void testCreateScoreTemplate(){
        ScoreTemplateEntity entity=new ScoreTemplateEntity();
        entity.setPortfolioId("001");
        portfolioDao.saveScoreTemplate(entity);

        ScoreTemplateEntity rel=portfolioDao.loadScoreTemplate("001");
        Assert.assertNotNull(rel);
    }
}
