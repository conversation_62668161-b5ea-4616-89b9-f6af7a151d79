package com.learninggenie.api.dao;

import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.toolkit.ChainWrappers;
import com.learninggenie.common.data.dao.impl.FormsResponseRecordDaoImpl;
import com.learninggenie.common.data.enums.FormResponseRecordType;
import com.learninggenie.common.data.mapper.mybatisplus.report.FormsResponseRecordMapper;
import com.learninggenie.common.data.model.FormsResponseRecord;
import com.learninggenie.common.data.model.HealthQuarantineEntity;
import com.learninggenie.common.sharding.ShardingProvider;
import org.apache.ibatis.builder.MapperBuilderAssistant;
import org.junit.Assert;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class FormsResponseRecordDaoImplTest {
    @InjectMocks
    private FormsResponseRecordDaoImpl formsResponseRecordDao;

    @Mock
    private FormsResponseRecordMapper formsResponseRecordMapper;

    @Mock
    private JdbcTemplate jdbcTemplate;

    @Mock
    @Qualifier("shardingJdbcTemplate")
    private JdbcTemplate shardingTemplate;

    @Mock
    private ShardingProvider shardingProvider;

    private MockedStatic<ChainWrappers> chainWrappersMockedStatic;

    /**
     * 初始化 TableInfoHelper
     */
    @BeforeAll
    public static void beforeClass() {
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), HealthQuarantineEntity.class);
    }

    /**
     * 初始化 chainWrappersMockedStatic
     */
    @BeforeEach
    public void beforeMethod() {
        chainWrappersMockedStatic = mockStatic(ChainWrappers.class);
    }

    /**
     * 关闭 chainWrappersMockedStatic
     */
    @AfterEach
    public void afterMethod() {
        chainWrappersMockedStatic.close();
    }

    /**
     * 测试 getRecordsByChildIdsAndDateAndType
     */
    @Test
    void testGetRecordsByChildIdsAndDateAndType() {
        FormsResponseRecordMapper mapper = this.formsResponseRecordMapper;
        ReflectionTestUtils.setField(formsResponseRecordDao, "baseMapper", mapper);
        final LambdaQueryChainWrapper<FormsResponseRecord> lambdaQuery = new LambdaQueryChainWrapper<>(mapper);
        chainWrappersMockedStatic.when(() -> ChainWrappers.lambdaQueryChain(mapper)).thenReturn(lambdaQuery);
        List<FormsResponseRecord> records = formsResponseRecordDao.getRecordsByChildIdsAndDateAndType(Arrays.asList("001"), "2023-09-06", FormResponseRecordType.HEALTH_CHECK_FORM.toString());

        Assert.assertEquals(0, records.size());
    }

    /**
     * 测试获取小孩签到日历记录
     */
    @Test
    public void testGetChildFormCalendar() {
        String sql = "test get child form calendar sql";
        // 模拟转换分表 SQL
        when(shardingProvider.convertShardingSqlWithChild(anyString(), anyString())).thenReturn(sql);
        // 模拟结果数据
        String childId = "test-child-id";
        FormsResponseRecord record = new FormsResponseRecord();
        record.setId("test-record-id");
        record.setChildId(childId);
        record.setFormId("test-form-id");
        // 模拟查询结果
        when(shardingTemplate.query(anyString(), any(Object[].class), eq(com.learninggenie.common.data.mapper.FormsResponseRecordMapper.formsResponseRecord))).thenReturn(Collections.singletonList(record));
        // 执行测试方法
        List<FormsResponseRecord> records = formsResponseRecordDao.getChildFormCalendar(childId, "2024-01-01");
        // 验证结果
        Assertions.assertEquals(1, records.size()); // 结果数量为 1
        Assertions.assertEquals(record.getId(), records.get(0).getId()); // 验证记录 ID
        Assertions.assertEquals(record.getChildId(), records.get(0).getChildId()); // 验证小孩 ID
        Assertions.assertEquals(record.getFormId(), records.get(0).getFormId()); // 验证问卷 ID
        verify(shardingProvider, times(1)).convertShardingSqlWithChild(anyString(), anyString()); // 验证分表 SQL 转换
    }

}