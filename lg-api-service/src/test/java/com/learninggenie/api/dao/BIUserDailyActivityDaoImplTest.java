package com.learninggenie.api.dao;

import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBMapper;
import com.amazonaws.services.dynamodbv2.datamodeling.PaginatedQueryList;
import com.learninggenie.common.data.dao.bi.BIUserDailyActivityDao;
import com.learninggenie.common.data.dao.bi.impl.BIUserDailyActivityDaoImpl;
import com.learninggenie.common.data.entity.bi.activity.BIUserDailyActivityEntity;
import com.learninggenie.common.exception.LearningGenieRuntimeException;
import org.junit.Assert;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.List;

import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class BIUserDailyActivityDaoImplTest {

    @InjectMocks
    private BIUserDailyActivityDaoImpl biUserDailyActivityDao;

    @Mock
    private DynamoDBMapper dynamoDBMapper;

    /**
     * 测试批量保存
     */
    @Test
    public void testBatchSave() {
        // 没有错误，返回结果为空
        when(dynamoDBMapper.batchSave(anyList())).thenReturn(null);
        // 执行方法
        biUserDailyActivityDao.batchSave(Collections.emptyList());
    }

    /**
     * 测试批量添加，有错误的情况
     */
    @Test
    public void testBatchSaveWithFailed() {
        // 模拟错误
        when(dynamoDBMapper.batchSave(anyList())).thenReturn(Collections.singletonList(new DynamoDBMapper.FailedBatch()));
        // 执行方法，断言异常
        Assert.assertThrows(LearningGenieRuntimeException.class, () -> biUserDailyActivityDao.batchSave(Collections.emptyList()));
    }

    /**
     * 测试获取活跃数据
     */
    @Test
    public void testGetDailyActivities() {
        // 活跃数据
        when(dynamoDBMapper.query(any(), any())).thenReturn(null);
        // 执行方法
        List<BIUserDailyActivityEntity> dailyActivities = biUserDailyActivityDao.getDailyActivities("agencyId", "moduleCode", "userTypeCode", null, null);
        // 验证结果
        Assertions.assertNull(dailyActivities);
    }

}
