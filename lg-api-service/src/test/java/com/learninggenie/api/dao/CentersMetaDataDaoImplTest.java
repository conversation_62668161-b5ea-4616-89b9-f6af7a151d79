package com.learninggenie.api.dao;

import com.learninggenie.common.data.dao.impl.CentersMetaDataDaoImpl;
import com.learninggenie.common.data.entity.CenterEntity;
import com.learninggenie.common.data.entity.CenterMetaDataEntity;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * 学校 metadata 的 dao impl 测试类
 *
 * <AUTHOR>
 * @date 2023/11/09
 */
@RunWith(MockitoJUnitRunner.class)
public class CentersMetaDataDaoImplTest {

    @InjectMocks
    private CentersMetaDataDaoImpl centersMetaDataDao;

    @Mock
    private JdbcTemplate jdbcTemplate;

    /**
     * 测试-创建 metadata 数据(更新数据)
     */
    @Test
    public void testCreateMeta() {
        // 定义 mock 方法的返回值
        // 定义从数据库中查询得到的 centerMetaDataEntities
        List<CenterMetaDataEntity> centerMetaDataEntitiesInDB = new ArrayList<>();
        // 定义一个 centerMetaDataEntity
        CenterMetaDataEntity centerMetaDataEntity = new CenterMetaDataEntity();
        // 设置 centerMetaDataEntity 的属性
        // 设置 center meta 的 id
        centerMetaDataEntity.setId("centerMetaDataEntityId");
        // 设置 center meta 的 key
        String metaKey = "metaKey";
        centerMetaDataEntity.setMetaKey(metaKey);
        // 设置 center meta 的 value
        String metaValue = "metaValue";
        centerMetaDataEntity.setMetaValue(metaValue);
        // 设置 center meta 的 center
        // 定义一个 center
        CenterEntity center = new CenterEntity();
        // 设置 center 的 name
        center.setName("centerName");
        // 设置 center 的 id
        String centerId = "centerId";
        center.setId(centerId);
        centerMetaDataEntity.setCenter(center);

        // 将 centerMetaDataEntity 添加到 centerMetaDataEntitiesInDB 中
        centerMetaDataEntitiesInDB.add(centerMetaDataEntity);
        // 设置 mock 方法的返回值
        when(jdbcTemplate.query(anyString(), any(Object[].class), any(RowMapper.class))).thenReturn(centerMetaDataEntitiesInDB);
        // 调用要测试的方法
        centersMetaDataDao.createMeta("test", "test", "test");

        // 验证 jdbcTemplate 的方法调用是否符合预期
        verify(jdbcTemplate, times(1)).query(Mockito.eq("select * from centers_metadata where  MetaKey=? AND CenterId=?"), Mockito.eq(new Object[]{"test", "test"}), any(RowMapper.class));
        // 验证进行了更新操作
        verify(jdbcTemplate, times(1)).update(Mockito.eq("UPDATE centers_metadata  SET  MetaValue=? WHERE MetaKey=? AND CenterId=?"),
                Mockito.eq("test"), Mockito.eq("test"), Mockito.eq("test"));
    }


    /**
     * 测试-创建 metadata 数据(插入数据)
     */
    @Test
    public void testCreateMeta_Insert() {
        // 定义 mock 方法的返回值
        // 定义从数据库中查询得到的 centerMetaDataEntities
        List<CenterMetaDataEntity> centerMetaDataEntitiesInDB = new ArrayList<>();
        // 设置 mock 方法的返回值
        when(jdbcTemplate.query(anyString(), any(Object[].class), any(RowMapper.class))).thenReturn(centerMetaDataEntitiesInDB);
        // 调用要测试的方法
        centersMetaDataDao.createMeta("test", "test", "test");

        // 验证 jdbcTemplate 的方法调用是否符合预期
        verify(jdbcTemplate, times(1)).query(Mockito.eq("select * from centers_metadata where  MetaKey=? AND CenterId=?"), Mockito.eq(new Object[]{"test", "test"}), any(RowMapper.class));
        // 验证进行了插入操作
        verify(jdbcTemplate, times(1)).update(Mockito.eq("INSERT INTO centers_metadata (MetaKey,MetaValue,CenterId)VALUES (?,?,?);"),
                Mockito.eq("test"), Mockito.eq("test"), Mockito.eq("test"));
    }
}
