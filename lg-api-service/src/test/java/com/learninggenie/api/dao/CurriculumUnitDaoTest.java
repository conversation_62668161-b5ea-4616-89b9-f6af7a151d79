package com.learninggenie.api.dao;


import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.learninggenie.common.data.dao.lesson2.impl.CurriculumUnitDaoImpl;
import com.learninggenie.common.data.entity.lesson2.curriculum.CurriculumUnitEntity;
import com.learninggenie.common.data.mapper.mybatisplus.lesson2.CurriculumUnitMapper;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.UUID;

import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
public class CurriculumUnitDaoTest {
    @InjectMocks
    private CurriculumUnitDaoImpl curriculumUnitDaoImpl;

    @Mock
    private ServiceImpl service;

    @Ignore
    @Test
    public void mockGetCurriculumUnitsByUserId() {
        final  String userId = UUID.randomUUID().toString();
        CurriculumUnitEntity curriculumUnitEntity = new CurriculumUnitEntity();
        curriculumUnitEntity.setCreateUserId(userId);
        curriculumUnitEntity.setCreateSource("CurriculumGenieUnit");
        ServiceImpl<CurriculumUnitMapper, CurriculumUnitEntity> service = Mockito.mock(ServiceImpl.class);
        LambdaQueryChainWrapper<CurriculumUnitEntity> wrapper = service.lambdaQuery();
        when(service.lambdaQuery()).thenReturn(wrapper);
        assertTrue(curriculumUnitDaoImpl.getUnitsByUserIdAndCreateSource(userId, anyString()).isEmpty());
    }
}
