package com.learninggenie.api.dao;

import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.toolkit.ChainWrappers;
import com.learninggenie.common.data.dao.export.impl.ExportSftpRecordDaoImpl;
import com.learninggenie.common.data.entity.export.ExportSftpRecord;
import com.learninggenie.common.data.enums.ExportSftpExportType;
import com.learninggenie.common.data.mapper.mybatisplus.export.ExportSftpRecordMapper;
import org.apache.ibatis.builder.MapperBuilderAssistant;
import org.junit.AfterClass;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.mockStatic;

/**
 * Create by hxl 2023/08/17.
 * ExportSftpRecordDaoImplTest 的测试类
 */
@RunWith(MockitoJUnitRunner.class)
public class ExportSftpRecordDaoImplTest {

    private static final String BASE_MAPPER = "baseMapper";

    private static MockedStatic<ChainWrappers> chainWrappersMockedStatic;

    @InjectMocks
    private ExportSftpRecordDaoImpl sftpRecordDao;

    @Mock
    private ExportSftpRecordMapper sftpRecordMapper;

    @BeforeClass
    public static void beforeClass() {
        chainWrappersMockedStatic = mockStatic(ChainWrappers.class);
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), ExportSftpRecord.class);
    }

    /**
     * 关闭 chainWrappersMockedStatic.
     */
    @AfterClass
    public static void afterClass() {
        if (chainWrappersMockedStatic != null) {
            chainWrappersMockedStatic.close();
        }
    }

    @Before
    public void setup() {
        // 调用待测试的方法
        ReflectionTestUtils.setField(sftpRecordDao, BASE_MAPPER, sftpRecordMapper);
        final LambdaQueryChainWrapper<ExportSftpRecord> lambdaUpdate = new LambdaQueryChainWrapper<>(sftpRecordMapper);
        lambdaUpdate.setEntity(new ExportSftpRecord());
        lambdaUpdate.setEntityClass(ExportSftpRecord.class);
        chainWrappersMockedStatic.when(() -> ChainWrappers.lambdaQueryChain(sftpRecordMapper)).thenReturn(lambdaUpdate);
    }

    @Test
    public void testPageByAgencyId() {
        // 创建Mock对象
        // 调用被测试方法
        IPage<ExportSftpRecord> result = sftpRecordDao.pageByAgencyId(Mockito.any(IPage.class), Mockito.anyString());

        // 验证行为
        // 断言结果
        assertNull(result);
    }

    @Test
    public void testPageByExportTypeAndAgencyId() {
        // 创建Mock对象
        List<ExportSftpExportType> exportType = new ArrayList<>();
        exportType.add(ExportSftpExportType.SCHEDULE);

        String agencyId = "agencyId";

        int pageNum = 1;
        int pageSize = 10;

        // 调用被测试方法
        IPage<ExportSftpRecord> result = sftpRecordDao.pageByExportTypeAndAgencyId(Page.of(pageNum, pageSize), exportType, agencyId);
        
    }

    @Test
    public void testGetPendingRecords() {
        // 调用被测试方法
        List<ExportSftpRecord> result = sftpRecordDao.getPendingRecords();

        // 验证行为

        // 断言结果
        assertNotNull(result);
    }

    @Test
    public void testGetBySettingIdAndExportDates() {

        List<Date> exportDateList = new ArrayList<>();
        exportDateList.add(new Date());
        // 调用被测试方法
        List<ExportSftpRecord> result = sftpRecordDao.getBySettingIdAndExportDates(Mockito.anyString(), exportDateList);

        // 断言结果
        assertNotNull(result);
    }

    @Test
    public void testGetProcessingRecords() {
        // 调用被测试方法
        List<ExportSftpRecord> result = sftpRecordDao.getProcessingRecords();


        // 断言结果
        assertNotNull(result);
    }

    @Test
    public void testResetExportStatus() {
        List<String> recordIds = new ArrayList<>();
        recordIds.add("1");
        recordIds.add("2");
        // 调用被测试方法
        sftpRecordDao.resetExportStatus(recordIds);
    }

    @Test
    public void testGetExportStatus() {
        // 调用被测试方法
        ExportSftpRecord result = sftpRecordDao.getExportStatus(Mockito.anyString());


        // 断言结果
        assertNull(result);
    }

    @Test
    public void testGetScheduleRecordsBySettingIdAndExportDates() {
        // 调用被测试方法
        List<ExportSftpRecord> result = sftpRecordDao.getScheduleRecordsBySettingIdAndExportDates(Mockito.anyString(), Mockito.anyList());

        // 断言结果
        assertNotNull(result);
    }

}
