package com.learninggenie.api.dao;

import com.learninggenie.common.data.dao.impl.InvitationsEnrollmentInvitationDaoImpl;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.jdbc.core.JdbcTemplate;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

/**
 * InvitationsEnrollmentInvitationDaoImpl 单元测试类.
 */
@RunWith(MockitoJUnitRunner.class)
public class InvitationsEnrollmentInvitationDaoImplTest {

    @InjectMocks
    private InvitationsEnrollmentInvitationDaoImpl invitationsEnrollmentInvitationDao;

    @Mock
    JdbcTemplate jdbcTemplate;

    /**
     * 测试根据用户 ID 更新邀请信息中家长用户名
     */
    @Test
    public void testUpdateInvitationUserName() {
        // 数据准备
        String userId = "userId001";
        String name = "name001";
        // 调用测试方法
        invitationsEnrollmentInvitationDao.updateInvitationUserName(userId, name);
        // 验证
        verify(jdbcTemplate, times(1)).update(anyString(), anyString(), anyString());
    }
}
