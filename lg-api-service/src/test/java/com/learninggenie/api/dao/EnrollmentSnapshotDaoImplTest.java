package com.learninggenie.api.dao;

import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.toolkit.ChainWrappers;
import com.learninggenie.common.data.dao.enrollment.impl.EnrollmentSnapshotDaoImpl;
import com.learninggenie.common.data.entity.enrollment.SnapshotEntity;
import com.learninggenie.common.data.mapper.mybatisplus.EnrollmentSnapshotMapper;
import org.apache.ibatis.builder.MapperBuilderAssistant;
import org.junit.AfterClass;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.times;

/**
 * Create by hxl 2023/08/17.
 * EnrollmentSnapshotDaoImpl 的测试类
 */
@RunWith(MockitoJUnitRunner.class)
public class EnrollmentSnapshotDaoImplTest {

    private static final String BASE_MAPPER = "baseMapper";

   public static MockedStatic<ChainWrappers> chainWrappersMockedStatic;

    @InjectMocks
    private EnrollmentSnapshotDaoImpl enrollmentSnapshotDao;

    @Mock
    private EnrollmentSnapshotMapper enrollmentSnapshotMapper;

    @BeforeClass
    public static void beforeClass() {
        chainWrappersMockedStatic = mockStatic(ChainWrappers.class);
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), SnapshotEntity.class);
    }

    /**
     * 关闭 chainWrappersMockedStatic.
     */
    @AfterClass
    public static void afterClass() {
        if (chainWrappersMockedStatic != null) {
            chainWrappersMockedStatic.close();
        }
    }

    @Before
    public void setup() {
        // 调用待测试的方法
        ReflectionTestUtils.setField(enrollmentSnapshotDao, BASE_MAPPER, enrollmentSnapshotMapper);
        final LambdaQueryChainWrapper<SnapshotEntity> lambdaQuery = new LambdaQueryChainWrapper<>(enrollmentSnapshotMapper);
        lambdaQuery.setEntity(new SnapshotEntity());
        lambdaQuery.setEntityClass(SnapshotEntity.class);
        chainWrappersMockedStatic.when(() -> ChainWrappers.lambdaQueryChain(enrollmentSnapshotMapper)).thenReturn(lambdaQuery);

        // 调用待测试的方法
        final LambdaUpdateChainWrapper<SnapshotEntity> lambdaUpdate = new LambdaUpdateChainWrapper<>(enrollmentSnapshotMapper);
        lambdaUpdate.setEntity(new SnapshotEntity());
        lambdaUpdate.setEntityClass(SnapshotEntity.class);
        chainWrappersMockedStatic.when(() -> ChainWrappers.lambdaUpdateChain(any())).thenReturn(lambdaUpdate);
    }

    @Test
    public void testListByEnrollmentIdsAndLikePeriod_EnrollmentIdsEmpty() {
        // Arrange
        List<String> enrollmentIds = new ArrayList<>();

        // Act
        List<SnapshotEntity> result = enrollmentSnapshotDao.listByEnrollmentIdsAndLikePeriod(enrollmentIds, "2021");

        // Assert
        assertEquals(0, result.size());
    }

    @Test
    public void testListByEnrollmentIdsAndLikePeriod_EnrollmentIdsNotEmpty() {
        // Arrange
        List<String> enrollmentIds = Arrays.asList("1", "2", "3");
        SnapshotEntity snapshot1 = new SnapshotEntity();
        // 使用 set 方法来替代构造函数
        snapshot1.setEnrollmentId("1");
        snapshot1.setDeleted(false);
        snapshot1.setActive(true);
        snapshot1.setSource("GENERATED");
        snapshot1.setPeriodAlias("2021");

        SnapshotEntity snapshot2 = new SnapshotEntity();
        snapshot2.setEnrollmentId("2");
        snapshot2.setDeleted(false);
        snapshot2.setActive(true);
        snapshot2.setSource("GENERATED");
        snapshot2.setPeriodAlias("2021");
        SnapshotEntity snapshot3 = new SnapshotEntity();
        snapshot3.setEnrollmentId("3");
        snapshot3.setDeleted(false);
        snapshot3.setActive(true);
        snapshot3.setSource("GENERATED");
        snapshot3.setPeriodAlias("2021");
        // Mock 测试数据
        List<SnapshotEntity> snapshotEntities = Arrays.asList(
                snapshot1,
                snapshot2,
                snapshot3
        );
        when(enrollmentSnapshotDao.list(any())).thenReturn(snapshotEntities);

        // Act
        List<SnapshotEntity> result = enrollmentSnapshotDao.listByEnrollmentIdsAndLikePeriod(enrollmentIds, "2021");

        // Assert
        assertEquals(3, result.size());
        assertEquals("1", result.get(0).getEnrollmentId());
        assertEquals("2", result.get(1).getEnrollmentId());
        assertEquals("3", result.get(2).getEnrollmentId());
    }

    @Test
    public void testListByEnrollmentIdsAndLikePeriod_EnrollmentIdsNotEmpty_MultipleChunks() {
        // Arrange
        List<String> enrollmentIds = Arrays.asList("1", "2", "3", "4", "5");
        // Mock 测试数据
        SnapshotEntity snapshot1 = new SnapshotEntity();
        snapshot1.setEnrollmentId("1");
        snapshot1.setDeleted(false);
        snapshot1.setActive(true);
        snapshot1.setSource("GENERATED");
        snapshot1.setPeriodAlias("2021");
        SnapshotEntity snapshot2 = new SnapshotEntity();
        snapshot2.setEnrollmentId("2");
        snapshot2.setDeleted(false);
        snapshot2.setActive(true);
        snapshot2.setSource("GENERATED");
        snapshot2.setPeriodAlias("2021");
        SnapshotEntity snapshot3 = new SnapshotEntity();
        snapshot3.setEnrollmentId("3");
        snapshot3.setDeleted(false);
        snapshot3.setActive(true);
        snapshot3.setSource("GENERATED");
        snapshot3.setPeriodAlias("2021");
        SnapshotEntity snapshot4 = new SnapshotEntity();
        snapshot4.setEnrollmentId("4");
        snapshot4.setDeleted(false);
        snapshot4.setActive(true);
        snapshot4.setSource("GENERATED");
        snapshot4.setPeriodAlias("2021");
        SnapshotEntity snapshot5 = new SnapshotEntity();
        snapshot5.setEnrollmentId("5");
        snapshot5.setDeleted(false);
        snapshot5.setActive(true);
        snapshot5.setSource("GENERATED");
        snapshot5.setPeriodAlias("2021");
        List<SnapshotEntity> snapshotEntities1 = Arrays.asList(
                snapshot1,
                snapshot2
        );
        List<SnapshotEntity> snapshotEntities2 = Arrays.asList(
                snapshot3,
                snapshot4
        );
        List<SnapshotEntity> snapshotEntities3 = Arrays.asList(
                snapshot5
        );

        when(enrollmentSnapshotDao.list(Mockito.any())).thenReturn(snapshotEntities1, snapshotEntities2, snapshotEntities3);

        // Act
        List<SnapshotEntity> result = enrollmentSnapshotDao.listByEnrollmentIdsAndLikePeriod(enrollmentIds, "2021");

        // Assert
        assertEquals(2, result.size());
        assertEquals("1", result.get(0).getEnrollmentId());
        assertEquals("2", result.get(1).getEnrollmentId());
    }

    @Test
    @DisplayName("listByEnrollmentIdsLikePeriod: Empty enrollmentIdList")
    public void testListByEnrollmentIdsLikePeriodEmptyEnrollmentIdList() {
        // Arrange
        List<String> enrollmentIdList = new ArrayList<>();
        String schoolYear = "2020";

        // Act
        List<SnapshotEntity> result = enrollmentSnapshotDao.listByEnrollmentIdsLikePeriod(enrollmentIdList, schoolYear);

        // Assert
        Assertions.assertEquals(0, result.size());
    }

    @Test
    @DisplayName("listByEnrollmentIdsLikePeriod: Non-empty enrollmentIdList")
    public void testListByEnrollmentIdsLikePeriodNonEmptyEnrollmentIdList() {
        // Arrange
        List<String> enrollmentIdList = new ArrayList<>();
        enrollmentIdList.add("enrollmentId1");
        enrollmentIdList.add("enrollmentId2");
        String schoolYear = "2020";

        // Act
        List<SnapshotEntity> result = enrollmentSnapshotDao.listByEnrollmentIdsLikePeriod(enrollmentIdList, schoolYear);

        // Assert
        Assertions.assertEquals(0, result.size());
    }

    @Test
    @DisplayName("listByGroupIdsAndLikePeriod: Empty groupIds")
    public void testListByGroupIdsAndLikePeriodEmptyGroupIds() {
        // Arrange
        List<String> groupIds = new ArrayList<>();
        String schoolYear = "2020";

        // Act
        List<SnapshotEntity> result = enrollmentSnapshotDao.listByGroupIdsAndLikePeriod(groupIds, schoolYear);

        // Assert
        Assertions.assertEquals(0, result.size());
    }

    @Test
    @DisplayName("listByGroupIdsAndLikePeriod: Non-empty groupIds")
    public void testListByGroupIdsAndLikePeriodNonEmptyGroupIds() {
        // Arrange
        List<String> groupIds = new ArrayList<>();
        groupIds.add("groupId1");
        groupIds.add("groupId2");
        String schoolYear = "2020";

        // Act
        List<SnapshotEntity> result = enrollmentSnapshotDao.listByGroupIdsAndLikePeriod(groupIds, schoolYear);

        // Assert
        Assertions.assertEquals(0, result.size());
    }


    /**
     * 测试 deletePendingSnapshots 方法
     */
    @Test
    public void testDeletePendingSnapshots() {
        // 准备测试数据
        String enrollmentId = "testEnrollmentId";
        String periodAlias = "testPeriodAlias";
        String frameworkId = "testFrameworkId";

        // 调用待测试的方法
        enrollmentSnapshotDao.deletePendingSnapshots(enrollmentId, periodAlias, frameworkId);

        // 验证方法调用
        verify(enrollmentSnapshotMapper, times(1)).update(any(), any());
    }
}
