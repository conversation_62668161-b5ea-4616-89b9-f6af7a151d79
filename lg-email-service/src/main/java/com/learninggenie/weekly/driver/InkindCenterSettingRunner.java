package com.learninggenie.weekly.driver;

import com.learninggenie.common.data.dao.InkindDao;
import com.learninggenie.common.data.dao.MetaDao;
import com.learninggenie.common.data.model.InkindCenterEntity;
import com.learninggenie.common.data.model.InkindItemModel;
import com.learninggenie.common.data.model.InkindReviewModel;
import com.learninggenie.common.data.model.InkindTypeEntity;
import com.learninggenie.common.utils.TimeUtil;
import com.learninggenie.weekly.config.SpringAppConfig;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;

import java.util.*;

public class InkindCenterSettingRunner {
    private static AnnotationConfigApplicationContext ctx;
    private static InkindDao inkindDao;
    private static MetaDao metaDao;

    public static void main(String[] args) {
        ctx = new AnnotationConfigApplicationContext(SpringAppConfig.class);
        inkindDao = ctx.getBean(InkindDao.class);
        metaDao = ctx.getBean(MetaDao.class);
        List<String> dates = new ArrayList<>();
        dates.add("2020-01-01");
        for (String date : dates) {
            String beforeDate = TimeUtil.format(TimeUtil.addYears(TimeUtil.parse(date, TimeUtil.format10), -1), TimeUtil.format10);
            List<InkindCenterEntity> targetCenterSettings = inkindDao.getAllCenterSettingsByDate(beforeDate);
            for (InkindCenterEntity targetCenterSetting : targetCenterSettings) {
                createCenterSetting(targetCenterSetting.getCenterId(), date, targetCenterSetting);
            }
        }
    }

    public static void createCenterSetting(String centerId, String yearDate, InkindCenterEntity targetCenterSettings) {
            String targetCenterId = targetCenterSettings.getCenterId();
            List<InkindTypeEntity> targetTypes = inkindDao.getInkindTypesBySettingIds(Arrays.asList(targetCenterSettings.getId()));
            List<InkindItemModel> targetItems = inkindDao.getInkindItemsBySettingIds(Arrays.asList(targetCenterSettings.getId()));
            List<InkindReviewModel> targetReviews = inkindDao.getInKindReviews(Arrays.asList(targetCenterId));

            // 创建
            InkindCenterEntity currentCenterSetting = new InkindCenterEntity();
            currentCenterSetting.setId(UUID.randomUUID().toString());
            currentCenterSetting.setCenterId(centerId);
            currentCenterSetting.setStateId(targetCenterSettings.getStateId());
            currentCenterSetting.setQuota(null);
            currentCenterSetting.setDate(yearDate);
            currentCenterSetting.setHourMoney(targetCenterSettings.getHourMoney());
            currentCenterSetting.setMileMoney(targetCenterSettings.getMileMoney());
            Date utcNow = TimeUtil.getUtcNow();
            currentCenterSetting.setCreateAtUtc(utcNow);
            currentCenterSetting.setUpdateAtUtc(utcNow);
            currentCenterSetting.setCustomized(targetCenterSettings.getCustomized());
            currentCenterSetting.setStateSync(true);
            currentCenterSetting.setDeleted(false);
            inkindDao.createInkindCenter(currentCenterSetting);

            List<InkindTypeEntity> addTypes = new ArrayList<>();
            Map<String, String> typeMap = new HashMap<>();
            for (InkindTypeEntity targetType : targetTypes) {
                InkindTypeEntity type = new InkindTypeEntity();
                type.setId(UUID.randomUUID().toString());
                type.setCenterSettingId(currentCenterSetting.getId());
                type.setCenterId(currentCenterSetting.getCenterId());
                type.setName(targetType.getName());
                type.setOrder(targetType.getOrder());
                type.setAbbreviation(targetType.getAbbreviation());
                type.setDeleted(targetType.getDeleted());
                type.setPlace(targetType.getPlace());
                type.setUnit(targetType.getUnit());
                type.setOpened(targetType.getOpened());
                Date createUtcNow = TimeUtil.getUtcNow();
                type.setCreateAtUtc(createUtcNow);
                type.setUpdateAtUtc(createUtcNow);
                addTypes.add(type);
                typeMap.put(targetType.getId(), type.getId());
            }
            if (!addTypes.isEmpty()) {
                inkindDao.batchCreateTypes(addTypes);
            }

            List<InkindItemModel> addItems = new ArrayList<>();
            for (InkindItemModel targetItem : targetItems) {
                if (null == typeMap.get(targetItem.getTypeId())) {
                    continue;
                }
                InkindItemModel item = new InkindItemModel();
                item.setId(UUID.randomUUID().toString());
                item.setTypeId(typeMap.get(targetItem.getTypeId()));
                item.setCenterSettingId(currentCenterSetting.getId());
                item.setName(targetItem.getName());
                item.setAbbreviation(targetItem.getAbbreviation());
                item.setRemark(targetItem.getRemark());
                item.setUnit(targetItem.getUnit());
                item.setMoney(targetItem.getMoney());
                item.setOrder(targetItem.getOrder());
                item.setDeleted(targetItem.getDeleted());
                Date createUtcNow = TimeUtil.getUtcNow();
                item.setCreateAtUtc(createUtcNow);
                item.setUpdateAtUtc(createUtcNow);
                addItems.add(item);
            }
            if (!addItems.isEmpty()) {
                inkindDao.batchCreateItems(addItems);
            }

            List<InkindReviewModel> addReviews = new ArrayList<>();
            for (InkindReviewModel targetReview : targetReviews) {
                InkindReviewModel review = new InkindReviewModel();
                review.setId(UUID.randomUUID().toString());
                review.setCenterId(currentCenterSetting.getCenterId());
                review.setUserId(null);
                review.setOpened(targetReview.getOpened());
                Date createUtcNow = TimeUtil.getUtcNow();
                review.setCreateAtUtc(createUtcNow);
                review.setUpdateAtUtc(createUtcNow);
                addReviews.add(review);
            }
            if (!addReviews.isEmpty()) {
                inkindDao.batchCreateReview(addReviews);
            }
    }
}
