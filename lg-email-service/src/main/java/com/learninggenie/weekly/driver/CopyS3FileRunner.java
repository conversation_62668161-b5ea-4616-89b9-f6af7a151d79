package com.learninggenie.weekly.driver;

import com.amazonaws.services.s3.AmazonS3;
import com.learninggenie.weekly.config.SpringAppConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by ha<PERSON> on 2017/9/21.
 */
public class CopyS3FileRunner {
    private static AnnotationConfigApplicationContext ctx;

    private static AmazonS3 s3Client;

    private static JdbcTemplate jdbcTemplate;

    private static Logger logger;

    private static int currentCount;
    
    private static List<String> failedList = new ArrayList<>();

    public static void main(String[] args) {
        ctx = new AnnotationConfigApplicationContext(SpringAppConfig.class);
        s3Client = ctx.getBean(AmazonS3.class);
        jdbcTemplate = ctx.getBean(JdbcTemplate.class);
        logger = LoggerFactory.getLogger(CopyS3FileRunner.class);
        run();
    }

    private static void run() {
        String fromDate = "2017-09-15";
        String toDate = "2017-09-16";

        List<String> filePathList = jdbcTemplate.queryForList("select RelativePath from medias_media where CreateAtUtc > ? and CreateAtUtc < ?", new String[]{fromDate, toDate}, String.class);
        for (String filePath : filePathList) {
            try {
                s3Client.copyObject("com.learning-genie.prod.us", filePath, "com.learning-genie.test.us", filePath);

            } catch (Exception e) {
                failedList.add(filePath);
                logger.error("Copy failed.", e);
            } finally {
                currentCount++;
                logger.info(filePath);
                logger.info(currentCount + "");
            }
        }

        logger.info("Failed: " + failedList.size());
        for (String failed : failedList) {
            logger.info(failed);
        }
    }
}
