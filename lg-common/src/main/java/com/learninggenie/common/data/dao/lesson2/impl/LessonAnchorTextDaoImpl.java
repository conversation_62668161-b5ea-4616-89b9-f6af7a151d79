package com.learninggenie.common.data.dao.lesson2.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.learninggenie.common.data.dao.lesson2.LessonAnchorTextDao;
import com.learninggenie.common.data.entity.lesson2.LessonAnchorTextEntity;
import com.learninggenie.common.data.mapper.mybatisplus.lesson2.LessonAnchorTextMapper;
import com.learninggenie.common.utils.StringUtil;
import org.springframework.stereotype.Repository;

@Repository
public class LessonAnchorTextDaoImpl extends ServiceImpl<LessonAnchorTextMapper, LessonAnchorTextEntity> implements LessonAnchorTextDao {

    /**
     * 根据 URL 查询未被删除的课程锚文本实体
     *
     * @param url 页面 URL
     * @return 匹配的 LessonAnchorTextEntity，如果不存在则返回 null
     */
    @Override
    public LessonAnchorTextEntity getByUrl(String url) {
        // 如果 url 为空或全是空白，直接返回 null
        if (StringUtil.isEmptyOrBlank(url)) {
            return null;
        }
        // 查询 pageUrl 等于 url 且未被删除（deleted=false）的记录，按 createAtUtc 降序排序，获取最新的一个
        return this.lambdaQuery()
                .eq(LessonAnchorTextEntity::getPageUrl, url)
                .eq(LessonAnchorTextEntity::getDeleted, false)
                .orderByDesc(LessonAnchorTextEntity::getCreateAtUtc)
                .list()
                .stream()
                .findFirst()
                .orElse(null);
    }
}