package com.learninggenie.common.data.dao.lesson2;

import com.baomidou.mybatisplus.extension.service.IService;
import com.learninggenie.common.data.entity.lesson2.LessonAnchorTextEntity;

public interface LessonAnchorTextDao extends IService<LessonAnchorTextEntity> {

    /**
     * 根据 URL 获取锚文本实体
     *
     * @param url 页面 URL
     * @return 锚文本实体，如果不存在则返回 null
     */
    LessonAnchorTextEntity getByUrl(String url);
}