package com.learninggenie.common.data.model;

import lombok.Data;
import java.util.Date;
import java.util.List;

@Data
public class ArticleContent {

    private String pageUrl;
    private String api;
    private Integer version;

    private Date date;
    private Double sentiment;
    private List<Image> images;
    private Date estimatedDate;
    private String icon;
    private String diffbotUri;
    private String siteName;
    private String type;
    private String title;
    private List<Breadcrumb> breadcrumb;
    private String humanLanguage;
    // private String html;
    private List<Category> categories;
    private String text;

    @Data
    public static class Image {
        private Integer naturalHeight;
        private Integer width;
        private String title;
        private String url;
        private Integer naturalWidth;
        private Boolean primary;
        private Integer height;
    }

    @Data
    public static class Breadcrumb {
        private String link;
        private String name;
    }

    @Data
    public static class Category {
        private Double score;
        private String name;
        private String id;
    }
}
