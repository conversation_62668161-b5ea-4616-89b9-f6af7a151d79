package com.learninggenie.common.data.model;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 文章内容
 */
@Data
public class ArticleContent {

    /**
     * 页面 URL
     */
    private String pageUrl;

    /**
     * API 名称
     */
    private String api;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 文章日期
     */
    private Date date;

    /**
     * 情感分析分数
     */
    private Double sentiment;

    /**
     * 图片列表
     */
    private List<Image> images;

    /**
     * 估算日期
     */
    private Date estimatedDate;

    /**
     * 网站图标
     */
    private String icon;

    /**
     * Diffbot 唯一标识
     */
    private String diffbotUri;

    /**
     * 网站名称
     */
    private String siteName;

    /**
     * 类型
     */
    private String type;

    /**
     * 标题
     */
    private String title;

    /**
     * 面包屑导航
     */
    private List<Breadcrumb> breadcrumb;

    /**
     * 语言（使用两字母 ISO 639-1 命名法）
     */
    private String humanLanguage;

    /**
     * 正文文本
     */
    private String text;

    @Data
    public static class Image {
        /**
         * 图片原始高度
         */
        private Integer naturalHeight;

        /**
         * 图片宽度
         */
        private Integer width;

        /**
         * 图片标题
         */
        private String title;

        /**
         * 图片链接
         */
        private String url;

        /**
         * 图片原始宽度
         */
        private Integer naturalWidth;

        /**
         * 是否为主图
         */
        private Boolean primary;

        /**
         * 图片高度
         */
        private Integer height;
    }

    @Data
    public static class Breadcrumb {
        /**
         * 链接
         */
        private String link;

        /**
         * 名称
         */
        private String name;
    }
}
