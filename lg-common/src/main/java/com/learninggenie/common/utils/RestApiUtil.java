package com.learninggenie.common.utils;

import com.learninggenie.common.data.model.ResultPojo;
import com.learninggenie.common.exception.CallInterfaceException;
import com.learninggenie.common.interfaces.UpdateTokenCallback;
import com.sun.jersey.api.client.Client;
import com.sun.jersey.api.client.ClientResponse;
import com.sun.jersey.api.client.WebResource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.*;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.client.RestTemplate;

import javax.ws.rs.core.MediaType;
import java.net.SocketTimeoutException;
import java.util.Map;

/**
 * Created by Zjj on 2017/4/1.
 */
public class RestApiUtil {

    private static final Logger logger = LoggerFactory.getLogger(RestApiUtil.class);

    public static ResultPojo post(String url, String requestBody, Map<String, String> headers, boolean timeoutTag) {
        return post(url, requestBody, headers, timeoutTag ? 300000 : null);
    }

    public static ResultPojo post(String url, String requestBody, Map<String, String> headers, Integer timeout) {
        ResultPojo resultPojo = new ResultPojo();
        ClientResponse response = null;
        try {
            Client client = Client.create();
            if (timeout != null) {
                client.setConnectTimeout(timeout);
                client.setReadTimeout(timeout);
            }
            WebResource resource = client.resource(url);
            WebResource.Builder builder = resource.getRequestBuilder();
            if (headers != null) {
                for (Map.Entry<String, String> entry : headers.entrySet()) {
                    builder.header(entry.getKey(), entry.getValue());
                }
            }
            response = builder.type(MediaType.APPLICATION_JSON_TYPE).post(ClientResponse.class, requestBody);
            int status = response.getStatus();
            String data = response.getEntity(String.class);
            resultPojo.setStatus(status);
            resultPojo.setData(data);
        } catch (Exception e) {
            resultPojo.setStatus(500);//服务器异常
            resultPojo.setErrorMsg(e.getMessage());
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return resultPojo;
    }

    public static ResultPojo get(String url, Map<String, String> headers, boolean timeoutTag) {
        // 默认 30 秒超时时间
        return get(url, headers, timeoutTag ? 30000 : null);
    }

    public static ResultPojo get(String url, Map<String, String> headers, Integer timeout) {
        ResultPojo resultPojo = new ResultPojo();
        ClientResponse response = null;
        try {
            Client client = Client.create();
            if (timeout != null) {
                client.setConnectTimeout(timeout);
                client.setReadTimeout(timeout);
            }
            WebResource resource = client.resource(url);
            WebResource.Builder builder = resource.getRequestBuilder();
            if (headers != null) {
                for (Map.Entry<String, String> entry : headers.entrySet()) {
                    builder.header(entry.getKey(), entry.getValue());
                }
            }
            response = builder.accept(MediaType.APPLICATION_JSON_TYPE).get(ClientResponse.class);
            int status = response.getStatus();
            String data = response.getEntity(String.class);
            resultPojo.setStatus(status);
            resultPojo.setData(data);
        } catch (Exception e) {
            resultPojo.setStatus(500);//服务器异常
            resultPojo.setErrorMsg(e.getMessage());
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return resultPojo;
    }
    public static ResultPojo delete(String url, Map<String, String> headers, boolean timeoutTag) {
        ResultPojo resultPojo = new ResultPojo();
        ClientResponse response = null;
        try {
            Client client = Client.create();
            if (timeoutTag) {
                client.setConnectTimeout(30000);
                client.setReadTimeout(30000);
            }
            WebResource resource = client.resource(url);
            WebResource.Builder builder = resource.getRequestBuilder();
            if (headers != null) {
                for (Map.Entry<String, String> entry : headers.entrySet()) {
                    builder.header(entry.getKey(), entry.getValue());
                }
            }
            response = builder.accept(MediaType.APPLICATION_JSON_TYPE).delete(ClientResponse.class);
            int status = response.getStatus();
            String data = response.getEntity(String.class);
            resultPojo.setStatus(status);
            resultPojo.setData(data);
        } catch (Exception e) {
            resultPojo.setStatus(500);//服务器异常
            resultPojo.setErrorMsg(e.getMessage());
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return resultPojo;
    }


    public static ResultPojo put(String url, String requestBody, Map<String, String> headers, boolean timeoutTag) {
        ResultPojo resultPojo = new ResultPojo();
        ClientResponse response = null;
        try {
            Client client = Client.create();
            if (timeoutTag) {
                client.setConnectTimeout(30000);
                client.setReadTimeout(30000);
            }
            WebResource resource = client.resource(url);
            WebResource.Builder builder = resource.getRequestBuilder();
            if (headers != null) {
                for (Map.Entry<String, String> entry : headers.entrySet()) {
                    builder.header(entry.getKey(), entry.getValue());
                }
            }
            response = builder.type(MediaType.APPLICATION_JSON_TYPE).put(ClientResponse.class, requestBody);
            int status = response.getStatus();
            String data = response.getEntity(String.class);
            resultPojo.setStatus(status);
            resultPojo.setData(data);
        } catch (Exception e) {
            resultPojo.setStatus(500);//服务器异常
            resultPojo.setErrorMsg(e.getMessage());
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return resultPojo;
    }

    /**
     * 发送 HTTP GET 请求
     * @param restTemplate 请求客户端
     * @param url 请求地址
     * @param headers 头部信息
     * @return 请求结果
     */
    public static ResultPojo get(RestTemplate restTemplate, String url, Map<String, String> headers, UpdateTokenCallback tokenCallback, org.springframework.http.MediaType mediaType) {
        return exchange(restTemplate, url, null, headers, HttpMethod.GET, 3, tokenCallback, true, mediaType);
    }

    /**
     * 发送 HTTP GET 请求
     * @param restTemplate 请求客户端
     * @param url 请求地址
     * @param headers 头部信息
     * @return 请求结果
     */
    public static ResultPojo get(RestTemplate restTemplate, String url, Map<String, String> headers, UpdateTokenCallback tokenCallback) {
        return exchange(restTemplate, url, null, headers, HttpMethod.GET, 3, tokenCallback, true, org.springframework.http.MediaType.APPLICATION_JSON);
    }

    /**
     * 发送 HTTP GET 请求
     *
     * @param restTemplate 请求客户端
     * @param url 请求地址
     * @param headers 头部信息
     * @param tokenCallback token更新回调
     * @param throwExp 是否抛出异常
     * @return 请求结果
     */
    public static ResultPojo get(RestTemplate restTemplate, String url, Map<String, String> headers, UpdateTokenCallback tokenCallback, boolean throwExp) {
        return exchange(restTemplate, url, null, headers, HttpMethod.GET, 3, tokenCallback, throwExp, org.springframework.http.MediaType.APPLICATION_JSON);
    }

    /**
     * 发送 HTTP POST 请求
     * @param restTemplate 请求客户端
     * @param url 请求地址
     * @param body 请求数据
     * @param headers 头部信息
     * @return 请求结果
     */
    public static ResultPojo post(RestTemplate restTemplate, String url, String body, Map<String, String> headers, UpdateTokenCallback tokenCallback) {
        return exchange(restTemplate, url, body, headers, HttpMethod.POST, 3, tokenCallback, true, org.springframework.http.MediaType.APPLICATION_JSON);
    }

    /**
     * 发送 HTTP POST 请求
     *
     * @param restTemplate 请求客户端
     * @param url 请求地址
     * @param body 请求数据
     * @param headers 头部信息
     * @param throwExp 是否抛出异常
     * @param tokenCallback token更新回调
     * @return 请求结果
     */
    public static ResultPojo post(RestTemplate restTemplate, String url, String body, Map<String, String> headers, UpdateTokenCallback tokenCallback, boolean throwExp) {
        return exchange(restTemplate, url, body, headers, HttpMethod.POST, 3, tokenCallback, throwExp, org.springframework.http.MediaType.APPLICATION_JSON);
    }

    /**
     * 发送 HTTP PUT 请求
     * @param restTemplate 请求客户端
     * @param url 请求地址
     * @param body 请求数据
     * @param headers 头部信息
     * @return 请求结果
     */
    public static ResultPojo put(RestTemplate restTemplate, String url, String body, Map<String, String> headers, UpdateTokenCallback tokenCallback) {
        return exchange(restTemplate, url, body, headers, HttpMethod.PUT, 3, tokenCallback, true, org.springframework.http.MediaType.APPLICATION_JSON);
    }

    /**
     * 发送 HTTP DELETE 请求
     * @param restTemplate 请求客户端
     * @param url 请求地址
     * @param headers 头部信息
     * @return 请求结果
     */
    public static ResultPojo delete(RestTemplate restTemplate, String url, Map<String, String> headers, UpdateTokenCallback tokenCallback) {
        return exchange(restTemplate, url, null, headers, HttpMethod.DELETE, 3, tokenCallback, true, org.springframework.http.MediaType.APPLICATION_JSON);
    }

    /**
     * 发送 HTTP DELETE 请求
     *
     * @param restTemplate 请求客户端
     * @param url 请求地址
     * @param headers 头部信息
     * @param tokenCallback token更新回调
     * @param throwExp 是否抛出异常
     * @return 请求结果
     */
    public static ResultPojo delete(RestTemplate restTemplate, String url, Map<String, String> headers, UpdateTokenCallback tokenCallback, boolean throwExp) {
        return exchange(restTemplate, url, null, headers, HttpMethod.DELETE, 3, tokenCallback, throwExp, org.springframework.http.MediaType.APPLICATION_JSON);
    }

    /**
     * 发送 HTTP PATCH 请求
     *
     * @param restTemplate 请求客户端
     * @param url 请求地址
     * @param body 请求体
     * @param headers 头部信息
     * @param tokenCallback token更新回调
     * @return 请求结果
     */
    public static ResultPojo patch(RestTemplate restTemplate, String url, String body, Map<String, String> headers, UpdateTokenCallback tokenCallback) {
        return exchange(restTemplate, url, body, headers, HttpMethod.PATCH, 3, tokenCallback, true, org.springframework.http.MediaType.APPLICATION_JSON);
    }


    /**
     * 发送 HTTP PATCH 请求
     *
     * @param restTemplate 请求客户端
     * @param url 请求地址
     * @param body 请求体
     * @param headers 头部信息
     * @param tokenCallback token更新回调
     * @param throwExp 是否抛出异常
     * @return 请求结果
     */
    public static ResultPojo patch(RestTemplate restTemplate, String url, String body, Map<String, String> headers, UpdateTokenCallback tokenCallback, boolean throwExp) {
        return exchange(restTemplate, url, body, headers, HttpMethod.PATCH, 3, tokenCallback, throwExp, org.springframework.http.MediaType.APPLICATION_JSON);
    }

    /**
     * 发送 HTTP 请求
     * @param restTemplate 请求客户端
     * @param url 请求地址
     * @param body 请求数据
     * @param headers 头部信息
     * @param method 请求方法
     * @param maxRetryCounts 最大尝试次数
     * @return 请求结果
     */
    public static ResultPojo exchange(RestTemplate restTemplate, String url, String body, Map<String, String> headers,
                                      HttpMethod method, int maxRetryCounts, UpdateTokenCallback tokenCallback,
                                      boolean throwExp, org.springframework.http.MediaType contentType) {
        return exchange(restTemplate, url, body, headers, method, maxRetryCounts, tokenCallback, throwExp, contentType, false);
    }

        /**
         * 发送 HTTP 请求
         * @param restTemplate 请求客户端
         * @param url 请求地址
         * @param body 请求数据
         * @param headers 头部信息
         * @param method 请求方法
         * @param maxRetryCounts 最大尝试次数
         * @param tokenCallback token更新回调
         * @param throwExp 是否抛出异常
         * @param contentType 请求类型
         * @param skipTimeout 是否跳过超时
         *
         * @return 请求结果
         */
    public static ResultPojo exchange(RestTemplate restTemplate, String url, String body, Map<String, String> headers,
                                      HttpMethod method, int maxRetryCounts, UpdateTokenCallback tokenCallback,
                                      boolean throwExp, org.springframework.http.MediaType contentType, boolean skipTimeout) {
        // 请求结果
        ResultPojo resultPojo = new ResultPojo();
        // 通用请求头部
        HttpHeaders httpHeaders = new HttpHeaders();
        if (contentType != null) {
            httpHeaders.setContentType(contentType);
        }
        httpHeaders.set("Accept-Charset", "UTF-8");
        // 设置额外的头部信息
        if (headers != null && !headers.isEmpty()) {
            for (String key : headers.keySet()) {
                httpHeaders.set(key, headers.get(key));
            }
        }
        // 尝试次数
        int retryCounts = 0;
        // token 失效的重试
        boolean tokenRetry = false;
        // 请求数据
        HttpEntity<String> requestData = new HttpEntity<>(body, httpHeaders);
        // 尝试请求接口
        while (retryCounts < maxRetryCounts) {
            ResponseEntity<String> responseEntity;
            try {
                logger.info("Call DRDP API: url={}，requestBody={}", url, requestData.getBody());
                responseEntity = restTemplate.exchange(url, method, requestData, String.class);
            } catch (ResourceAccessException e) {
                // IO 错误，直接重试
                retryCounts++;
                logger.error("exchange 发生错误 {}", e);
                // 如果跳过超时并且异常原因超时，则设置重试次数为最大值重试次数
                if (skipTimeout && e.getCause() instanceof SocketTimeoutException) {
                    // 连接超时
                    retryCounts = maxRetryCounts;
                    // 设置超时状态
                    resultPojo.setStatus(504);
                }
                continue;
            }
            // 响应状态
            HttpStatus status = responseEntity.getStatusCode();
            // 设置响应信息
            resultPojo.setStatus(status.value());
            resultPojo.setData(responseEntity.getBody());
            // 认证失败，重新获取 token
            if (status.value() == 401 && tokenCallback != null && !tokenRetry) {
                // 认证失败，不记录重试次数中
                tokenRetry = true;
                Map<String, String> tokenHeader = tokenCallback.updateHeaderToken();
                // 设置认证 Token
                for (String key : tokenHeader.keySet()) {
                    httpHeaders.set(key, tokenHeader.get(key));
                    requestData = new HttpEntity<>(body, httpHeaders);
                }
                continue;
            }
            // 如果调用成功，或者是正常错误，则直接返回
            if (status.is2xxSuccessful()) {
                return resultPojo;
            }
            // 调用接口报错，重新尝试
            retryCounts++;
        }
        if (200 != resultPojo.getStatus()) {
            logger.error("Call api failed! {} {} {} {}", resultPojo.getStatus(), resultPojo.getData(), url, body);
            // 设置异常内容
            resultPojo.setErrorMsg(resultPojo.getData());
            // 清空请求响应
            resultPojo.setData(null);
            if (throwExp) {
                throw new CallInterfaceException(resultPojo.getErrorMsg());
            }
        }
        return resultPojo;
    }

    /**
     * 发送 HTTP 请求
     *
     * @param restTemplate 请求客户端
     * @param url          请求地址
     * @param body         请求数据
     * @param headers      头部信息
     * @return 请求结果
     */
    public static ResultPojo exchange(RestTemplate restTemplate, String url, String body, Map<String, String> headers,
                                      HttpMethod method, org.springframework.http.MediaType contentType) {
        // 请求结果
        ResultPojo resultPojo = new ResultPojo();
        // 通用请求头部
        HttpHeaders httpHeaders = new HttpHeaders();
        if (contentType != null) {
            httpHeaders.setContentType(contentType);
        }
        httpHeaders.set("Accept-Charset", "UTF-8");
        // 设置额外的头部信息
        if (headers != null && !headers.isEmpty()) {
            for (String key : headers.keySet()) {
                httpHeaders.set(key, headers.get(key));
            }
        }
        // 请求数据
        HttpEntity<String> requestData = new HttpEntity<>(body, httpHeaders);
        // 尝试请求接口
        ResponseEntity<String> responseEntity;
        logger.info("Call API: url = {} requestBody = {}", url, requestData.getBody());
        responseEntity = restTemplate.exchange(url, method, requestData, String.class);
        // 响应状态
        HttpStatus status = responseEntity.getStatusCode();
        // 设置响应信息
        resultPojo.setStatus(status.value());
        resultPojo.setData(responseEntity.getBody());
        return resultPojo;
    }
}
