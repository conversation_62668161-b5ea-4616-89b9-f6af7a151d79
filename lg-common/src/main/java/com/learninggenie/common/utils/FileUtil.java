package com.learninggenie.common.utils;

import com.learninggenie.common.exception.LearningGenieRuntimeException;
import com.opencsv.CSVReader;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpHost;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.poi.util.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.color.ColorSpace;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

/**
 * Created by LiWanXing on 2016/8/1.
 */
public class FileUtil {

    /**
     * csv BOM 头
     */
    public static final String CSV_BOM_HEAD = "\uFEFF";

    private static final Logger log = LoggerFactory.getLogger(FileUtil.class);

    /**
     * 读取 csv 文件内容
     *
     * @param reader csv 文件流
     * @return csv 文件内容
     * @throws IOException 读取异常
     */
    public static List<String[]> parseCsvFile(InputStreamReader reader) throws IOException {
        try (CSVReader csvReader = new CSVReader(reader)) {
            List<String[]> csvStringsList = csvReader.readAll();
            // 去除 csv 文件第一行的 BOM 头
            if (csvStringsList.size() > 0) {
                String[] csvStrings = csvStringsList.get(0);
                if (csvStrings.length > 0 && csvStrings[0].startsWith(CSV_BOM_HEAD)) {
                    csvStrings[0] = csvStrings[0].replace(CSV_BOM_HEAD, "");
                }
            }
            return csvStringsList;
        }
    }

    /**
     * 创建文件目录
     * @param dir
     */
    public static File makeDir(String dir) {
        File dirFile = new File(dir);
        dirFile.mkdirs();
        dirFile.getParentFile().setReadable(true, false);
        dirFile.getParentFile().setWritable(true, false);
        dirFile.getParentFile().setExecutable(true, false);
        dirFile.setReadable(true, false);
        dirFile.setWritable(true, false);
        dirFile.setExecutable(true, false);
        return dirFile;
    }

    /**
     * 删除文件目录
     */
    public static void deleteDir(String dir) {
        try {
            File tempDir = new File(dir);
            if (tempDir.exists()) {
                FileUtils.deleteDirectory(new File(dir));
            }
        } catch (IOException e) {
            log.error("Delete dir failed." + dir, e);
        }
    }

    /**
     * 递归清空目录
     *
     * @param directory 要删除的目录
     */

    public static void clearDirectory(File directory) {
        if (directory.exists()) {
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isDirectory()) {
                    } else {
                        file.delete();
                    }
                }
            }
            directory.delete();
        }
    }

    /**
     * 删除文件
     */
    public static void deleteFile(String path) {
        File tempFile = new File(path);
        if (tempFile.exists() && tempFile.isFile()) {
            tempFile.delete();
        }
    }

    /**
     * 创建文件
     * @param path
     * @param data
     */
    public static void createStringFile(String path, String data) {
        try {
            File file = new File(path);
            FileUtils.writeStringToFile(file, data, "utf-8");
            file.setReadable(true, false);
            file.setWritable(true, false);
            file.setExecutable(true, false);
        } catch (IOException e) {
            throw new LearningGenieRuntimeException("Create file failed.", e);
        }
    }

    /**
     * 在临时目录下随机一个文件名
     * @param ext
     * @return
     */
    public static String randomTempFilePath(String ext) {
        String tempDir = System.getProperty("java.io.tmpdir");
        String path = tempDir + File.separator + UUID.randomUUID();
        if (ext != null)
            path += ext;
        return path;
    }

    /**
     * windows 测试时使用 (window获取java.io.tmpdir的临时路径时会在末尾多出一个'\')
     *                  (Linux和Mac则末尾不存在'\'
     */
    public static String randomTempFilePath2(String ext) {
        String tempDir = System.getProperty("java.io.tmpdir");
        String path = tempDir + UUID.randomUUID();
        if (ext != null)
            path += ext;
        return path;
    }
    public static String createTempFileName(String fileName) {
        String tempDir = System.getProperty("java.io.tmpdir");
        return tempDir + File.separator + fileName;
    }
    /**
     * 从网络Url中下载文件
     * 调用完记得关闭Stream!!!
     * @param urlStr
     * @param fileName
     * @param savePath
     * @throws IOException
     */
    public static FileInputStream  downLoadFromUrl(String urlStr, String fileName, String savePath) throws IOException{
        URL url = new URL(urlStr);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        //设置超时间为3秒
        conn.setConnectTimeout(20*1000);
        //防止屏蔽程序抓取而返回403错误
        conn.setRequestProperty("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");

        //得到输入流
        InputStream inputStream = conn.getInputStream();
        //获取自己数组
        byte[] getData = readInputStream(inputStream);

        //文件保存位置
        File saveDir = new File(savePath);
        if (!saveDir.exists()) {
            saveDir.mkdir();
            saveDir.setReadable(true);
        }
        File file = new File(saveDir+File.separator+fileName);
        FileOutputStream fos = new FileOutputStream(file);
        fos.write(getData);
        if (fos!=null) {
            fos.close();
        }
        if (inputStream!=null) {
            inputStream.close();
        }

        return new FileInputStream(file);
    }

    /**
     * 从网络Url中下载文件(返回文件)
     * 调用完记得关闭Stream!!!
     * @param urlStr
     * @param fileName
     * @param savePath
     * @throws IOException
     */
    public static File  downLoadFileFromUrl(String urlStr, String fileName, String savePath) throws IOException{
        // 对 URL 进行编码，防止 URL 中含有特殊字符
        urlStr = urlStr.replace(" ", "%20");
        URL url = new URL(urlStr);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        //设置超时间为3秒
        conn.setConnectTimeout(20*1000);
        //防止屏蔽程序抓取而返回403错误
        conn.setRequestProperty("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");

        //得到输入流
        InputStream inputStream = conn.getInputStream();
        //获取自己数组
        byte[] getData = readInputStream(inputStream);

        //文件保存位置
        File saveDir = new File(savePath);
        if (!saveDir.exists()) {
            saveDir.mkdir();
            saveDir.setReadable(true);
        }
        File file = new File(saveDir+File.separator+fileName);
        FileOutputStream fos = new FileOutputStream(file);
        fos.write(getData);
        if (fos!=null) {
            fos.close();
        }
        if (inputStream!=null) {
            inputStream.close();
        }

        return file;
    }

    /**
     * 从网络Url中获取文件 bytes
     * @param urlStr
     * @throws IOException
     */
    public static byte[] getBytesFromUrl(String urlStr) throws IOException {
        URL url = new URL(urlStr);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        // 设置超时间为30秒
        conn.setConnectTimeout(30*1000);
        // 防止屏蔽程序抓取而返回403错误
        conn.setRequestProperty("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");
        // 得到输入流
        InputStream inputStream = conn.getInputStream();
        // 获取自己数组
        byte[] stream = readInputStream(inputStream);
        return stream;
    }

    /**
     * 从输入流中获取字节数组
     * @param inputStream
     * @return
     * @throws IOException
     */
    public static  byte[] readInputStream(InputStream inputStream) throws IOException {
        byte[] buffer = new byte[1024];
        int len = 0;
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        while ((len = inputStream.read(buffer)) != -1) {
            bos.write(buffer, 0, len);
        }
        bos.close();
        return bos.toByteArray();
    }

    public static String randomFileName(){
        String date = TimeUtil.format(TimeUtil.getUtcNow(), TimeUtil.format14);
        String ranNum = RandomStringUtils.randomNumeric(5).replaceAll("0", "9");
        return date + ranNum;
    }


    /**
     * 比较两个以TB,GB,MB,KB为单位的字符串大小
     * @param size1
     * @param size2
     * @return
     */
    public static int compareSize(String size1, String size2) {
        if (StringUtil.isEmptyOrBlank(size1) && StringUtil.isEmptyOrBlank(size2)) {
            return 0;
        }
        if (StringUtil.isEmptyOrBlank(size1)) {
            return -1;
        }
        if (StringUtil.isEmptyOrBlank(size2)) {
            return 1;
        }
        if (size1.equalsIgnoreCase(size2)) {
            return 0;
        }
        BigDecimal size1Number = new BigDecimal(size1.substring(0, size1.length() - 2));
        String size1Unit = size1.substring(size1.length() - 2);

        BigDecimal size2Number = new BigDecimal(size2.substring(0, size2.length() - 2));
        String size2Unit = size2.substring(size2.length() - 2);

        if (size1Unit.equalsIgnoreCase(size2Unit)) {
            return size1Number.compareTo(size2Number);
        }else {
            if (size1Unit.equalsIgnoreCase("TB")) {
                return 1;
            }
            if (size2Unit.equalsIgnoreCase("TB")) {
                return -1;
            }
            if (size1Unit.equalsIgnoreCase("GB")) {
                return 1;
            }
            if (size2Unit.equalsIgnoreCase("GB")) {
                return -1;
            }
            if (size1Unit.equalsIgnoreCase("MB")) {
                return 1;
            }
            if (size2Unit.equalsIgnoreCase("MB")) {
                return -1;
            }
            return size1.compareToIgnoreCase(size2);
        }
    }


    /**
     * 文件字节单位转换
     * @param fileSize
     * @return
     */
    public static String formetFileSize(long fileSize){
        DecimalFormat df = new DecimalFormat("#.0");
        String fileSizeString = "";
        if (fileSize < 1024) {
            fileSizeString = df.format((double) fileSize) + "B";
        } else if (fileSize < 1048576) {
            fileSizeString = df.format((double) fileSize / 1024) + "K";
        } else if (fileSize < 1073741824) {
            fileSizeString = df.format((double) fileSize / 1048576) + "M";
        }else {
            fileSizeString = df.format((double) fileSize / 1073741824) + "G";
        }
        return fileSizeString;
    }

    public static File createTempFile() throws IOException {
        return createTempFile(".tmp");
    }

    public static File createTempFile(String suffix) throws IOException {
        try {
            return Files.createTempFile(UUID.randomUUID().toString(), suffix).toFile();
        } catch (IOException e) {
            log.error("local temp file cannot be created", e);
            throw e;
        }
    }

    public static List<String> getMediaURLs(Map<String, String> mediaMap, String mediaIds) {
        if (StringUtil.isEmptyOrBlank(mediaIds)) {
            return new ArrayList<>();
        }
        return Stream.of(mediaIds.split(StringUtil.COMMA))
                .map(String::toUpperCase)
                .map(mediaMap::get)
                .filter(url -> !StringUtil.isEmptyOrBlank(url))
                .collect(Collectors.toList());
    }

    /**
     * 根据文件名（包含后缀），获取文件图标 URL
     * @param fileName 文件名
     * @return 图标 URL
     */
    public static String getFileIcon(String fileName, String server) {
        String extension = FilenameUtils.getExtension(fileName);
        if (StringUtils.equalsIgnoreCase(extension, "pdf")) {
            return server + "/images/pdf.png";
        }
        if (StringUtils.equalsIgnoreCase(extension, "ppt") || StringUtils.equalsIgnoreCase(extension, "pptx")) {
            return server + "/images/pptx.png";
        }
        if (StringUtils.equalsIgnoreCase(extension, "doc") || StringUtils.equalsIgnoreCase(extension, "docx")) {
            return server + "/images/doc.png";
        }
        if (StringUtils.equalsIgnoreCase(extension, "xls") || StringUtils.equalsIgnoreCase(extension, "xlsx")) {
            return server + "/images/xls.png";
        }
        return server + "/images/default.png";
    }

    /**
     * 压缩图片文件。
     * 创建一个临时文件，将指定图片按指定质量进行压缩后，保存到这个临时文件中。
     *
     * @param imageFile 待压缩的图片文件。
     * @param imageFormat 图片格式，如"jpg"、"png"等。
     * @param quality 压缩质量，取值范围为0.0到1.0，其中0.0为最差质量（最大压缩比），1.0为最好质量（最小压缩比）。
     * @return 压缩后的图片文件，如果压缩失败则返回null。
     */
    public static File compressImage(File imageFile, float quality, String imageFormat) {
        try {
            // 创建一个临时文件，用于存放压缩后的图片
            File compressedImageFile = FileUtil.createTempFile(UUID.randomUUID() + "." + imageFormat);
            // 读取原始图片
            BufferedImage image = ImageIO.read(imageFile);
            // 根据指定质量缩放图片
            java.awt.Image compressedImage = image.getScaledInstance((int) (image.getWidth() * quality), (int) (image.getHeight() * quality), java.awt.Image.SCALE_SMOOTH);
            // 创建一个与缩放后的图片大小相匹配的 BufferedImage 对象
            BufferedImage resultImage = new BufferedImage((int) (image.getWidth() * quality), (int) (image.getHeight() * quality), image.getType());
            // 使用 Graphics2D 绘制缩放后的图片
            Graphics2D graphics2D = resultImage.createGraphics();
            graphics2D.drawImage(compressedImage, 0, 0, null);
            graphics2D.dispose();
            // 将绘制后的图片写入到临时文件中
            ImageIO.write(resultImage, imageFormat, compressedImageFile);
            return compressedImageFile;
        } catch (IOException e) {
            // 图片压缩失败，打印异常信息并返回 null
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 重绘图片，移除 EXIF 信息，并处理 CMYK 转 RGB 问题。
     *
     * @param sourceFile 原始图片文件
     * @param fileExtension 图片文件扩展名（如 "jpg", "png" 等）
     * @return 重绘后的图片文件
     * @throws IOException 如果读取或写入图片文件时发生错误
     */
    public static File redrawImage(File sourceFile, String fileExtension) throws IOException {
        // 读取原始图片
        BufferedImage originalImage = ImageIO.read(sourceFile);

        // 处理颜色空间问题（CMYK转RGB） - 仅对JPEG格式
        BufferedImage processedImage = originalImage;
        if (("jpg".equalsIgnoreCase(fileExtension) || "jpeg".equalsIgnoreCase(fileExtension)) &&
                originalImage.getColorModel().getColorSpace().getType() == ColorSpace.TYPE_CMYK) {
            processedImage = convertCMYKtoRGB(originalImage);
        }

        // 创建新图片
        int imageTypeCode = (processedImage.getTransparency() == Transparency.OPAQUE)
                ? BufferedImage.TYPE_INT_RGB
                : BufferedImage.TYPE_INT_ARGB;

        BufferedImage newImage = new BufferedImage(
                processedImage.getWidth(),
                processedImage.getHeight(),
                imageTypeCode
        );

        // 重绘图片（移除EXIF）
        Graphics2D g = newImage.createGraphics();
        g.drawImage(processedImage, 0, 0, null);
        g.dispose();

        // 创建临时文件
        File outputFile = File.createTempFile("redrawn_", "." + fileExtension);

        // 保存图片（保留原始质量）
        ImageIO.write(newImage, fileExtension, outputFile);

        return outputFile;
    }

    /**
     * 转换CMYK图片到RGB
     */
    private static BufferedImage convertCMYKtoRGB(BufferedImage cmykImage) {
        BufferedImage rgbImage = new BufferedImage(
                cmykImage.getWidth(),
                cmykImage.getHeight(),
                BufferedImage.TYPE_INT_RGB
        );

        Graphics2D g = rgbImage.createGraphics();
        g.drawImage(cmykImage, 0, 0, null);
        g.dispose();

        return rgbImage;
    }

    public static void createImageByHtml(String cohortReadinessReportHtmlPath, String cohortReadinessReportImagePath) {
        try {
            // googleslider/bin/wkhtmltoimage.exe
            String cmd = "googleslider/bin/wkhtmltoimage.exe ---crop-h 2048 --crop-w 2048 --crop-x 0 --crop-y 0 --width 1980 --format png " + cohortReadinessReportHtmlPath + " " + cohortReadinessReportImagePath;
            Process process = Runtime.getRuntime().exec(cmd);
            process.waitFor();
        } catch (Exception e) {
            log.error("Create image by html failed.", e);
        }
    }

    /**
     * 移动路径
     *
     * @param newFileName     文件名
     * @param oldFileName 生成文档
     * @param more         文件后缀
     * @return {@link Path}
     * @throws IOException IOException
     */
    public static Path movePath(String newFileName, File oldFileName, String more) throws IOException {
        // 创建新目录
        File newDir = FileUtil.makeDir(oldFileName.getParent() + File.separator + UUID.randomUUID());
        // 定义新路径，临时目录 + uuid 作为子目录，再拼接文件名
        Path movePath = Paths.get(newDir.getPath(), newFileName + more);
        // 生成新文件
        return Files.move(oldFileName.toPath(),
                Files.createFile(movePath),
                StandardCopyOption.REPLACE_EXISTING);
    }

    /**
     * 获取 image 的 byte 数组
     *
     * @param image 图片
     * @param format 传递 JPG 或者 PNG
     * @return {@link byte[]} 图片对应的字节数组
     */
    public static byte[] getBufferByteArray(BufferedImage image, String format) {
        return getBufferByteArray(image, format, 5.20 / 3);
    }

    /**
     * 获取 image 的 byte 数组
     *
     * @param image 图片
     * @param format 传递 JPG 或者 PNG
     * @return {@link byte[]} 图片对应的字节数组
     */
    public static byte[] getBufferByteArray(BufferedImage image, String format, double targetAspectRatio) {
        // 如果图片为空，直接返回
        if (null == image) return null;
        // 获取图片的格式名称
        String formatName = (!StringUtil.isEmptyOrBlank(format) && format.charAt(0) == '.') ? format.substring(1) : format;
        // 如果不存在格式，则默认是 png
        if (!StringUtil.isEmptyOrBlank(format)) formatName = "png";
        BufferedImage newImage = image;
        // 创建字节数组读取流
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        try {
            // 计算原始图片的宽高比
            double aspectRatio = (double) image.getWidth() / image.getHeight();

            // 如果原始图片的宽高比不接近目标宽高比
            if (Math.abs(aspectRatio - targetAspectRatio) > 0.01) {
                // 计算裁剪后的图片的宽度和高度
                int newWidth = 0;
                int newHeight = 0;
                if (aspectRatio > targetAspectRatio) {
                    newHeight = image.getHeight();
                    newWidth = (int) (newHeight * targetAspectRatio);
                } else {
                    newWidth = image.getWidth();
                    newHeight = (int) (newWidth / targetAspectRatio);
                }

                // 计算裁剪区域的位置，使得裁剪后的图片在水平和垂直方向上都能居中
                int x = (image.getWidth() - newWidth) / 2;
                int y = (image.getHeight() - newHeight) / 2;
                // 每次获取图片只获取其中的一部分
                newImage = image.getSubimage(x, y, newWidth, newHeight);
            }
            // 读取流并返回
            ImageIO.write(newImage, formatName, os);
            return os.toByteArray();
        } catch (Exception e) {
            return null;
        } finally {
            IOUtils.closeQuietly(os);
        }
    }

    /**
     * 从url下载文件，没有错误
     * 调用完记得关闭Stream!!!
     * @param urlStr   url str
     * @param fileName 文件名
     * @param savePath 保存路径
     * @return {@link File }
     * @throws IOException IOException
     */
    public static File downLoadFileFromUrlWithOutError(String urlStr, String fileName,
            String savePath) throws IOException {
        // 对 URL 进行编码，防止 URL 中含有特殊字符
        urlStr = urlStr.replace(" ", "%20");
        // 处理文件名，只保留字母、数字、. 和 -
        fileName = StringUtil.filterNoValidatedChar(fileName);

        // 创建 HttpClient 实例
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            // 构造 GET 请求
            HttpGet httpGet = new HttpGet(urlStr);
            httpGet.setHeader("User-Agent", "Mozilla/4.76");

            // 执行请求并获取响应
            try (CloseableHttpResponse response = httpClient.execute(httpGet)) {
                // 获取响应实体
                HttpEntity entity = response.getEntity();
                if (entity != null) {
                    // 确保保存目录存在
                    File saveDir = new File(savePath);
                    if (!saveDir.exists()) {
                        boolean mkdirred = saveDir.mkdir();
                        if (mkdirred) {
                            boolean readable = saveDir.setReadable(true);
                            if (!readable) {
                                log.error("Set readable failed.");
                            }
                        }
                    }

                    // 保存文件到指定路径
                    File file = new File(saveDir + File.separator + fileName);
                    try (InputStream inputStream = entity.getContent()) {
                        Files.copy(inputStream, file.toPath(), StandardCopyOption.REPLACE_EXISTING);
                    }
                    // 解压 ZIP 文件
                    if (fileName.toLowerCase().endsWith(".zip")) {
                        unzipToNamedFolder(file, saveDir);

                        // 删除 ZIP 文件
                        if (!file.delete()) {
                            log.warn("Warning: failed to delete zip file {} ", file.getAbsolutePath());
                        }
                    }
                    return file;
                } else {
                    throw new IOException("Failed to download file. Response entity is null.");
                }
            }
        }
    }

    /**
     * 对 zip 包进行解压
     * @param zipFile 文件
     * @param parentDir 目录
     * @throws IOException 异常
     */
    public static void unzipToNamedFolder(File zipFile, File parentDir) throws IOException {
        // 取 zip 包的名字（不含扩展名）
        String baseName = zipFile.getName();
        if (baseName.toLowerCase().endsWith(".zip")) {
            baseName = baseName.substring(0, baseName.length() - 4);
        }

        // 构建解压路径
        File destDir = new File(parentDir, baseName);
        if (!destDir.exists()) {
            destDir.mkdirs();
        }

        byte[] buffer = new byte[8192]; // 推荐 8KB 缓冲
        try (ZipInputStream zis = new ZipInputStream(new FileInputStream(zipFile))) {
            ZipEntry zipEntry;
            while ((zipEntry = zis.getNextEntry()) != null) {
                File newFile = new File(destDir, zipEntry.getName());

                // 防止 Zip Slip 攻击
                if (!newFile.getCanonicalPath().startsWith(destDir.getCanonicalPath())) {
                    throw new IOException("Bad zip entry: " + zipEntry.getName());
                }
    
                if (zipEntry.isDirectory() || zipEntry.getName().endsWith("/") || zipEntry.getName().endsWith("\\")) {
                    newFile.mkdirs();
                } else {
                    File parent = newFile.getParentFile();
                    if (!parent.exists()) {
                        parent.mkdirs();
                    }
                    try (FileOutputStream fos = new FileOutputStream(newFile)) {
                        int len;
                        while ((len = zis.read(buffer)) > 0) {
                            fos.write(buffer, 0, len);
                        }
                    }
                }
                zis.closeEntry();
            }
        }
    }


    /**
     * 从url下载文件，没有错误
     * 调用完记得关闭Stream!!!
     * @param urlStr   url str
     * @param fileName 文件名
     * @param savePath 保存路径
     * @return {@link File }
     * @throws IOException IOException
     */
    public static File downLoadFileFromUrlThrowError(String urlStr, String fileName,
                                                       String savePath) throws IOException {
        // 对 URL 进行编码，防止 URL 中含有特殊字符
        urlStr = urlStr.replace(" ", "%20");
        // 处理文件名，只保留字母、数字、. 和 -
        fileName = StringUtil.filterNoValidatedChar(fileName);

        // 创建 HttpClient 实例
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            // 构造 GET 请求
            HttpGet httpGet = new HttpGet(urlStr);
            httpGet.setHeader("User-Agent", "Mozilla/4.76");

            // 执行请求并获取响应
            try (CloseableHttpResponse response = httpClient.execute(httpGet)) {

                // 获取状态码
                int statusCode = response.getStatusLine().getStatusCode();
                // 处理 401 未授权 或 403 禁止访问
                if (statusCode == 401) {
                    throw new IOException("Unauthorized access (401). Please check your authentication credentials.");
                } else if (statusCode == 403) {
                    throw new IOException("Forbidden access (403). You don't have permission to access this resource.");
                } else if (statusCode >= 400) {
                    throw new IOException("HTTP error: " + statusCode + " - " + response.getStatusLine().getReasonPhrase());
                }
                // 获取响应实体
                HttpEntity entity = response.getEntity();
                if (entity != null) {
                    // 确保保存目录存在
                    File saveDir = new File(savePath);
                    if (!saveDir.exists()) {
                        boolean mkdirred = saveDir.mkdir();
                        if (mkdirred) {
                            boolean readable = saveDir.setReadable(true);
                            if (!readable) {
                                log.error("Set readable failed.");
                            }
                        }
                    }

                    // 保存文件到指定路径
                    File file = new File(saveDir + File.separator + fileName);
                    try (InputStream inputStream = entity.getContent()) {
                        Files.copy(inputStream, file.toPath(), StandardCopyOption.REPLACE_EXISTING);
                    }
                    return file;
                } else {
                    throw new IOException("Failed to download file. Response entity is null.");
                }
            }
        }
    }

}
