package com.learninggenie.common.utils;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import com.learninggenie.common.data.model.ArticleContent;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;
import java.lang.reflect.Type;
import java.util.*;

public class JsonUtil {

    // Object Mapper 序列化时不包含空值
    private final static ObjectMapper objectMapperNonNull = new ObjectMapper();

    private static Gson gson = new Gson();
    private static Gson gsonWithDefaultDateFormat = new GsonBuilder().setDateFormat(TimeUtil.format1).create();
    private static Gson gsonSerializeNulls = new GsonBuilder().serializeNulls().create();

    /**
     * 使用 MMM dd, yyyy h:mm:ss a 格式的 Gson 实例
     */
    private static Gson gsonWithAmPmDateFormat = new GsonBuilder().setDateFormat(TimeUtil.format30).create();


    static {
        // 忽略未知属性
        objectMapperNonNull.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        // 序列化时不包含空值
        objectMapperNonNull.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        // 序列化后以下划线分割
        objectMapperNonNull.setPropertyNamingStrategy(PropertyNamingStrategy.SNAKE_CASE);
    }

    public static <T> T fromJson(String json, Class<T> classOfT) {
        return gson.fromJson(json, classOfT);
    }

    public static <T> T fromJson(String json, Type type) {
        return gson.fromJson(json, type);
    }

    /**
     * Java编译过程 会将泛型对象的类型擦除 如果需要使用（例如Lambda操作）转换后的集合 请使用 stringToArray方法
     */
    public static <T> List<T> fromJsonArray(String json) {
        Type type = new TypeToken<List<T>>() {
        }.getType();
        return gson.fromJson(json, type);
    }

    public static <T> List<T> fromJsonArray(String json, Type type) {
        return gson.fromJson(json, type);
    }

    public static String toJson(Object object) {
        return gson.toJson(object);
    }

    public static String toJsonTree(Object object) {
        return gson.toJsonTree(object).toString();
    }

    public static String toSerializeNullsJson(Object object) {
        return gsonSerializeNulls.toJson(object);
    }
    public static <T> List<T> stringToArray(String json, Class<T[]> clazz) {
        T[] arr = gson.fromJson(json, clazz);
        if ( arr == null){
            return new ArrayList<>();
        }
        return Arrays.asList(arr);
    }

    public static String toJsonWithDefaultDateFormat(Object object) {
        return gsonWithDefaultDateFormat.toJson(object);
    }

    public static <T> T fromJsonWithDefaultDateFormat(String json, Class<T> classOfT) {
        return gsonWithDefaultDateFormat.fromJson(json, classOfT);
    }

    /**
     * 将对象转换为 JSON 字符串，使用 AM/PM 格式的日期格式化
     *
     * @param object 需要转换的对象
     * @return 转换后的 JSON 字符串
     */
    public static String toJsonWithAmPmDateFormat(Object object) {
        return gsonWithAmPmDateFormat.toJson(object);
    }

    /**
     * 将 JSON 字符串转换为指定类型的对象，使用 AM/PM 格式的日期格式化
     *
     * @param json 需要转换的 JSON 字符串
     * @param classOfT 目标类型的 Class 对象
     * @return 转换后的对象
     */
    public static <T> T fromJsonWithAmPmDateFormat(String json, Class<T> classOfT) {
        return gsonWithAmPmDateFormat.fromJson(json, classOfT);
    }

    public static Map<String, Object> jsonToMap(JSONObject json) throws JSONException {
        Map<String, Object> retMap = new HashMap<String, Object>();

        if(json != JSONObject.NULL) {
            retMap = toMap(json);
        }
        return retMap;
    }

    public static Map<String, Object> toMap(JSONObject object) throws JSONException {
        Map<String, Object> map = new HashMap<String, Object>();

        Iterator<String> keysItr = object.keys();
        while(keysItr.hasNext()) {
            String key = keysItr.next();
            Object value = object.get(key);

            if(value instanceof JSONArray) {
                value = toList((JSONArray) value);
            }

            else if(value instanceof JSONObject) {
                value = toMap((JSONObject) value);
            }
            map.put(key, value);
        }
        return map;
    }

    public static List<Object> toList(JSONArray array) throws JSONException {
        List<Object> list = new ArrayList<Object>();
        for(int i = 0; i < array.length(); i++) {
            Object value = array.get(i);
            if(value instanceof JSONArray) {
                value = toList((JSONArray) value);
            }

            else if(value instanceof JSONObject) {
                value = toMap((JSONObject) value);
            }
            list.add(value);
        }
        return list;
    }

    /**
     * Object 转换为 Json String，忽略空值
     * @param object 对象
     * @return Json String
     */
    public static String writeValueAsStringNonNull(Object object) {
        try {
            return objectMapperNonNull.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * Json String 转换为 Object
     */
    public static <T> T readValue(String content, Class<T> valueType) {
        try {
            return objectMapperNonNull.readValue(content, valueType);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }


    /**
     * 将 Json 字符串 content 反序列化为指定类型的对象（支持泛型类型）。
     *
     * @param content      Json 字符串内容
     * @param valueTypeRef 类型引用（用于支持泛型，如 List、Map 等复杂类型）
     * @param <T>          返回对象的类型
     * @return 反序列化后的对象
     * @throws RuntimeException 如果解析失败则抛出运行时异常
     */
    public static <T> T readValue(String content, TypeReference<T> valueTypeRef) {
        try {
            return objectMapperNonNull.readValue(content, valueTypeRef);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
