package com.learninggenie.common.utils;

import java.util.Base64;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 2017/3/31.
 */
public class VerifyUtil {

    public static boolean VerifyPassword(String tempPw, String userPw, String commonPw) {
        if ((StringUtil.isEmptyOrBlank(tempPw) || !tempPw.equals(userPw)) && !userPw.equals(commonPw))
            return false;
        return true;
    }

    public static boolean VerifyPassword(String tempPw, String userPw) {
        if ((StringUtil.isEmptyOrBlank(tempPw) || !tempPw.equals(userPw)))
            return false;
        return true;
    }

    public static boolean verifyGeneralPassword(String userPw, List<String> generalPasswords) {
        if (StringUtil.isEmptyOrBlank(userPw) || generalPasswords.isEmpty()) {
            return false;
        }
        String basePassword = Base64.getEncoder().encodeToString(userPw.getBytes());
        for (String generalPassword : generalPasswords) {
            if (!StringUtil.isEmptyOrBlank(generalPassword) && generalPassword.equals(basePassword)) {
                return true;
            }
        }
        return false;
    }
}
