package com.learninggenie.common.score;

import com.learninggenie.common.data.dao.DomainDao;
import com.learninggenie.common.data.dao.NoteDao;
import com.learninggenie.common.data.dao.ScoreDao;
import com.learninggenie.common.data.enums.NoteType;
import com.learninggenie.common.data.model.DomainEntity;
import com.learninggenie.common.score.model.RatingStatisticModel;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;


@RunWith(MockitoJUnitRunner.class)
public class RatingServiceImplTest {

    @InjectMocks
    private RatingServiceImpl ratingService;

    @Mock
    private ScoreDao scoreDao;

    @Mock
    private NoteDao noteDao;

    @Mock
    private DomainDao domainDao;

    @Test
    public void testIsCompleted() {
        String childId = "child1";
        String from = "2022-01-01";
        String to = "2022-12-31";
        String frameworkId = "framework1";
        Map<String, Set<String>> frameworkDomainMap = new HashMap<>();

        when(scoreDao.get(childId)).thenReturn(new ArrayList<>());
        List<DomainEntity> domainEntities = new ArrayList<>();
        DomainEntity domainEntity = new DomainEntity();
        domainEntity.setId("domain1");

        domainEntities.add(domainEntity);
        DomainEntity domainEntity2 = new DomainEntity();
        domainEntity2.setId("domain2");
        domainEntity2.setUseCondition("IEP");
        domainEntities.add(domainEntity2);
        when(domainDao.getAllChildDomains(frameworkId)).thenReturn(domainEntities);

        RatingStatisticModel result = ratingService.isCompleted(childId, from, to, frameworkId, frameworkDomainMap);

        assertNotNull(result);
    }

    @Test
    public void testIsCompleted2() {
        String childId = "child1";
        String from = "2022-01-01";
        String to = "2022-12-31";
        String frameworkId = "framework1";
        Map<String, Set<String>> frameworkDomainMap = new HashMap<>();
        Set<String> keyMeasures = new HashSet<>();
        keyMeasures.add("domainId");
        when(scoreDao.get(childId)).thenReturn(new ArrayList<>());
        RatingStatisticModel result = ratingService.isCompleted(childId, from, to, frameworkId, frameworkDomainMap, keyMeasures);

        assertNotNull(result);
    }
}
