package com.learninggenie.common.score;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;
public class DomainScoreServiceImplTest {
    @Test
    public void testGetDomainId() {
        // 准备测试数据
        String domainName = "SSD";
        List<DomainScoreValue> domainScoreValueList = new ArrayList<>();
        DomainScoreValue domainScoreValue = new DomainScoreValue();
        domainScoreValue.setDomainId("1");
        domainScoreValue.setDomainName("HLTH");
        domainScoreValueList.add(domainScoreValue);
        DomainScoreValue domainScoreValue1 = new DomainScoreValue();
        domainScoreValue1.setDomainId("2");
        domainScoreValue1.setDomainName("SSD");
        domainScoreValueList.add(domainScoreValue1);
        DomainScoreValue domainScoreValue2 = new DomainScoreValue();
        domainScoreValue2.setDomainId("3");
        domainScoreValue2.setDomainName("COG");
        domainScoreValueList.add(domainScoreValue2);
        // 调用被测试方法
        DomainScoreServiceImpl domainScoreService = new DomainScoreServiceImpl();
        String domainId = domainScoreService.getDomainId(domainName, domainScoreValueList);

        // 验证结果
        Assertions.assertEquals("2", domainId);
    }

}