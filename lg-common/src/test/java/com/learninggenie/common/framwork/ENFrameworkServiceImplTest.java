package com.learninggenie.common.framwork;

import com.learninggenie.common.data.dao.PortfolioDao;
import com.learninggenie.common.data.model.DomainLevelsEntity;
import com.learninggenie.common.data.model.ScoreTemplateEntity;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * 框架测试类
 */
@ExtendWith(MockitoExtension.class)
class ENFrameworkServiceImplTest {
    @InjectMocks
    private ENFrameworkServiceImpl frameworkService;

    @Mock
    private PortfolioDao portfolioDao;


    /**
     * 测试获取框架的领域和等级
     */
    @Test
    void getDomainLevelsMap() {
        ScoreTemplateEntity scoreTemplateEntity = new ScoreTemplateEntity();
        scoreTemplateEntity.setDomainLevelsJson("[{\n" +
                "  \"domainId\": \"test_5ec7e5e9e5a2\",\n" +
                "  \"measure\": \"test_5169957910c3\",\n" +
                "  \"measureName\": \"test_b2e47fd469e6\",\n" +
                "  \"levels\": [\n" +
                "    {\n" +
                "      \"id\": \"test_f16dcaee5fab\",\n" +
                "      \"name\": \"test_4dc72c717523\",\n" +
                "      \"type\": \"test_e7c60dec6ca2\",\n" +
                "      \"sortIndex\": \"test_25e31e77ae75\",\n" +
                "      \"value\": \"test_b4fe361fbf44\",\n" +
                "      \"finalValue\": \"test_2880f0b58d62\",\n" +
                "      \"finalName\": \"test_03dacdcfb290\",\n" +
                "      \"tip\": \"test_e677c7e90d60\",\n" +
                "      \"hidden\": true,\n" +
                "      \"rated\": false,\n" +
                "      \"levelIndex\": 54,\n" +
                "      \"scoreExamples\": [\n" +
                "        {\n" +
                "          \"exampleName\": \"test_59ebed1efba4\",\n" +
                "          \"content\": [\n" +
                "            \"test_6276d3dfe816\"\n" +
                "          ],\n" +
                "          \"columnSize\": 21\n" +
                "        }\n" +
                "      ]\n" +
                "    }\n" +
                "  ],\n" +
                "  \"levelMap\": {}\n" +
                "}]");
        when(portfolioDao.loadScoreTemplate(any())).thenReturn(scoreTemplateEntity);
        Map<String, DomainLevelsEntity> domainLevelsMap = frameworkService.getDomainLevelsMap("framework001");
    }
}