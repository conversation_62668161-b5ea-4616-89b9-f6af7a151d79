package com.learninggenie.common.monday.util;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class MondayAPIUtilTest {

    private static final Map<String, String> COMMON_HEADER = new HashMap<String, String>() {{
        put("Authorization", "eyJhbGciOiJIUzI1NiJ9.eyJ0aWQiOjg5MDU5ODgzLCJ1aWQiOjQ0MTg2NjQsImlhZCI6IjIwMjAtMTAtMjZUMDM6MzU6MDAuMDAwWiIsInBlciI6Im1lOndyaXRlIn0.H8ukOBBQ7Rhts06qWX1E-o8X-pKBnxoQMRlVpwiyNbU");
        put("API-Version", "2023-10");
    }};

    private static final String MONDAY_URL_V2 = "https://api.monday.com/v2";

    @Before
    public void setUp() {
        mockStatic(RequestUtil.class);
    }

    @Test
    public void createColumnWithValuesTest() throws IOException {
        String uuid = UUID.randomUUID().toString();
        Map<String, Object> params = new HashMap<>();
        // 替换参数
        params.put("query", "mutation{\n" +
                "  create_column(board_id: boardId, after_column_id :\"afterColumnId\", title:\"title\", description: \"description\", column_type:columnType) {\n" +
                "    id\n" +
                "    title\n" +
                "    description\n" +
                "  }\n" +
                "}");
        // 模拟请求
        when(RequestUtil.post(MONDAY_URL_V2, params, COMMON_HEADER)).thenReturn(uuid);
        String columnWithValues = MondayAPIUtil.createColumnWithValues("boardId", "afterColumnId", "title", "description", "columnType");
        // 验证结果
        assertNotNull(columnWithValues);
        assertEquals(uuid, columnWithValues);
    }

}
