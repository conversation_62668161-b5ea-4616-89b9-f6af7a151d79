package com.learninggenie.common.data.config.mybatis;

import org.apache.ibatis.type.JdbcType;
import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.sql.*;
import java.time.LocalDateTime;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class LocalDateTimeTypeHandlerTest {
    @InjectMocks
    private LocalDateTimeTypeHandler localDateTimeTypeHandler;

    @Mock
    private PreparedStatement ps;

    @Mock
    private ResultSet rs;

    @Mock
    private CallableStatement cs;

    /**
     * 测试设置非空参数
     *
     * @throws SQLException
     */
    @Test
    void testSetNonNullParameter() throws SQLException {
        localDateTimeTypeHandler.setNonNullParameter(ps, 0, LocalDateTime.now(), JdbcType.TIMESTAMP);

        verify(ps).setTimestamp(anyInt(), any());
    }

    /**
     * 测试通过列名获取 LocalDateTime 类型转换结果
     *
     * @throws SQLException
     */
    @Test
    void testGetNullableResult() throws SQLException {
        Timestamp timestamp = Timestamp.valueOf(LocalDateTime.now());
        when(rs.getTimestamp(any())).thenReturn(timestamp);
        LocalDateTime localDateTime = localDateTimeTypeHandler.getNullableResult(rs, "localDateTime");

        Assert.assertEquals(timestamp.toLocalDateTime(), localDateTime);
    }

    /**
     * 测试通过列下标获取 LocalDateTime 类型转换结果
     *
     * @throws SQLException
     */
    @Test
    void testGetNullableResult1() throws SQLException {
        Timestamp timestamp = Timestamp.valueOf(LocalDateTime.now());
        when(rs.getTimestamp(0)).thenReturn(timestamp);
        LocalDateTime localDateTime = localDateTimeTypeHandler.getNullableResult(rs, 0);

        Assert.assertEquals(timestamp.toLocalDateTime(), localDateTime);
    }

    /**
     * 测试通过列下标获取 LocalDateTime 类型转换结果
     *
     * @throws SQLException
     */
    @Test
    void testGetNullableResult2() throws SQLException {
        Timestamp timestamp = Timestamp.valueOf(LocalDateTime.now());
        when(cs.getTimestamp(0)).thenReturn(timestamp);
        LocalDateTime localDateTime = localDateTimeTypeHandler.getNullableResult(cs, 0);

        Assert.assertEquals(timestamp.toLocalDateTime(), localDateTime);
    }
}