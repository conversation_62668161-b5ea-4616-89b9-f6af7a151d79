package com.learninggenie.common.data.config.mybatis;

import org.apache.ibatis.type.JdbcType;
import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.sql.*;
import java.time.LocalDate;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class LocalDateTypeHandlerTest {
    @InjectMocks
    private LocalDateTypeHandler localDateTypeHandler;

    @Mock
    private PreparedStatement ps;

    @Mock
    private ResultSet rs;

    @Mock
    private CallableStatement cs;

    /**
     * 测试设置非空参数
     *
     * @throws SQLException
     */
    @Test
    void testSetNonNullParameter() throws SQLException {
        localDateTypeHandler.setNonNullParameter(ps, 0, LocalDate.now(), JdbcType.DATE);
        
        verify(ps).setDate(anyInt(), any());
    }

    /**
     * 测试通过列名获取 LocalDate 类型转换结果
     *
     * @throws SQLException
     */
    @Test
    void testGetNullableResult() throws SQLException {
        Date date = Date.valueOf(LocalDate.now());
        when(rs.getDate(any())).thenReturn(date);
        LocalDate localDate = localDateTypeHandler.getNullableResult(rs, "localDate");

        Assert.assertEquals(date.toLocalDate(), localDate);
    }

    /**
     * 测试通过列下标获取 LocalDate 类型转换结果
     *
     * @throws SQLException
     */
    @Test
    void testGetNullableResult1() throws SQLException {
        Date date = Date.valueOf(LocalDate.now());
        when(rs.getDate(0)).thenReturn(date);
        LocalDate localDate = localDateTypeHandler.getNullableResult(rs, 0);

        Assert.assertEquals(date.toLocalDate(), localDate);
    }

    /**
     * 测试通过列下标获取 LocalDate 类型转换结果
     *
     * @throws SQLException
     */
    @Test
    void testGetNullableResult2() throws SQLException {
        Date date = Date.valueOf(LocalDate.now());
        when(cs.getDate(0)).thenReturn(date);
        LocalDate localDate = localDateTypeHandler.getNullableResult(cs, 0);

        Assert.assertEquals(date.toLocalDate(), localDate);
    }
}