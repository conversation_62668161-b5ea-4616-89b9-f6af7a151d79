//package com.learninggenie.common.comm;
//
//import com.learninggenie.common.data.dao.AgencyDao;
//import com.learninggenie.common.data.dao.CenterDao;
//import com.learninggenie.common.data.dao.MediaBookDao;
//import com.learninggenie.common.data.dao.PushNotificationDao;
//import com.learninggenie.common.data.dao.impl.UserDaoImpl;
//import com.learninggenie.common.data.entity.AgencyEntity;
//import com.learninggenie.common.data.entity.MediaResourceEntity;
//import com.learninggenie.common.data.model.ResultPojo;
//import com.learninggenie.common.data.model.UserModel;
//import com.learninggenie.common.data.model.hyphenate.*;
//import com.learninggenie.common.utils.*;
//import org.junit.Assert;
//import org.junit.Before;
//import org.junit.Ignore;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.Mockito;
//import org.mockito.MockitoAnnotations;
//import org.powermock.api.mockito.PowerMockito;
//import org.powermock.core.classloader.annotations.PrepareForTest;
//import org.powermock.modules.junit4.PowerMockRunner;
//
//import java.sql.Time;
//import java.util.ArrayList;
//import java.util.Date;
//import java.util.List;
//
//import static org.mockito.Matchers.anyObject;
//import static org.mockito.Matchers.anyString;
//
//
//@PrepareForTest({RestApiUtil.class,JsonUtil.class})
//@RunWith(PowerMockRunner.class)
//public class CommServiceImplTest {
//
//    @InjectMocks
//    private CommServiceImpl commService;
//    @Mock
//    private AgencyDao agencyDao;
//    @Mock
//    private PushNotificationDao pushNotificationDao;
//    @Mock
//    private UserDaoImpl userDao;
//    @Mock
//    private MediaBookDao mediaBookDao;
//    @Mock
//    private CenterDao centerDao;
//
//    @Before
//    public void setUp() {
//        MockitoAnnotations.initMocks(this);
//    }
//
//
//    /**
//     * 关闭，删除agency的推送，两个
//     */
//    @Test
//    public void testDeletePushNotification_WithTwoAgencies() {
//        String email = "<EMAIL>";
//
//        AgencyEntity agencyEntity1 = new AgencyEntity();
//        agencyEntity1.setId("a001");
//        agencyEntity1.setName("agency1");
//
//        AgencyEntity agencyEntity2 = new AgencyEntity();
//        agencyEntity2.setId("a002");
//        agencyEntity2.setName("agency2");
//
//        List<AgencyEntity> agencyEntities = new ArrayList<>();
//        agencyEntities.add(agencyEntity1);
//        agencyEntities.add(agencyEntity2);
//
//        UserModel user = new UserModel();
//        user.setId("u001");
//        user.setRole("AGENCY_OWNER");
//
//        Mockito.when(agencyDao.getAgencyByEmail(email)).thenReturn(agencyEntities);
//        Mockito.when(userDao.getUserByEmail(email)).thenReturn(user);
//
//        commService.deletePushNotification(email, "Shawn Brown");
//
//        Mockito.verify(agencyDao, Mockito.times(1)).getAgencyByEmail(email);
//        Mockito.verify(pushNotificationDao, Mockito.times(1)).deleteAgencyPush("a001", "notification", "Shawn Brown");
//        Mockito.verify(pushNotificationDao, Mockito.times(1)).deleteAgencyPush("a002", "notification", "Shawn Brown");
//    }
//
//    /**
//     * email 为空
//     */
//    @Test(expected = IllegalArgumentException.class)
//    public void testDeletePushNotification_NoEmail() {
//        String email = "";
//
//        commService.deletePushNotification(email, "Shawn Brown");
//    }
//
//    /**
//     * 正常添加agency推送记录
//     */
//    @Test
//    public void testAddPushNotification() {
//        PushNotificationRequest request = new PushNotificationRequest();
//        request.setAgencyOwnerEmail("<EMAIL>");
//        request.setIntervalMinutes("5");
//        request.setMediaCategory("Shawn Brown");
//        Date now = TimeUtil.getUtcNow();
//        Date tomorrow = TimeUtil.addDays(now, 1);
//        String tomorrowStr = TimeUtil.format(tomorrow, TimeUtil.format2);
//        request.setPushTime(tomorrowStr);
//
//        UserModel user = new UserModel();
//        user.setId("u001");
//        user.setDisplayName("Boss");
//        user.setRole("AGENCY_OWNER");
//
//        MediaResourceEntity resource1 = new MediaResourceEntity();
//        resource1.setVideoList("v001");
//
//        MediaResourceEntity resource2 = new MediaResourceEntity();
//        resource2.setVideoList("v002");
//
//        List<MediaResourceEntity> mediaResourceEntities = new ArrayList<>();
//        mediaResourceEntities.add(resource1);
//        mediaResourceEntities.add(resource2);
//
//        AgencyEntity agency1 = new AgencyEntity();
//        agency1.setId("a001");
//
//        List<AgencyEntity> agencyEntities = new ArrayList<>();
//        agencyEntities.add(agency1);
//
//        String centerTimeZone = "time/zone";
//
//        Mockito.when(userDao.getUserByEmail(request.getAgencyOwnerEmail())).thenReturn(user);
//        Mockito.when(mediaBookDao.getMediaResourceByCategory(Mockito.anyString())).thenReturn(mediaResourceEntities);
//        Mockito.when(agencyDao.getAgencyByEmail(request.getAgencyOwnerEmail())).thenReturn(agencyEntities);
//        Mockito.when(centerDao.getFirstCenterTimeZoneByAgencyId(Mockito.anyString())).thenReturn(centerTimeZone);
//
//        commService.addPushNotification(request);
//
//        Mockito.verify(userDao, Mockito.times(1)).getUserByEmail("<EMAIL>");
//        Mockito.verify(mediaBookDao, Mockito.times(1)).getMediaResourceByCategory(Mockito.anyString());
//        Mockito.verify(agencyDao, Mockito.times(1)).getAgencyByEmail("<EMAIL>");
//        Mockito.verify(agencyDao, Mockito.times(1)).setMeta(agency1.getId(), "IS_PUSH", "true");
//        Mockito.verify(pushNotificationDao, Mockito.times(2)).addPushMessage(Mockito.anyObject());
//    }
//
//    /**
//     * 没有email地址
//     */
//    @Test(expected = IllegalArgumentException.class)
//    public void testAddPushNotification_NoEmail() {
//        PushNotificationRequest request = new PushNotificationRequest();
//        request.setAgencyOwnerEmail("");
//        request.setIntervalMinutes("5");
////        request.setMediaCategory();
//        Date now = TimeUtil.getUtcNow();
//        Date tomorrow = TimeUtil.addDays(now, 1);
//        String tomorrowStr = TimeUtil.format(tomorrow, TimeUtil.format2);
//        request.setPushTime(tomorrowStr);
//
//        commService.addPushNotification(request);
//    }
//
//    /**
//     * 用户不是agencyOwner
//     */
//    @Test(expected = IllegalArgumentException.class)
//    public void testAddPushNotification_NotAgencyOwner() {
//        PushNotificationRequest request = new PushNotificationRequest();
//        request.setAgencyOwnerEmail("<EMAIL>");
//        request.setIntervalMinutes("5");
////        request.setMediaCategory();
//        Date now = TimeUtil.getUtcNow();
//        Date tomorrow = TimeUtil.addDays(now, 1);
//        String tomorrowStr = TimeUtil.format(tomorrow, TimeUtil.format2);
//        request.setPushTime(tomorrowStr);
//
//        UserModel user = new UserModel();
//        user.setId("u001");
//        user.setDisplayName("Teacher");
//        user.setRole("COLLABORATOR");
//
//        Mockito.when(userDao.getUserByEmail(request.getAgencyOwnerEmail())).thenReturn(user);
//
//        commService.addPushNotification(request);
//    }
//
//    /**
//     * 没有时区
//     */
//    @Test(expected = IllegalArgumentException.class)
//    public void testAddPushNotification_NotTimeZone() {
//        PushNotificationRequest request = new PushNotificationRequest();
//        request.setAgencyOwnerEmail("<EMAIL>");
//        request.setIntervalMinutes("5");
////        request.setMediaCategory();
//        Date now = TimeUtil.getUtcNow();
//        Date tomorrow = TimeUtil.addDays(now, 1);
//        String tomorrowStr = TimeUtil.format(tomorrow, TimeUtil.format2);
//        request.setPushTime(tomorrowStr);
//
//        UserModel user = new UserModel();
//        user.setId("u001");
//        user.setDisplayName("Boss");
//        user.setRole("AGENCY_OWNER");
//
//        MediaResourceEntity resource1 = new MediaResourceEntity();
//        resource1.setVideoList("v001");
//
//        MediaResourceEntity resource2 = new MediaResourceEntity();
//        resource2.setVideoList("v002");
//
//        List<MediaResourceEntity> mediaResourceEntities = new ArrayList<>();
//        mediaResourceEntities.add(resource1);
//        mediaResourceEntities.add(resource2);
//
//        AgencyEntity agency1 = new AgencyEntity();
//        agency1.setId("a001");
//
//        List<AgencyEntity> agencyEntities = new ArrayList<>();
//        agencyEntities.add(agency1);
//
//        Mockito.when(userDao.getUserByEmail(request.getAgencyOwnerEmail())).thenReturn(user);
//        Mockito.when(mediaBookDao.getMediaResourceByCategory(Mockito.anyString())).thenReturn(mediaResourceEntities);
//        Mockito.when(agencyDao.getAgencyByEmail(request.getAgencyOwnerEmail())).thenReturn(agencyEntities);
//        Mockito.when(centerDao.getFirstCenterTimeZoneByAgencyId(Mockito.anyString())).thenReturn(null);
//        commService.addPushNotification(request);
//
//        Mockito.verify(userDao, Mockito.times(1)).getUserByEmail("<EMAIL>");
//        Mockito.verify(mediaBookDao, Mockito.times(1)).getMediaResourceByCategory(Mockito.anyString());
//        Mockito.verify(agencyDao, Mockito.times(1)).getAgencyByEmail("<EMAIL>");
//        Mockito.verify(agencyDao, Mockito.times(1)).setMeta(agency1.getId(), "IS_PUSH", "true");
//        Mockito.verify(pushNotificationDao, Mockito.times(0)).addPushMessage(Mockito.anyObject());
//    }
//
//    /**
//     * 添加的时间是现在之前的
//     */
//    @Test(expected = IllegalArgumentException.class)
//    public void testAddPushNotification_beforeNow() {
//        PushNotificationRequest request = new PushNotificationRequest();
//        request.setAgencyOwnerEmail("<EMAIL>");
//        request.setIntervalMinutes("5");
////        request.setMediaCategory();
//        request.setPushTime("2017-11-07 00:00:00.000");
//
//        UserModel user = new UserModel();
//        user.setId("u001");
//        user.setDisplayName("Boss");
//        user.setRole("AGENCY_OWNER");
//
//        MediaResourceEntity resource1 = new MediaResourceEntity();
//        resource1.setVideoList("v001");
//
//        MediaResourceEntity resource2 = new MediaResourceEntity();
//        resource2.setVideoList("v002");
//
//        List<MediaResourceEntity> mediaResourceEntities = new ArrayList<>();
//        mediaResourceEntities.add(resource1);
//        mediaResourceEntities.add(resource2);
//
//        AgencyEntity agency1 = new AgencyEntity();
//        agency1.setId("a001");
//
//        List<AgencyEntity> agencyEntities = new ArrayList<>();
//        agencyEntities.add(agency1);
//
//        String centerTimeZone = "time/zone";
//
//        Mockito.when(userDao.getUserByEmail(request.getAgencyOwnerEmail())).thenReturn(user);
//        Mockito.when(mediaBookDao.getMediaResourceByCategory(Mockito.anyString())).thenReturn(mediaResourceEntities);
//        Mockito.when(agencyDao.getAgencyByEmail(request.getAgencyOwnerEmail())).thenReturn(agencyEntities);
//        Mockito.when(centerDao.getFirstCenterTimeZoneByAgencyId(Mockito.anyString())).thenReturn(centerTimeZone);
//
//        commService.addPushNotification(request);
//    }
//
//    /**
//     * 没有media resource
//     */
//    @Test(expected = IllegalArgumentException.class)
//    public void testAddPushNotification_NoResources() {
//        PushNotificationRequest request = new PushNotificationRequest();
//        request.setAgencyOwnerEmail("<EMAIL>");
//        request.setIntervalMinutes("5");
////        request.setMediaCategory();
//        Date now = TimeUtil.getUtcNow();
//        Date tomorrow = TimeUtil.addDays(now, 1);
//        String tomorrowStr = TimeUtil.format(tomorrow, TimeUtil.format2);
//        request.setPushTime(tomorrowStr);
//
//        UserModel user = new UserModel();
//        user.setId("u001");
//        user.setDisplayName("Boss");
//        user.setRole("AGENCY_OWNER");
//
//        AgencyEntity agency1 = new AgencyEntity();
//        agency1.setId("a001");
//
//        List<AgencyEntity> agencyEntities = new ArrayList<>();
//        agencyEntities.add(agency1);
//
//        String centerTimeZone = "time/zone";
//
//        List<MediaResourceEntity> mediaResourceEntities = new ArrayList<>();
//
//        Mockito.when(userDao.getUserByEmail(request.getAgencyOwnerEmail())).thenReturn(user);
//        Mockito.when(mediaBookDao.getMediaResourceByCategory(Mockito.anyString())).thenReturn(mediaResourceEntities);
//        Mockito.when(agencyDao.getAgencyByEmail(request.getAgencyOwnerEmail())).thenReturn(agencyEntities);
//        Mockito.when(centerDao.getFirstCenterTimeZoneByAgencyId(Mockito.anyString())).thenReturn(centerTimeZone);
//
//        commService.addPushNotification(request);
//
//        Mockito.verify(userDao, Mockito.times(1)).getUserByEmail("<EMAIL>");
//        Mockito.verify(mediaBookDao, Mockito.times(1)).getMediaResourceByCategory(Mockito.anyString());
//    }
//
//    /**
//     * 有周六周日
//     */
//    @Test
//    public void testAddPushNotification_WithWeekend() {
//        PushNotificationRequest request = new PushNotificationRequest();
//        request.setAgencyOwnerEmail("<EMAIL>");
//        request.setIntervalMinutes("1440");
//        request.setMediaCategory("Shawn Brown");
//        Date now = TimeUtil.getUtcNow();
//        Date tomorrow = TimeUtil.addDays(now, 1);
//        String tomorrowStr = TimeUtil.format(tomorrow, TimeUtil.format2);
//        request.setPushTime(tomorrowStr);
//
//        UserModel user = new UserModel();
//        user.setId("u001");
//        user.setDisplayName("Boss");
//        user.setRole("AGENCY_OWNER");
//
//        MediaResourceEntity resource1 = new MediaResourceEntity();
//        resource1.setVideoList("v001,v001,v001,v001,v001,v001,v001");
//
//        List<MediaResourceEntity> mediaResourceEntities = new ArrayList<>();
//        mediaResourceEntities.add(resource1);
//
//        AgencyEntity agency1 = new AgencyEntity();
//        agency1.setId("a001");
//
//        List<AgencyEntity> agencyEntities = new ArrayList<>();
//        agencyEntities.add(agency1);
//
//        String centerTimeZone = "time/zone";
//
//        Mockito.when(userDao.getUserByEmail(request.getAgencyOwnerEmail())).thenReturn(user);
//        Mockito.when(mediaBookDao.getMediaResourceByCategory(Mockito.anyString())).thenReturn(mediaResourceEntities);
//        Mockito.when(agencyDao.getAgencyByEmail(request.getAgencyOwnerEmail())).thenReturn(agencyEntities);
//        Mockito.when(centerDao.getFirstCenterTimeZoneByAgencyId(Mockito.anyString())).thenReturn(centerTimeZone);
//
//        commService.addPushNotification(request);
//
//        Mockito.verify(userDao, Mockito.times(1)).getUserByEmail("<EMAIL>");
//        Mockito.verify(mediaBookDao, Mockito.times(1)).getMediaResourceByCategory(Mockito.anyString());
//        Mockito.verify(agencyDao, Mockito.times(1)).getAgencyByEmail("<EMAIL>");
//        Mockito.verify(agencyDao, Mockito.times(1)).setMeta(agency1.getId(), "IS_PUSH", "true");
//        Mockito.verify(pushNotificationDao, Mockito.times(7)).addPushMessage(Mockito.anyObject());
//    }
//
//    /**
//     * 获取谷歌视频，正常获取
//     */
//    @Test
//    @Ignore
//    public void testGetGoogleVideos() {
//        String mediaCategory = "shawn brown";
//        String videoId = "";
//
//        MediaResourceEntity resource1 = new MediaResourceEntity();
//        resource1.setPlaylistId("p001");
//        resource1.setVideoList("tkmUgR6Geyg,v002");
//
//        List<MediaResourceEntity> mediaResourceEntities = new ArrayList<>();
//        mediaResourceEntities.add(resource1);
//
//        ResultPojo resultPojo = new ResultPojo();
//        resultPojo.setStatus(200);
//        resultPojo.setData("[{\"extEmbedCode\":\"<!DOCTYPE html><html style=\\\"height:100%\\\"><head><script type=\\\"text/javascript\\\"src=\\\"https://www.youtube.com/player_api\\\"></script></head><body style=\\\"height:100%\\\"><div id=\\\"ques\\\"style=\\\"position:absolute;width:100%;height:100%;display:none;background-color:rgba(243,243,243,.5);padding-top:100px;padding-left:50px;color:#fff\\\"><form style=\\\"background-color:#000;height:100px;width:200px;padding:20px\\\">Like?<input type=\\\"radio\\\"name=\\\"group1\\\">Yes<input type=\\\"radio\\\"name=\\\"group1\\\">No Comments:<input type=\\\"text\\\"><br><br><button type=\\\"submit\\\">Submit</button></form></div><!--placeholder--><div id=\\\"player\\\"></div></body></html><script>var videoId=\\\"tkmUgR6Geyg\\\";function onYouTubePlayerAPIReady(){player=new YT.Player(\\\"player\\\",{height:\\\"100%\\\",width:\\\"100%\\\",videoId:videoId,events:{onStateChange:stateChange}})}function stateChange(){}/*placeholder*/</script>\",\"duration\":\"PT4M41S\",\"contentDetails\":null,\"etag\":\"\\\"ld9biNPKjAjgjV7EZ4EKeEGrhao/6k0QxfvzUzCy3U5BVoVGb9TEwtY\\\"\",\"id\":\"tkmUgR6Geyg\",\"kind\":\"youtube#video\",\"snippet\":{\"channelId\":null,\"channelTitle\":null,\"description\":null,\"playlistId\":null,\"position\":null,\"publishedAt\":\"2015-02-15T16:56:22.000+00:00\",\"resourceId\":{\"channelId\":null,\"kind\":null,\"playlistId\":null,\"videoId\":\"tkmUgR6Geyg\",\"ETag\":null},\"thumbnails\":{\"default\":{\"height\":90,\"url\":\"https://i.ytimg.com/vi/tkmUgR6Geyg/default.jpg\",\"width\":120,\"ETag\":null},\"high\":{\"height\":360,\"url\":\"https://i.ytimg.com/vi/tkmUgR6Geyg/hqdefault.jpg\",\"width\":480,\"ETag\":null},\"maxres\":null,\"medium\":{\"height\":180,\"url\":\"https://i.ytimg.com/vi/tkmUgR6Geyg/mqdefault.jpg\",\"width\":320,\"ETag\":null},\"standard\":{\"height\":480,\"url\":\"https://i.ytimg.com/vi/tkmUgR6Geyg/sddefault.jpg\",\"width\":640,\"ETag\":null},\"ETag\":null},\"title\":\"Super Fun Show - by Shawn Brown\",\"ETag\":null},\"status\":null},{\"extEmbedCode\":\"<!DOCTYPE html><html style=\\\"height:100%\\\"><head><script type=\\\"text/javascript\\\"src=\\\"https://www.youtube.com/player_api\\\"></script></head><body style=\\\"height:100%\\\"><div id=\\\"ques\\\"style=\\\"position:absolute;width:100%;height:100%;display:none;background-color:rgba(243,243,243,.5);padding-top:100px;padding-left:50px;color:#fff\\\"><form style=\\\"background-color:#000;height:100px;width:200px;padding:20px\\\">Like?<input type=\\\"radio\\\"name=\\\"group1\\\">Yes<input type=\\\"radio\\\"name=\\\"group1\\\">No Comments:<input type=\\\"text\\\"><br><br><button type=\\\"submit\\\">Submit</button></form></div><!--placeholder--><div id=\\\"player\\\"></div></body></html><script>var videoId=\\\"stvF4gan12k\\\";function onYouTubePlayerAPIReady(){player=new YT.Player(\\\"player\\\",{height:\\\"100%\\\",width:\\\"100%\\\",videoId:videoId,events:{onStateChange:stateChange}})}function stateChange(){}/*placeholder*/</script>\",\"duration\":\"PT2M31S\",\"contentDetails\":null,\"etag\":\"\\\"ld9biNPKjAjgjV7EZ4EKeEGrhao/zWqy7nW7HQ16gtTn-lUvNVc19S8\\\"\",\"id\":\"stvF4gan12k\",\"kind\":\"youtube#video\",\"snippet\":{\"channelId\":null,\"channelTitle\":null,\"description\":null,\"playlistId\":null,\"position\":null,\"publishedAt\":\"2017-02-01T03:00:27.000+00:00\",\"resourceId\":{\"channelId\":null,\"kind\":null,\"playlistId\":null,\"videoId\":\"stvF4gan12k\",\"ETag\":null},\"thumbnails\":{\"default\":{\"height\":90,\"url\":\"https://i.ytimg.com/vi/stvF4gan12k/default.jpg\",\"width\":120,\"ETag\":null},\"high\":{\"height\":360,\"url\":\"https://i.ytimg.com/vi/stvF4gan12k/hqdefault.jpg\",\"width\":480,\"ETag\":null},\"maxres\":null,\"medium\":{\"height\":180,\"url\":\"https://i.ytimg.com/vi/stvF4gan12k/mqdefault.jpg\",\"width\":320,\"ETag\":null},\"standard\":{\"height\":480,\"url\":\"https://i.ytimg.com/vi/stvF4gan12k/sddefault.jpg\",\"width\":640,\"ETag\":null},\"ETag\":null},\"title\":\"Dinosaur Story - Part 1\",\"ETag\":null},\"status\":null}]");
//
//        Mockito.when(mediaBookDao.getMediaResourceByCategory(Mockito.anyString())).thenReturn(mediaResourceEntities);
//        PowerMockito.mockStatic(RestApiUtil.class);
//        PowerMockito.when(RestApiUtil.get("null/api/medias/playlistItems?playlistIds=p001", null, false)).thenReturn(resultPojo);
//
//        GoogleVideoHyphenate googleVideoHyphenate = commService.getGoogleVideos("tkmUgR6Geyg", "Shawn Brown", null);
//
//        Assert.assertEquals("Super Fun Show - by Shawn Brown", googleVideoHyphenate.getTitle());
//        Assert.assertEquals("https://i.ytimg.com/vi/tkmUgR6Geyg/default.jpg", googleVideoHyphenate.getImgUrl());
//        Assert.assertEquals("[{\"duration\"&#58;\"PT4M41S\"&#44;\"snippet\"&#58;{\"playlistId\"&#58;null&#44;\"resourceId\"&#58;{\"playlistId\"&#58;null&#44;\"ETag\"&#58;null&#44;\"kind\"&#58;null&#44;\"videoId\"&#58;\"tkmUgR6Geyg\"&#44;\"channelId\"&#58;null}&#44;\"publishedAt\"&#58;\"2015-02-15T16&#58;56&#58;22.000+00&#58;00\"&#44;\"ETag\"&#58;null&#44;\"description\"&#58;null&#44;\"position\"&#58;null&#44;\"thumbnails\"&#58;{\"standard\"&#58;{\"ETag\"&#58;null&#44;\"width\"&#58;640&#44;\"url\"&#58;\"https&#58;//i.ytimg.com/vi/tkmUgR6Geyg/sddefault.jpg\"&#44;\"height\"&#58;480}&#44;\"default\"&#58;{\"ETag\"&#58;null&#44;\"width\"&#58;120&#44;\"url\"&#58;\"https&#58;//i.ytimg.com/vi/tkmUgR6Geyg/default.jpg\"&#44;\"height\"&#58;90}&#44;\"high\"&#58;{\"ETag\"&#58;null&#44;\"width\"&#58;480&#44;\"url\"&#58;\"https&#58;//i.ytimg.com/vi/tkmUgR6Geyg/hqdefault.jpg\"&#44;\"height\"&#58;360}&#44;\"maxres\"&#58;null&#44;\"ETag\"&#58;null&#44;\"medium\"&#58;{\"ETag\"&#58;null&#44;\"width\"&#58;320&#44;\"url\"&#58;\"https&#58;//i.ytimg.com/vi/tkmUgR6Geyg/mqdefault.jpg\"&#44;\"height\"&#58;180}}&#44;\"title\"&#58;\"Super Fun Show - by Shawn Brown\"&#44;\"channelId\"&#58;null&#44;\"channelTitle\"&#58;null}&#44;\"extEmbedCode\"&#58;\"<!DOCTYPE html><html style=\\\"height&#58;100%\\\"><head><script type=\\\"text/javascript\\\"src=\\\"https&#58;//www.youtube.com/player_api\\\"><\\/script><\\/head><body style=\\\"height&#58;100%\\\"><div id=\\\"ques\\\"style=\\\"position&#58;absolute;width&#58;100%;height&#58;100%;display&#58;none;background-color&#58;rgba(243&#44;243&#44;243&#44;.5);padding-top&#58;100px;padding-left&#58;50px;color&#58;#fff\\\"><form style=\\\"background-color&#58;#000;height&#58;100px;width&#58;200px;padding&#58;20px\\\">Like?<input type=\\\"radio\\\"name=\\\"group1\\\">Yes<input type=\\\"radio\\\"name=\\\"group1\\\">No Comments&#58;<input type=\\\"text\\\"><br><br><button type=\\\"submit\\\">Submit<\\/button><\\/form><\\/div><!--placeholder--><div id=\\\"player\\\"><\\/div><\\/body><\\/html><script>var videoId=\\\"tkmUgR6Geyg\\\";function onYouTubePlayerAPIReady(){player=new YT.Player(\\\"player\\\"&#44;{height&#58;\\\"100%\\\"&#44;width&#58;\\\"100%\\\"&#44;videoId&#58;videoId&#44;events&#58;{onStateChange&#58;stateChange}})}function stateChange(){}/*placeholder*/<\\/script>\"&#44;\"kind\"&#58;\"youtube#video\"&#44;\"etag\"&#58;\"\\\"ld9biNPKjAjgjV7EZ4EKeEGrhao/6k0QxfvzUzCy3U5BVoVGb9TEwtY\\\"\"&#44;\"id\"&#58;\"tkmUgR6Geyg\"&#44;\"contentDetails\"&#58;null&#44;\"status\"&#58;null}]", googleVideoHyphenate.getSongInfo());
//    }
//
//    /**
//     * 获取谷歌视频,没有视频资源
//     */
//    @Test
//    public void testGetGoogleVideos_NoMediaResource() {
//        String mediaCategory = "shawn brown";
//        String videoId = "";
//
//        List<MediaResourceEntity> mediaResourceEntities = new ArrayList<>();
//
//        Mockito.when(mediaBookDao.getMediaResourceByCategory(Mockito.anyString())).thenReturn(mediaResourceEntities);
//
////        GoogleVideoHyphenate googleVideoHyphenate = commService.getGoogleVideos("tkmUgR6Geyg", "Shawn Brown");
////        Assert.assertEquals(null, googleVideoHyphenate.getTitle());
////        Assert.assertEquals(null, googleVideoHyphenate.getSongInfo());
////        Assert.assertEquals(null, googleVideoHyphenate.getImgUrl());
//    }
//
//}