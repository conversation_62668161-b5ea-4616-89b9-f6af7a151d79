package com.learninggenie.common.report;

import com.google.protobuf.InvalidProtocolBufferException;
import org.junit.Ignore;
import org.junit.Test;

import static org.junit.Assert.assertEquals;

/**
 * Created by miao on 7/11/2016.
 */
public class LGSnapshotTest{
    @Ignore
    @Test
    public void testStudentSnapshotObject() throws InvalidProtocolBufferException {
        LGSnapshot.StudentSnapshot studentSnapshot = LGSnapshot.StudentSnapshot.newBuilder()
                .setId("xxxxxx-xxxxxxx-xxxxxxx-xxxx")
                .setFirstName("mark")
                .setLastName("demon")
                .build();
        byte[] students = studentSnapshot.toByteArray();
        LGSnapshot.StudentSnapshot readBackStudentSnapshot = LGSnapshot.StudentSnapshot.parseFrom(students);
        assertEquals(studentSnapshot.toString(), readBackStudentSnapshot.toString());
    }

}