package com.learninggenie.common.report;

import com.learninggenie.common.data.dao.AgencyDao;
import com.learninggenie.common.data.dao.DomainDao;
import com.learninggenie.common.data.dao.ReportDao;
import com.learninggenie.common.data.dao.StudentDao;
import com.learninggenie.common.data.model.AgencySnapshotEntity;
import com.learninggenie.common.data.model.SnapshotResponse;
import com.learninggenie.common.data.model.StudentSnapshotEntity;
import com.learninggenie.common.region.RegionService;
import com.learninggenie.common.score.RatingService;
import com.learninggenie.common.utils.GenerateCodeUtil;
import com.learninggenie.common.utils.TimeUtil;
import org.json.JSONObject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.junit.Assert.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class AnalysisServiceImplTest {
    @Mock
    private RatingService ratingService;

    @Mock
    private AgencyDao agencyDao;

    @Mock
    private DomainDao domainDao;

    @InjectMocks
    private AnalysisServiceImpl analysisService;

    @Mock
    private RegionService regionService;

    @Mock
    private StudentDao studentDao;

    @Mock
    private ReportDao reportDao;

    @BeforeEach
    public void setUp() {
        Mockito.reset(ratingService);
    }

    /**
     * 测试 getAttrKeyFromAttrTemplate 方法
     * case：当框架 ID 为 "PS" 时，返回 "PS_ATTR"
     **/
    @Test
    public void testGetAttrKeyFromAttrTemplateWhenFrameworkIdIsPSThenReturnPSAttrKey() {
        // 创建一个 AttrTemplate 对象，设置 PS 属性为 "PS_ATTR" 和所有属性为 "DRDP_TECH_ATTR_ALL"
        LGSnapshot.AttrTemplate attrTemplate = LGSnapshot.AttrTemplate.newBuilder().setPs("PS_ATTR")
                .setAll("DRDP_TECH_ATTR_ALL").build();
        // 当 ratingService.isPSFramework 方法的输入为 "PS" 时，返回 true
        when(ratingService.isPSFramework("PS")).thenReturn(true);
        // 当框架 ID 为 "PS" 时，调用 getAttrKeyFromAttrTemplate 方法
        String result = analysisService.getAttrKeyFromAttrTemplate("PS", attrTemplate);
        // 验证返回结果是否为 "PS_ATTR"
        assertEquals("PS_ATTR", result);
    }

    /**
     * 测试 getAttrKeyFromAttrTemplate 方法
     * case：当框架 ID 为 "IT" 时，返回 "IT_ATTR"
     **/
    @Test
    public void testGetAttrKeyFromAttrTemplateWhenFrameworkIdIsITThenReturnITAttrKey() {
        // 创建一个 AttrTemplate 对象，设置 IT 属性为 "IT_ATTR" 和所有属性为 "DRDP_TECH_ATTR_ALL"
        LGSnapshot.AttrTemplate attrTemplate = LGSnapshot.AttrTemplate.newBuilder().setIt("IT_ATTR")
                .setAll("DRDP_TECH_ATTR_ALL").build();
        // 当 ratingService.isITFramework 方法的输入为 "IT" 时，返回 true
        when(ratingService.isITFramework("IT")).thenReturn(true);
        // 当框架 ID 为 "IT" 时，调用 getAttrKeyFromAttrTemplate 方法
        String result = analysisService.getAttrKeyFromAttrTemplate("IT", attrTemplate);
        // 验证返回结果是否为 "IT_ATTR"
        assertEquals("IT_ATTR", result);
    }

    /**
     * 测试 getAttrKeyFromAttrTemplate 方法
     * case：当框架 ID 为 "K" 时，返回 "K_ATTR"
     **/
    @Test
    public void testGetAttrKeyFromAttrTemplateWhenFrameworkIdIsKThenReturnKAttrKey() {
        // 创建一个 AttrTemplate 对象，设置 K 属性为 "K_ATTR" 和所有属性为 "DRDP_TECH_ATTR_ALL"
        LGSnapshot.AttrTemplate attrTemplate = LGSnapshot.AttrTemplate.newBuilder().setK("K_ATTR")
                .setAll("DRDP_TECH_ATTR_ALL").build();
        // 当 ratingService.isKFramework 方法的输入为 "K" 时，返回 true
        when(ratingService.isKFramework("K")).thenReturn(true);
        // 当框架 ID 为 "K" 时，调用 getAttrKeyFromAttrTemplate 方法
        String result = analysisService.getAttrKeyFromAttrTemplate("K", attrTemplate);
        // 验证返回结果是否为 "K_ATTR"
        assertEquals("K_ATTR", result);
    }

    /**
     * 测试 getAttrKeyFromAttrTemplate 方法
     * case：当框架 ID 为 "SA" 时，返回 "SA_ATTR"
     **/
    @Test
    public void testGetAttrKeyFromAttrTemplateWhenFrameworkIdIsSAThenReturnSAAttrKey() {
        // 创建一个 AttrTemplate 对象，设置 SA 属性为 "SA_ATTR" 和所有属性为 "DRDP_TECH_ATTR_ALL"
        LGSnapshot.AttrTemplate attrTemplate = LGSnapshot.AttrTemplate.newBuilder().setSa("SA_ATTR")
                .setAll("DRDP_TECH_ATTR_ALL").build();
        // 当 ratingService.isSAFramework 方法的输入为 "SA" 时，返回 true
        when(ratingService.isSAFramework("SA")).thenReturn(true);
        // 当框架 ID 为 "SA" 时，调用 getAttrKeyFromAttrTemplate 方法
        String result = analysisService.getAttrKeyFromAttrTemplate("SA", attrTemplate);
        // 验证返回结果是否为 "SA_ATTR"
        assertEquals("SA_ATTR", result);
    }

    /**
     * 测试 getAttrKeyFromAttrTemplate 方法
     * case：当框架 ID 为 "UNKNOWN" 时，返回 "COMMON_ATTR"
     **/
    @Test
    public void testGetAttrKeyFromAttrTemplateWhenFrameworkIdIsUnknownThenReturnCommonAttr() {
        // 创建一个 AttrTemplate 对象，设置 Common 属性为 "COMMON_ATTR"
        LGSnapshot.AttrTemplate attrTemplate = LGSnapshot.AttrTemplate.newBuilder().setCommon("COMMON_ATTR").build();
        // 当框架 ID 为 "UNKNOWN" 时，调用 getAttrKeyFromAttrTemplate 方法
        String result = analysisService.getAttrKeyFromAttrTemplate("UNKNOWN", attrTemplate);
        // 验证返回结果是否为 "COMMON_ATTR"
        assertEquals("COMMON_ATTR", result);
    }

    /**
     * 测试 getAttrKeyFromAttrTemplate 方法
     * case：当 frameworkId 输入为 null 或空白时，返回 "COMMON_ATTR"
     **/
    @Test
    public void testGetAttrKeyFromAttrTemplateWhenInputIsNullOrBlankThenReturnCommonAttr() {
        // 创建一个 AttrTemplate 对象，设置 Common 属性为 "COMMON_ATTR"
        LGSnapshot.AttrTemplate attrTemplate = LGSnapshot.AttrTemplate.newBuilder().setCommon("COMMON_ATTR").build();
        // 当输入为 null 时，调用 getAttrKeyFromAttrTemplate 方法
        String result = analysisService.getAttrKeyFromAttrTemplate(null, attrTemplate);
        // 验证返回结果是否为 "COMMON_ATTR"
        assertEquals("COMMON_ATTR", result);

        // 当输入为 null 时，调用 getAttrKeyFromAttrTemplate 方法
        String result2 = analysisService.getAttrKeyFromAttrTemplate("", attrTemplate);
        // 验证返回结果是否为 "COMMON_ATTR"
        assertEquals("COMMON_ATTR", result2);
    }

    /**
     * 测试 getAttrKeyFromAttrTemplate 方法
     * case：当 attrTemplate 输入为 null 时，返回 "COMMON_ATTR"
     **/
    @Test
    public void testGetAttrKeyFromAttrTemplateWhenInputIsNullThenReturnCommonAttr() {
        // 当输入为 null 时，调用 getAttrKeyFromAttrTemplate 方法
        String result = analysisService.getAttrKeyFromAttrTemplate("IT", null);
        // 验证返回结果是否为 "COMMON_ATTR"
        assertEquals("COMMON_ATTR", result);
    }

    @Test
    public void testGetSnapshot() {
        StudentSnapshotEntity studentSnapshotEntity = prepareSnapshotEntityData(true);

        AgencySnapshotEntity agencySnapshotEntity = new AgencySnapshotEntity();
        LGSnapshot.StudentRecordSnapshot studentSnapshot = LGSnapshot.StudentRecordSnapshot.newBuilder()
                .setRecordId("r001")
                .setCreatedAt(TimeUtil.getUtcNow().getTime())
                .setUpdatedAt(TimeUtil.getUtcNow().getTime())
                .build();
        LGSnapshot.ClassSnapshot classSnapshot =LGSnapshot.ClassSnapshot.newBuilder()
                .setId("sg001")
                .setName("sg001")
                .setCreatedAt(TimeUtil.getUtcNow().getTime())
                .setUpdatedAt(TimeUtil.getUtcNow().getTime())
                .addRecords(studentSnapshot)
                .build();
        LGSnapshot.CenterSnapshot centerSnapshot = LGSnapshot.CenterSnapshot.newBuilder()
                .setId("sc001")
                .setName("sc001")
                .setCreatedAt(TimeUtil.getUtcNow().getTime())
                .setUpdatedAt(TimeUtil.getUtcNow().getTime())
                .addClasses(classSnapshot)
                .build();
        LGSnapshot.Properties property  = LGSnapshot.Properties.newBuilder()
                .setName("a001")
                .setValue("avalue001")
                .setType("atype001")
                .build();
        LGSnapshot.AgencySnapshot agencySnapshot = LGSnapshot.AgencySnapshot.newBuilder()
                .setId("a000001")
                .setName("s000001")
                .setCreatedAt(TimeUtil.getUtcNow().getTime())
                .setUpdatedAt(TimeUtil.getUtcNow().getTime())
                .addCenters(centerSnapshot)
                .addProperties(property)
                .build();
        agencySnapshotEntity.setId("snapshotId");
        agencySnapshotEntity.setData(agencySnapshot.toByteArray());
        when(agencyDao.getSnapshot(anyString(), anyString())).thenReturn(agencySnapshotEntity);
        when(agencyDao.isDRDPtech(anyString())).thenReturn(true);
        when(domainDao.getAllChildDomains(anyString())).thenReturn(new ArrayList<>());
        when(ratingService.getLanguageJson(anyString(), anyString())).thenReturn(new JSONObject());

        SnapshotResponse response = analysisService.getSnapshot(studentSnapshotEntity, true, "en");

        assertNotNull(response);
    }


    private StudentSnapshotEntity prepareSnapshotEntityData(boolean prepareSsidAttr){
        StudentSnapshotEntity snapshotEntity = new StudentSnapshotEntity();
        LGSnapshot.StudentSnapshot.Builder studentSnapshotBuilder = LGSnapshot.StudentSnapshot.newBuilder();
        Date birth = TimeUtil.parseDate("2016-11-3");
        studentSnapshotBuilder.setId("test001")
                .setFirstName("testFirstName")
                .setMiddleName("testMiddleName")
                .setLastName("testLastName")
                .setBirthday(birth.getTime())
                .setGender(LGSnapshot.StudentSnapshot.GenderType.FEMALE)
                .setAvatarUrl("avatarUrl")
                .setAge(TimeUtil.getAgeByBirthday(birth))
                .setEnrollmentDate(new Date().getTime())
                .setWithdrawnDate(new Date().getTime())
                .setCompletedDate(new Date().getTime());

        LGSnapshot.StudentRecordSnapshot lastRecord = LGSnapshot.StudentRecordSnapshot.newBuilder()
                .setRecordId("test000")
                .setCreatedAt(new Date().getTime())
                .setUpdatedAt(new Date().getTime())
                .build();
        LGSnapshot.StudentRecordSnapshot nextRecord = LGSnapshot.StudentRecordSnapshot.newBuilder()
                .setRecordId("test002")
                .setCreatedAt(new Date().getTime())
                .setUpdatedAt(new Date().getTime())
                .build();
        //学生的属性
        List<LGSnapshot.Properties> properties = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            LGSnapshot.Properties property = LGSnapshot.Properties.newBuilder()
                    .setName("attrName" + i)
                    .setValue("values" + i)
                    .setType("boolean")
                    .build();
            properties.add(property);
        }
        if (prepareSsidAttr) {
            LGSnapshot.Properties ssidProperty = LGSnapshot.Properties.newBuilder().setName("Statewide Student Identifier").setValue("1234567890").setType("text").build();
            properties.add(ssidProperty);
        }
        //学生的当前周期
        List<LGSnapshot.Score> scores = new ArrayList<>();
        for (int i = 0; i < 30; i++) {
            LGSnapshot.Score score = LGSnapshot.Score.newBuilder()
                    .setDomainId("domainId" + i)
                    .setDomainAbbr("abbr" + i)
                    .setDomainName("domainName" + i)
                    .setScoreId("scoreId" + i)
                    .setScore(String.valueOf(GenerateCodeUtil.SECURE_RANDOM.nextDouble() * 10))
                    .setCore(true)
                    .build();
            scores.add(score);
        }
        LGSnapshot.RatingRecords ratingRecords = LGSnapshot.RatingRecords.newBuilder()
                .setPeriodAlias("2016 - 2017 Fall")
                .setFrom(TimeUtil.parseDate("2016-01-03").getTime())
                .setTo(TimeUtil.parseDate("2016-11-03").getTime())
                .setFramework("FrameWorkName")
                .setFrameworkId("FrameWorkId")
                .addAllScores(scores)
                .build();

        //agency
        LGSnapshot.AgencySnapshot agency = LGSnapshot.AgencySnapshot.newBuilder()
                .setId("agencyId0")
                .setName("agencyName")
                .setCreatedAt(TimeUtil.getNow().getTime())
                .setUpdatedAt(TimeUtil.getNow().getTime())
                .build();
        //学校
        LGSnapshot.CenterSnapshot center = LGSnapshot.CenterSnapshot.newBuilder()
                .setId("centerId")
                .setName("centerName")
                .setCreatedAt(TimeUtil.getNow().getTime())
                .setUpdatedAt(TimeUtil.getNow().getTime())
                .setAgency(agency)
                .build();
        //班级
        LGSnapshot.ClassSnapshot group = LGSnapshot.ClassSnapshot.newBuilder()
                .setId("groupId")
                .setName("groupName")
                .setCreatedAt(TimeUtil.getNow().getTime())
                .setUpdatedAt(TimeUtil.getNow().getTime())
                .setCenter(center)
                .build();

        studentSnapshotBuilder.setRatingRecords(ratingRecords)
                .addAllProperties(properties)
                .setGroup(group);
        studentSnapshotBuilder.setLastSnapshot(lastRecord);
        studentSnapshotBuilder.setNextSnapshot(nextRecord);

        LGSnapshot.StudentSnapshot studentSnapshot = studentSnapshotBuilder.build();
        snapshotEntity.setData(studentSnapshot.toByteArray());
        snapshotEntity.setMasterId("masterId");
        snapshotEntity.setId("snapshotId");
        snapshotEntity.setAgencyId("agencyId");
        snapshotEntity.setActive(true);
        snapshotEntity.setCreateAtUtc(TimeUtil.getNow());
        snapshotEntity.setDeleted(false);
        snapshotEntity.setEnrollmentId("test001");
        snapshotEntity.setPeriodAlias("2023-2024 Fall");
        snapshotEntity.setFromAtLocal(TimeUtil.parseDate("2024-01-03"));
        snapshotEntity.setToAtLocal(TimeUtil.parseDate("2024-05-03"));
        snapshotEntity.setLockAtLocal(TimeUtil.getNow());
        snapshotEntity.setLockAtUtc(TimeUtil.getNow());
        snapshotEntity.setUpdateByUserId("userId0");
        return snapshotEntity;
    }
}