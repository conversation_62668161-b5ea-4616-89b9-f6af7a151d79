package com.learninggenie.common.utils.openai;

import com.learninggenie.common.data.entity.prompt.PromptEntity;
import com.learninggenie.common.data.model.chat.ChatbotRecord;
import com.learninggenie.common.data.model.openai.completion.chat.ChatCompletionRequest;
import com.learninggenie.common.override.ChainHashMap;
import com.theokanning.openai.completion.chat.ChatMessage;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;

public class PromptUtilTest {

    @Test
    public void testClearPromptValue() {
        // 内容为空
        Assertions.assertEquals("", PromptUtil.clearHtmlTag(null));
        Assertions.assertEquals("", PromptUtil.clearHtmlTag(""));
        Assertions.assertEquals("", PromptUtil.clearHtmlTag(" "));
        // 正常内容
        Assertions.assertEquals("abc", PromptUtil.clearHtmlTag("abc"));
        Assertions.assertEquals("abc", PromptUtil.clearHtmlTag(" abc "));
        Assertions.assertEquals("abc", PromptUtil.clearHtmlTag("abc\n"));
        Assertions.assertEquals("abc", PromptUtil.clearHtmlTag("abc\n\n"));
        Assertions.assertEquals("a b  c", PromptUtil.clearHtmlTag(" a b  c\n\n"));
        Assertions.assertEquals("a b \nc", PromptUtil.clearHtmlTag(" a b \nc\n"));
        Assertions.assertEquals("a \n\nb \nc", PromptUtil.clearHtmlTag(" a \n\nb \nc\n"));
        // HTML 内容
        Assertions.assertEquals("abc", PromptUtil.clearHtmlTag("<p>abc</p>"));
        Assertions.assertEquals("abc\n\ndef", PromptUtil.clearHtmlTag("<p>abc</p>\n<p>def</p>"));
        Assertions.assertEquals("abc\ndef\nghi", PromptUtil.clearHtmlTag("<div><p>abc</p><p>def</p><p>ghi</p></div>"));
        // 错误的 HTML 标签
        Assertions.assertEquals("abc\ndef\nghi", PromptUtil.clearHtmlTag("<p>abc</p><p>def</p><p>ghi"));
        // 换行
        Assertions.assertEquals("abc\nde\nf\nghi", PromptUtil.clearHtmlTag("<p>abc</p><p>de<br>f</p><p>ghi"));
        Assertions.assertEquals("abc\nde\nf\nghi", PromptUtil.clearHtmlTag("<p>abc</p><p>de<br/>f</p><p>ghi"));
    }

    @Test
    public void testCreateChatMessages() {
        String message = "abc";
        // 调用方法
        List<ChatMessage> chatMessages = PromptUtil.createChatMessages(message);
        // 断言
        Assertions.assertEquals(1, chatMessages.size());
        // 验证内容
        ChatMessage chatMessage = chatMessages.get(0);
        Assertions.assertEquals("user", chatMessage.getRole());
        Assertions.assertEquals("abc", chatMessage.getContent());
    }

    @Test
    public void testCreateChatCompletionRequest() {
        String prompt = "abc";
        PromptEntity promptEntity = new PromptEntity();
        promptEntity.setModel("model");
        promptEntity.setTemperature(0.1);
        // 调用方法
        ChatCompletionRequest request = PromptUtil.createChatCompletionRequest(prompt, promptEntity);
        // 断言
        Assertions.assertEquals("model", request.getModel());
        // 验证内容
        Assertions.assertEquals("abc", request.getMessages().get(0).getContent());
        Assertions.assertEquals("user", request.getMessages().get(0).getRole());
        Assertions.assertEquals(0.1, request.getTemperature());

    }

    @Test
    public void testCreateChatMessages2() {
        List<ChatbotRecord> message = new ArrayList<>();
        ChatbotRecord chatbotRecord = new ChatbotRecord();
        chatbotRecord.setRole("user");
        chatbotRecord.setMessage("abc");
        message.add(chatbotRecord);
        ChatbotRecord chatbotRecord2 = new ChatbotRecord();
        chatbotRecord2.setRole("system");
        chatbotRecord2.setMessage("def");
        message.add(chatbotRecord2);
        String userPrompt = "abc";
        // 调用方法
        List<ChatMessage> chatMessages = PromptUtil.createChatbotMessages(message, userPrompt);
        // 断言
        Assertions.assertEquals(3, chatMessages.size());
        // 验证内容
        Assertions.assertEquals("abc", chatMessages.get(0).getContent());
        Assertions.assertEquals("user", chatMessages.get(0).getRole());
        Assertions.assertEquals("def", chatMessages.get(1).getContent());
        Assertions.assertEquals("assistant", chatMessages.get(1).getRole());
        Assertions.assertEquals("abc", chatMessages.get(2).getContent());
    }

    @Test
    public void testCreateChatCompletionRequest2() {
        List<ChatbotRecord> message = new ArrayList<>();
        ChatbotRecord chatbotRecord = new ChatbotRecord();
        chatbotRecord.setRole("user");
        chatbotRecord.setMessage("abc");
        message.add(chatbotRecord);
        ChatbotRecord chatbotRecord2 = new ChatbotRecord();
        chatbotRecord2.setRole("system");
        chatbotRecord2.setMessage("def");
        message.add(chatbotRecord2);
        String userPrompt = "abc";
        PromptEntity promptEntity = new PromptEntity();
        promptEntity.setModel("model");
        promptEntity.setTemperature(0.1);
        // 调用方法
        ChatCompletionRequest request = PromptUtil.createChatCompletionRequest(message, promptEntity, userPrompt);
        // 断言
        Assertions.assertEquals("model", request.getModel());
        // 验证内容
        Assertions.assertEquals("abc", request.getMessages().get(0).getContent());
        Assertions.assertEquals("user", request.getMessages().get(0).getRole());
        Assertions.assertEquals("def", request.getMessages().get(1).getContent());
        Assertions.assertEquals("assistant", request.getMessages().get(1).getRole());
    }

    @Test
    public void testReplacePromptTag() {
        String tagName = "TAG";
        String promptTemplate = "a <TAG>B</TAG>";
        // 参数为空的情况
        String prompt = PromptUtil.replacePromptTag(promptTemplate, null, false);
        Assertions.assertEquals(promptTemplate, prompt); // Prompt 不变

        // 移除的标签的情况
        prompt = PromptUtil.replacePromptTag(promptTemplate, tagName, true);
        Assertions.assertEquals("a ", prompt);

        // 不溢出标签的情况
        prompt = PromptUtil.replacePromptTag(promptTemplate, tagName, false);
        Assertions.assertEquals("a B", prompt);
    }

    /**
     * 测试 replaceAgePromptTag 方法，包含所有分支
     */
    @Test
    public void testReplaceAgePromptTag() {
        // 测试数据准备
        String prompt1 = "Please include the Lesson Title (same as the topic), Age Group [TK (4-5)], Preparation time, Activity Duration (<AGE@[PS/PK (3-4),TK (4-5)]@>Best Practice: 20-30 minutes</AGE@[PS/PK (3-4),TK (4-5)]@><AGE@[K (5-6),Grade (1-2)]@>Best Practice Range: 25-35 minutes</AGE@[K (5-6),Grade (1-2)]@><AGE@[Grade (3-4)]@>Best Practice Range: 30-45 minutes</AGE@[Grade (3-4)]@><AGE@[Grade (5-12)]@>Best Practice Range: 40-60 minutes</AGE@[Grade (5-12)]@>), Measures (Abbreviations only), Objectives (very brief goal for children's learning and development in one paragraph), Materials, Key Vocabulary Words with child-friendly definitions, and detailed implementation steps. ";
        String prompt2 = "Age Group [PS/PK (3-4)] Each activity usually includes 4-6 measures, and the measures in each activity can not be repeated too many times. Feel free to adjust it based on the activity.\n" +
                "</AGE@[PS/PK (3-4),TK (4-5),K (5-6)]@>\n" +
                "<AGE@[Grade (1-12)]@>";
        String promptEmpty = "Please include the Lesson Title (same as the topic), Age Group [TK (4-5)], Preparation time, Activity Duration (Best Practice: 20-30 minutes), Measures (Abbreviations only), Objectives (very brief goal for children's learning and development in one paragraph), Materials, Key Vocabulary Words with child-friendly definitions, and detailed implementation steps. ";
        String emptyPrompt = "";
        String nullPrompt = null;
        String emptyGrade = "";
        String nullGrade = null;
        String grade1 = "PS/PK (3-4)";
        String grade4 = "TK (4-5)";
        String grade6 = "K (5-6)";

        // 测试空的 prompt 或空的 grade
        assertEquals(emptyPrompt, PromptUtil.replaceAgePromptTag(emptyPrompt, grade1));
        assertEquals(nullPrompt, PromptUtil.replaceAgePromptTag(nullPrompt, grade1));
        assertEquals(promptEmpty, PromptUtil.replaceAgePromptTag(prompt1, emptyGrade));
        assertEquals(promptEmpty, PromptUtil.replaceAgePromptTag(prompt1, nullGrade));

        // 测试提取到的年级列表中不包含指定年级的情况
        String result = PromptUtil.replaceAgePromptTag(prompt1, grade6);
        assertTrue(result.contains(grade4));
        assertFalse(result.contains("[grade:4,5]"));

        // 测试替换年级标签
        String expectedPrompt1 = "Please include the Lesson Title (same as the topic), Age Group [TK (4-5)], Preparation time, Activity Duration (Best Practice: 20-30 minutes), Measures (Abbreviations only), Objectives (very brief goal for children's learning and development in one paragraph), Materials, Key Vocabulary Words with child-friendly definitions, and detailed implementation steps. ";
        assertEquals(expectedPrompt1, PromptUtil.replaceAgePromptTag(prompt1, grade1));

        String expectedPrompt2 = "Age Group [PS/PK (3-4)] Each activity usually includes 4-6 measures, and the measures in each activity can not be repeated too many times. Feel free to adjust it based on the activity.\n" +
                "</AGE@[PS/PK (3-4),TK (4-5),K (5-6)]@>\n";
        assertEquals(expectedPrompt2, PromptUtil.replaceAgePromptTag(prompt2, grade4));
    }

    @Test
    public void testFormatPrompt() {
        // Prompt 模板
        String template = "{{ child_name }}\n" +
                "{% if grade == 'Grade 1' %}\n" +
                " Is Grade 1\n" +
                "{% endif %}\n" +
                "{% if grade == 'Grade 2' %} Is Grade 2 \n{% elif grade == 'Grade 3' %} Is Grade 3 \n{% else %} Is Other Grade \n{% endif %}\n" +
                "{% if is_iep %} Is IEP \n{% else %} Not IEP \n{% endif %}\n" +
                "{% if 'Grade 1' in grades %} Contains Grade 1 \n{% else %} Not Contains Grade 1 \n{% endif %}\n";
        // 模板数据
        Map<String, Object> context = new ChainHashMap<String, Object>()
                .chainPut("child_name", "Tom")
                .chainPut("grade", "Grade 1")
                .chainPut("is_iep", true)
                .chainPut("grades", "Grade 1; Grade 2; Grade 3");
        // 格式 Prompt
        String prompt = PromptUtil.formatPrompt(template, context);
        // 断言
        Assertions.assertEquals("Tom\n" +
                " Is Grade 1\n" +
                " Is Other Grade \n" +
                " Is IEP \n" +
                " Contains Grade 1 \n", prompt);
        // 模板数据
        context = new ChainHashMap<String, Object>()
                .chainPut("child_name", "Tom 2")
                .chainPut("grade", "Grade 2")
                .chainPut("is_iep", false)
                .chainPut("grades", "Grade 2; Grade 3");
        // 格式 Prompt
        prompt = PromptUtil.formatPrompt(template, context);
        // 断言
        Assertions.assertEquals("Tom 2\n" +
                " Is Grade 2 \n" +
                " Not IEP \n" +
                " Not Contains Grade 1 \n", prompt);
    }
}
