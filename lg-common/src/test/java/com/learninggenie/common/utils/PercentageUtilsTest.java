package com.learninggenie.common.utils;


import org.junit.Test;
import org.junit.jupiter.api.Assertions;



/**
 * 百分比计算工具类
 *
 */

public class PercentageUtilsTest {


    /**
     * 测试当除数为零时，返回值应为 0
     *
     */
    @Test
    public void testGetPercentageDivideByZero() {
        Assertions.assertEquals(0, PercentageUtils.getPercentage(10, 0)); // 计算 10 / 0 预期结果是 0
    }

    /**
     * 测试一些正常情况下的计算结果
     *
     */
    @Test
    public void testGetPercentageNormalCase() {
        Assertions.assertEquals(50, PercentageUtils.getPercentage(50, 100)); // 计算 50 / 100 预期结果 50
        Assertions.assertEquals(25, PercentageUtils.getPercentage(25, 100)); // 计算 25 / 100 预期结果 25
        Assertions.assertEquals(75, PercentageUtils.getPercentage(75, 100)); // 计算 75 / 100 预期结果 75
    }

    /**
     * 测试一些需要四舍五入的情况
     *
     */
    @Test
    public void testGetPercentageRounding() {
        Assertions.assertEquals(33, PercentageUtils.getPercentage(1, 3)); // 计算 1 / 3 预期结果 33
        Assertions.assertEquals(67, PercentageUtils.getPercentage(2, 3)); // 计算 2 / 3 预期结果 67
    }

    /**
     * 测试极端情况，即当结果应调整为 99% 或 1% 时
     *
     */
    @Test
    public void testGetPercentageEdgeCases() {
        Assertions.assertEquals(99, PercentageUtils.getPercentage(299, 300)); // 计算 299 / 300 预期结果 99
        Assertions.assertEquals(1, PercentageUtils.getPercentage(1, 300)); // 计算 1 / 300 预期结果 1
    }

    /**
     * 测试当被除数为零时，返回值应为 0
     *
     */
    @Test
    public void testGetPercentageZeroNumerator() {
        Assertions.assertEquals(0, PercentageUtils.getPercentage(0, 100)); // 计算 0 / 100 预期结果 0
    }

    /**
     * 测试当被除数和除数相等时，返回值应为 100%
     *
     */
    @Test
    public void testGetPercentageFullPercentage() {
        Assertions.assertEquals(100, PercentageUtils.getPercentage(100, 100)); // 计算 100 / 100 预期结果 100
    }

}
