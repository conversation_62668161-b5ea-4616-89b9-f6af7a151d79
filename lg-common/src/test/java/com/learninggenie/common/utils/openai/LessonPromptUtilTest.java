package com.learninggenie.common.utils.openai;

import com.learninggenie.common.data.entity.lesson2.curriculum.CurriculumUnitEntity;
import com.learninggenie.common.data.entity.lesson2.plan.ItemEntity;
import com.learninggenie.common.data.entity.lesson2.plan.PlanEntity;
import com.learninggenie.common.data.enums.lesson2.ClassroomType;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.omg.CORBA.PUBLIC_MEMBER;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class LessonPromptUtilTest {

    @Test
    public void testCreateUnitOverviewPrompt() {
        String template = "{{ total_number_of_week }}{{ unit_name }}{{ city }}{{ state }}{{ unit_description }}{{ grade }}";
        CurriculumUnitEntity unit = new CurriculumUnitEntity();
        unit.setWeekCount(1);
        unit.setTitle("Unit Title");
        unit.setGrade("Grade");
        unit.setCity("City");
        unit.setState("State");
        unit.setDescription("Unit Description");
        String frameworkInfo = "Framework Info";
        // 调用方法
        String unitOverviewPrompt = LessonPromptUtil.createUnitOverviewPrompt(template, unit, frameworkInfo);
        // 验证结果
        Assertions.assertNotNull(unitOverviewPrompt);
        // 验证数据
        Assertions.assertTrue(unitOverviewPrompt.contains(unit.getTitle()));
        Assertions.assertTrue(unitOverviewPrompt.contains(unit.getGrade()));
    }

    /**
     * 测试创建单元概览提示
     * case: 年龄段为 4-5 年龄段
     */
    @Test
    public void testCreateUnitOverviewPrompt1() {
        String template = "{{ total_number_of_week }}{{ unit_name }}{{ city }}{{ state }}{{ unit_description }}{{ grade }}";
        CurriculumUnitEntity unit = new CurriculumUnitEntity();
        unit.setWeekCount(1);
        unit.setTitle("Unit Title");
        unit.setGrade("TK (4-5)");
        unit.setCity("City");
        unit.setState("State");
        unit.setDescription("Unit Description");
        String frameworkInfo = "Framework Info";
        // 调用方法
        String unitOverviewPrompt = LessonPromptUtil.createUnitOverviewPrompt(template, unit, frameworkInfo);
        // 验证结果
        Assertions.assertNotNull(unitOverviewPrompt);
        // 验证数据
        Assertions.assertTrue(unitOverviewPrompt.contains(unit.getTitle()));
        Assertions.assertTrue(unitOverviewPrompt.contains(unit.getGrade()));
    }

    /**
     * 测试创建单元概览提示
     * case: 年龄段为 6-11 年龄段
     */
    @Test
    public void testCreateUnitOverviewPrompt2() {
        String template = "{{ total_number_of_week }}{{ unit_name }}{{ city }}{{ state }}{{ unit_description }}{{ grade }}";
        CurriculumUnitEntity unit = new CurriculumUnitEntity();
        unit.setWeekCount(1);
        unit.setTitle("Unit Title");
        unit.setGrade("Grade 4");
        unit.setCity("City");
        unit.setState("State");
        unit.setDescription("Unit Description");
        String frameworkInfo = "Framework Info";
        // 调用方法
        String unitOverviewPrompt = LessonPromptUtil.createUnitOverviewPrompt(template, unit, frameworkInfo);
        // 验证结果
        Assertions.assertNotNull(unitOverviewPrompt);
        // 验证数据
        Assertions.assertTrue(unitOverviewPrompt.contains(unit.getTitle()));
        Assertions.assertTrue(unitOverviewPrompt.contains(unit.getGrade()));
    }

    /**
     * 测试创建单元概览提示
     * case: 年龄段为 12-14 年龄段
     */
    @Test
    public void testCreateUnitOverviewPrompt3() {
        String template = "{{ total_number_of_week }}{{ unit_name }}{{ city }}{{ state }}{{ unit_description }}{{ grade }}";
        CurriculumUnitEntity unit = new CurriculumUnitEntity();
        unit.setWeekCount(1);
        unit.setTitle("Unit Title");
        unit.setGrade("Grade 6");
        unit.setCity("City");
        unit.setState("State");
        unit.setDescription("Unit Description");
        String frameworkInfo = "Framework Info";
        // 调用方法
        String unitOverviewPrompt = LessonPromptUtil.createUnitOverviewPrompt(template, unit, frameworkInfo);
        // 验证结果
        Assertions.assertNotNull(unitOverviewPrompt);
        // 验证数据
        Assertions.assertTrue(unitOverviewPrompt.contains(unit.getTitle()));
        Assertions.assertTrue(unitOverviewPrompt.contains(unit.getGrade()));
    }

    /**
     * 测试创建单元概览提示
     * case: 年龄段为 15 以上
     */
    @Test
    public void testCreateUnitOverviewPrompt4() {
        String template = "{{ total_number_of_week }}{{ unit_name }}{{ city }}{{ state }}{{ unit_description }}{{ grade }}";
        CurriculumUnitEntity unit = new CurriculumUnitEntity();
        unit.setWeekCount(1);
        unit.setTitle("Unit Title");
        unit.setGrade("Grade 9");
        unit.setCity("City");
        unit.setState("State");
        unit.setDescription("Unit Description");
        String frameworkInfo = "Framework Info";
        // 调用方法
        String unitOverviewPrompt = LessonPromptUtil.createUnitOverviewPrompt(template, unit, frameworkInfo);
        // 验证结果
        Assertions.assertNotNull(unitOverviewPrompt);
        // 验证数据
        Assertions.assertTrue(unitOverviewPrompt.contains(unit.getTitle()));
        Assertions.assertTrue(unitOverviewPrompt.contains(unit.getGrade()));
    }

    @Test
    public void testCreateEvaluateUnitOverviewPrompt() {
        String template = "{{ unit name }} {{ city }} {{ state }} {{ age group }} {{ generated unit content }} ";
        CurriculumUnitEntity unit = new CurriculumUnitEntity();
        unit.setTitle("Unit Title");
        unit.setState("State");
        unit.setGrade("Grade");
        unit.setCity("City");
        String generatedContent = "Generated Content";
        // 调用方法
        String unitOverviewPrompt = LessonPromptUtil.createEvaluateUnitOverviewPrompt(template, unit, generatedContent);
        // 验证结果
        Assertions.assertNotNull(unitOverviewPrompt);
        // 验证数据
        Assertions.assertTrue(unitOverviewPrompt.contains(unit.getTitle()));
        Assertions.assertEquals(unitOverviewPrompt, "Unit Title City State Grade Generated Content ");
    }

    @Test
    public void testCreatePlanOverviewPrompt() {
        String template = "{{ total number of week }},{{ unit name }},{{ city }},{{ state }},{{ age group }},{{ overview }}" +
                "{{ unit trajectory }},{{ concepts }},{{ unit description }},{{ guiding questions }}";
        CurriculumUnitEntity unit = new CurriculumUnitEntity();
        unit.setWeekCount(1);
        unit.setTitle("Unit Title");
        unit.setGrade("K (5-6)");
        unit.setCity("City");
        unit.setState("State");
        unit.setDescription("Unit Description");
        unit.setOverview("Unit Overview");
        unit.setTrajectory("Unit Trajectory");
        unit.setConcepts("Unit Concepts");
        unit.setGuidingQuestions("Unit Guiding Questions");
        // 调用方法
        String planOverviewPrompt = LessonPromptUtil.createPlanOverviewPrompt(template, unit);
        // 验证结果
        Assertions.assertNotNull(planOverviewPrompt);
        // 验证数据
        Assertions.assertTrue(planOverviewPrompt.contains(unit.getTitle()));
        Assertions.assertTrue(planOverviewPrompt.contains(unit.getGrade()));
        Assertions.assertEquals(planOverviewPrompt, "1,Unit Title,City,State,K (5-6),Unit OverviewUnit Trajectory,Unit Concepts,Unit Description,Unit Guiding Questions");
    }

    /**
     * 测试创建计划概览
     * case: 年龄段为 15 以上
     */
    @Test
    public void testCreatePlanOverviewPrompt1() {
        String template = "{{ total number of week }},{{ unit name }},{{ city }},{{ state }},{{ age group }},{{ overview }}" +
                "{{ unit trajectory }},{{ concepts }},{{ unit description }},{{ guiding questions }}";
        CurriculumUnitEntity unit = new CurriculumUnitEntity();
        unit.setWeekCount(1);
        unit.setTitle("Unit Title");
        unit.setGrade("Grade 10");
        unit.setCity("City");
        unit.setState("State");
        unit.setDescription("Unit Description");
        unit.setOverview("Unit Overview");
        unit.setTrajectory("Unit Trajectory");
        unit.setConcepts("Unit Concepts");
        unit.setGuidingQuestions("Unit Guiding Questions");
        // 调用方法
        String planOverviewPrompt = LessonPromptUtil.createPlanOverviewPrompt(template, unit);
        // 验证结果
        Assertions.assertNotNull(planOverviewPrompt);
        // 验证数据
        Assertions.assertTrue(planOverviewPrompt.contains(unit.getTitle()));
        Assertions.assertTrue(planOverviewPrompt.contains(unit.getGrade()));
        Assertions.assertEquals(planOverviewPrompt, "1,Unit Title,City,State,Grade 10,Unit OverviewUnit Trajectory,Unit Concepts,Unit Description,Unit Guiding Questions");
    }

    @Test
    @Ignore
    public void testCreateLessonOverviewPrompt() {
        String template = "{{ week number }} {{ unit name }} {{ city }} {{ state }} {{ age group }} {{ weekly theme }} {{ weekly overview }} {{ assessment framework }}";
        CurriculumUnitEntity unit = new CurriculumUnitEntity();
        unit.setWeekCount(1);
        unit.setTitle("Unit Title");
        unit.setGrade("Grade");
        unit.setCity("City");
        unit.setState("State");
        unit.setDescription("Unit Description");
        unit.setOverview("Unit Overview");
        unit.setTrajectory("Unit Trajectory");
        unit.setConcepts("Unit Concepts");
        unit.setGuidingQuestions("Unit Guiding Questions");
        PlanEntity plan = new PlanEntity();
        plan.setTheme("Plan Theme");
        plan.setOverview("Plan Description");
        String frameworkInfo = "Framework Info";
        // 调用方法
        String lessonOverviewPrompt = LessonPromptUtil.createLessonOverviewPrompt(template, unit, plan, frameworkInfo, false, null);
        // 验证结果
        Assertions.assertNotNull(lessonOverviewPrompt);
        // 验证数据
        Assertions.assertTrue(lessonOverviewPrompt.contains(unit.getTitle()));
        Assertions.assertTrue(lessonOverviewPrompt.contains(unit.getGrade()));
        Assertions.assertEquals( lessonOverviewPrompt,"1 Unit Title City City Grade Plan Theme Plan Description Framework Info");
    }

    /**
     * 测试创建课程概览提示
     * case: 年龄段为小于 15
     */
    @Test
    public void testCreateLessonOverviewPrompt1() {
        String template = "{{ week number }} {{ unit name }} {{ city }} {{ state }} {{ age group }} {{ weekly theme }} {{ weekly overview }} {{ assessment framework }}";
        CurriculumUnitEntity unit = new CurriculumUnitEntity();
        unit.setWeekCount(1);
        unit.setTitle("Unit Title");
        unit.setGrade("Grade 2");
        unit.setCity("City");
        unit.setState("State");
        unit.setDescription("Unit Description");
        unit.setOverview("Unit Overview");
        unit.setTrajectory("Unit Trajectory");
        unit.setConcepts("Unit Concepts");
        unit.setGuidingQuestions("Unit Guiding Questions");
        PlanEntity plan = new PlanEntity();
        plan.setTheme("Plan Theme");
        plan.setOverview("Plan Description");
        plan.setWeek(1);
        String frameworkInfo = "Framework Info";
        // 调用方法
        String lessonOverviewPrompt = LessonPromptUtil.createLessonOverviewPrompt(template, unit, plan, frameworkInfo, false, null);
        // 验证结果
        Assertions.assertNotNull(lessonOverviewPrompt);
        // 验证数据
        Assertions.assertTrue(lessonOverviewPrompt.contains(unit.getTitle()));
        Assertions.assertTrue(lessonOverviewPrompt.contains(unit.getGrade()));
        Assertions.assertEquals( lessonOverviewPrompt,"1 Unit Title City State Grade 2 Plan Theme Plan Description Framework Info");
    }

    /**
     * 测试创建课程概览提示
     * case: 年龄段为 15 以上
     */
    @Test
    public void testCreateLessonOverviewPrompt2() {
        String template = "{{ week number }} {{ unit name }} {{ city }} {{ state }} {{ age group }} {{ weekly theme }} {{ weekly overview }} {{ assessment framework }}";
        CurriculumUnitEntity unit = new CurriculumUnitEntity();
        unit.setWeekCount(1);
        unit.setTitle("Unit Title");
        unit.setGrade("Grade 10");
        unit.setCity("City");
        unit.setState("State");
        unit.setDescription("Unit Description");
        unit.setOverview("Unit Overview");
        unit.setTrajectory("Unit Trajectory");
        unit.setConcepts("Unit Concepts");
        unit.setGuidingQuestions("Unit Guiding Questions");
        PlanEntity plan = new PlanEntity();
        plan.setTheme("Plan Theme");
        plan.setOverview("Plan Description");
        plan.setWeek(1);
        String frameworkInfo = "Framework Info";
        // 调用方法
        String lessonOverviewPrompt = LessonPromptUtil.createLessonOverviewPrompt(template, unit, plan, frameworkInfo, false, null);
        // 验证结果
        Assertions.assertNotNull(lessonOverviewPrompt);
        // 验证数据
        Assertions.assertTrue(lessonOverviewPrompt.contains(unit.getTitle()));
        Assertions.assertTrue(lessonOverviewPrompt.contains(unit.getGrade()));
        Assertions.assertEquals( lessonOverviewPrompt,"1 Unit Title City State Grade 10 Plan Theme Plan Description Framework Info");
    }

    /**
     * 测试创建课程概览提示
     * case: 年龄段为 4-5 年龄段
     */
    @Test
    public void testCreateLessonOverviewPrompt3() {
        String template = "{{ week number }} {{ unit name }} {{ city }} {{ state }} {{ age group }} {{ weekly theme }} {{ weekly overview }} {{ assessment framework }}";
        CurriculumUnitEntity unit = new CurriculumUnitEntity();
        unit.setWeekCount(1);
        unit.setTitle("Unit Title");
        unit.setGrade("TK (4-5)");
        unit.setCity("City");
        unit.setState("State");
        unit.setDescription("Unit Description");
        unit.setOverview("Unit Overview");
        unit.setTrajectory("Unit Trajectory");
        unit.setConcepts("Unit Concepts");
        unit.setGuidingQuestions("Unit Guiding Questions");
        PlanEntity plan = new PlanEntity();
        plan.setTheme("Plan Theme");
        plan.setOverview("Plan Description");
        plan.setWeek(1);
        String frameworkInfo = "Framework Info";
        // 调用方法
        String lessonOverviewPrompt = LessonPromptUtil.createLessonOverviewPrompt(template, unit, plan, frameworkInfo, false, null);
        // 验证结果
        Assertions.assertNotNull(lessonOverviewPrompt);
        // 验证数据
        Assertions.assertTrue(lessonOverviewPrompt.contains(unit.getTitle()));
        Assertions.assertTrue(lessonOverviewPrompt.contains(unit.getGrade()));
        Assertions.assertEquals( lessonOverviewPrompt,"1 Unit Title City State TK (4-5) Plan Theme Plan Description Framework Info");
    }


    /**
     * 测试创建课程概览提示
     * case: 年龄段为 6-8 年龄段
     */
    @Test
    public void testCreateLessonOverviewPrompt4() {
        String template = "{{ week number }} {{ unit name }} {{ city }} {{ state }} {{ age group }} {{ weekly theme }} {{ weekly overview }} {{ assessment framework }}";
        CurriculumUnitEntity unit = new CurriculumUnitEntity();
        unit.setWeekCount(1);
        unit.setTitle("Unit Title");
        unit.setGrade("Grade 1");
        unit.setCity("City");
        unit.setState("State");
        unit.setDescription("Unit Description");
        unit.setOverview("Unit Overview");
        unit.setTrajectory("Unit Trajectory");
        unit.setConcepts("Unit Concepts");
        unit.setGuidingQuestions("Unit Guiding Questions");
        PlanEntity plan = new PlanEntity();
        plan.setTheme("Plan Theme");
        plan.setOverview("Plan Description");
        plan.setWeek(1);
        String frameworkInfo = "Framework Info";
        // 调用方法
        String lessonOverviewPrompt = LessonPromptUtil.createLessonOverviewPrompt(template, unit, plan, frameworkInfo, false, null);
        // 验证结果
        Assertions.assertNotNull(lessonOverviewPrompt);
        // 验证数据
        Assertions.assertTrue(lessonOverviewPrompt.contains(unit.getTitle()));
        Assertions.assertTrue(lessonOverviewPrompt.contains(unit.getGrade()));
        Assertions.assertEquals( lessonOverviewPrompt,"1 Unit Title City State Grade 1 Plan Theme Plan Description Framework Info");
    }

    /**
     * 测试创建课程概览提示
     * case: 年龄段为 9-11 年龄段
     */
    @Test
    public void testCreateLessonOverviewPrompt5() {
        String template = "{{ week number }} {{ unit name }} {{ city }} {{ state }} {{ age group }} {{ weekly theme }} {{ weekly overview }} {{ assessment framework }}";
        CurriculumUnitEntity unit = new CurriculumUnitEntity();
        unit.setWeekCount(1);
        unit.setTitle("Unit Title");
        unit.setGrade("Grade 3");
        unit.setCity("City");
        unit.setState("State");
        unit.setDescription("Unit Description");
        unit.setOverview("Unit Overview");
        unit.setTrajectory("Unit Trajectory");
        unit.setConcepts("Unit Concepts");
        unit.setGuidingQuestions("Unit Guiding Questions");
        PlanEntity plan = new PlanEntity();
        plan.setTheme("Plan Theme");
        plan.setOverview("Plan Description");
        plan.setWeek(1);
        String frameworkInfo = "Framework Info";
        // 调用方法
        String lessonOverviewPrompt = LessonPromptUtil.createLessonOverviewPrompt(template, unit, plan, frameworkInfo, false, null);
        // 验证结果
        Assertions.assertNotNull(lessonOverviewPrompt);
        // 验证数据
        Assertions.assertTrue(lessonOverviewPrompt.contains(unit.getTitle()));
        Assertions.assertTrue(lessonOverviewPrompt.contains(unit.getGrade()));
        Assertions.assertEquals( lessonOverviewPrompt,"1 Unit Title City State Grade 3 Plan Theme Plan Description Framework Info");
    }

    /**
     * 测试创建课程概览提示
     * case: 年龄段为 12-14 年龄段
     */
    @Test
    public void testCreateLessonOverviewPrompt6() {
        String template = "{{ week number }} {{ unit name }} {{ city }} {{ state }} {{ age group }} {{ weekly theme }} {{ weekly overview }} {{ assessment framework }}";
        CurriculumUnitEntity unit = new CurriculumUnitEntity();
        unit.setWeekCount(1);
        unit.setTitle("Unit Title");
        unit.setGrade("Grade 7");
        unit.setCity("City");
        unit.setState("State");
        unit.setDescription("Unit Description");
        unit.setOverview("Unit Overview");
        unit.setTrajectory("Unit Trajectory");
        unit.setConcepts("Unit Concepts");
        unit.setGuidingQuestions("Unit Guiding Questions");
        PlanEntity plan = new PlanEntity();
        plan.setTheme("Plan Theme");
        plan.setOverview("Plan Description");
        plan.setWeek(1);
        String frameworkInfo = "Framework Info";
        // 调用方法
        String lessonOverviewPrompt = LessonPromptUtil.createLessonOverviewPrompt(template, unit, plan, frameworkInfo, false, null);
        // 验证结果
        Assertions.assertNotNull(lessonOverviewPrompt);
        // 验证数据
        Assertions.assertTrue(lessonOverviewPrompt.contains(unit.getTitle()));
        Assertions.assertTrue(lessonOverviewPrompt.contains(unit.getGrade()));
        Assertions.assertEquals( lessonOverviewPrompt,"1 Unit Title City State Grade 7 Plan Theme Plan Description Framework Info");
    }
    @Test
    public void testCreateLessonDetailPrompt() {
        String template = "{{ week number }} {{ name }} {{ city }} {{ state }} {{ age group }} {{ weekly theme }} {{ weekly overview }}" +
                "{{ lesson name }}  {{ lesson description }}  {{ activity type }} {{ measures }} {{ assessment framework }}";
        CurriculumUnitEntity unit = new CurriculumUnitEntity();
        unit.setWeekCount(1);
        unit.setTitle("Unit Title");
        unit.setGrade("Grade");
        unit.setCity("City");
        unit.setState("State");
        unit.setWeekCount(1);
        PlanEntity plan = new PlanEntity();
        plan.setTheme("Plan Theme");
        plan.setOverview("Plan Description");
        ItemEntity item = new ItemEntity();
        item.setName("Item Name");
        item.setDescription("Item Description");
        String activityType = "Activity Type";
        String frameworkInfo = "Framework Info";
        List<String> measures = new ArrayList<>();
        measures.add("Measure 1");
        measures.add("Measure 2");
        // 调用方法
        String lessonDetailPrompt = LessonPromptUtil.createLessonDetailPrompt(template, unit, plan, item, frameworkInfo, activityType, measures);
        // 验证结果
        Assertions.assertNotNull(lessonDetailPrompt);
        // 验证数据
        Assertions.assertTrue(lessonDetailPrompt.contains(unit.getTitle()));
        Assertions.assertTrue(lessonDetailPrompt.contains(unit.getGrade()));
        Assertions.assertEquals(lessonDetailPrompt, "1 Unit Title City State Grade Plan Theme Plan DescriptionItem Name  Item Description  Activity Type Measure 1, Measure 2 Framework Info");

        // 0-1 年龄段
        unit.setGrade("Infant (0-1)");
        // 调用方法
        lessonDetailPrompt = LessonPromptUtil.createLessonDetailPrompt(template, unit, plan, item, frameworkInfo, activityType, measures);
        // 验证结果
        Assertions.assertNotNull(lessonDetailPrompt);

        // 2-3 年龄段
        unit.setGrade("Toddler (1-3)");
        // 调用方法
        lessonDetailPrompt = LessonPromptUtil.createLessonDetailPrompt(template, unit, plan, item, frameworkInfo, activityType, measures);
        // 验证结果
        Assertions.assertNotNull(lessonDetailPrompt);

        // 4 年龄段
        unit.setGrade("PS/PK (3-4)");
        // 调用方法
        lessonDetailPrompt = LessonPromptUtil.createLessonDetailPrompt(template, unit, plan, item, frameworkInfo, activityType, measures);
        // 验证结果
        Assertions.assertNotNull(lessonDetailPrompt);

        // 5 年龄段
        unit.setGrade("TK (4-5)");
        // 调用方法
        lessonDetailPrompt = LessonPromptUtil.createLessonDetailPrompt(template, unit, plan, item, frameworkInfo, activityType, measures);
        // 验证结果
        Assertions.assertNotNull(lessonDetailPrompt);

    }

    @Test
    public void testCreateRedesignLessonPrompt() {
        String template = "{{ framework_base_info }} {{ grade }} {{ lesson_name }} {{ preparation_time }} {{ activity_duration }} {{ measures }} " +
                "{{ objectives }} {{ materials }} {{ key_vocabulary_words }} {{ implementation_steps }} {{ generate_measures }}";
        String ageGroup = "Age Group";
        String lessonName = "Lesson Name";
        String prepareTime = "Preparation Time";
        String activityTime = "Activity Time";
        String objectives = "Objectives";
        String materials = "Materials";
        String keyVocabularyWords = "Key Vocabulary Words";
        String implementationSteps = "Implementation Steps";
        String frameworkInfo = "Framework Info";
        List<String> measures = new ArrayList<>();
        measures.add("Measure 1");
        measures.add("Measure 2");
        List<String> newMeasures = new ArrayList<>();
        newMeasures.add("New Measure 1");
        newMeasures.add("New Measure 2");
        CurriculumUnitEntity unit = new CurriculumUnitEntity();
        // 调用方法
        String redesignLessonPrompt = LessonPromptUtil.createRedesignLessonPrompt(template, ageGroup, lessonName, prepareTime, activityTime, objectives, materials, keyVocabularyWords, implementationSteps, frameworkInfo, measures, newMeasures, unit, false);
        // 验证结果
        Assertions.assertNotNull(redesignLessonPrompt);
        // 验证数据
        Assertions.assertTrue(redesignLessonPrompt.contains(lessonName));
        Assertions.assertTrue(redesignLessonPrompt.contains(prepareTime));
        Assertions.assertEquals(redesignLessonPrompt, "Framework Info Age Group Lesson Name Preparation Time Activity Time Measure 1, Measure 2 Objectives Materials Key Vocabulary Words Implementation Steps New Measure 1, New Measure 2");

    }

    @Test
    public void testCreateEvaluateRedesignLessonPrompt() {
        String template = "{{ activity_type }} {{ week }} {{ unit_name }} {{ grade }} {{ weekly_plan_theme }} {{ weekly_plan_description }}" +
                "{{ lesson_name }} {{ preparation_time }} {{ activity_duration }}  {{ measures }} {{ objectives }}  {{ materials }}" +
                "{{ key_vocabulary_words }} {{ implementation_steps }}";
        String ageGroup = "Age Group";
        String lessonName = "Lesson Name";
        String prepareTime = "Preparation Time";
        String activityTime = "Activity Time";
        String objectives = "Objectives";
        String materials = "Materials";
        String keyVocabularyWords = "Key Vocabulary Words";
        String implementationSteps = "Implementation Steps";
        String activityType = "Activity Type";
        CurriculumUnitEntity unit = new CurriculumUnitEntity();
        unit.setTitle("Unit Title");
        PlanEntity plan = new PlanEntity();
        plan.setWeek(1);
        plan.setTheme("Plan Theme");
        plan.setOverview("Plan Description");
        List<String> measures = new ArrayList<>();
        measures.add("Measure 1");
        measures.add("Measure 2");
        // 调用方法
        String evaluateRedesignLessonPrompt = LessonPromptUtil.createEvaluateRedesignLessonPrompt(template, ageGroup, lessonName, prepareTime, activityTime, objectives, materials, keyVocabularyWords, implementationSteps, activityType, unit, plan, measures);
        // 验证结果
        Assertions.assertNotNull(evaluateRedesignLessonPrompt);
        // 验证数据
        Assertions.assertTrue(evaluateRedesignLessonPrompt.contains(lessonName));
        Assertions.assertTrue(evaluateRedesignLessonPrompt.contains(prepareTime));
        Assertions.assertEquals(evaluateRedesignLessonPrompt, "Activity Type 1 Unit Title Age Group Plan Theme Plan DescriptionLesson Name Preparation Time Activity Time  Measure 1, Measure 2 Objectives  MaterialsKey Vocabulary Words Implementation Steps");
    }

    @Test
    public void testCreateTypicalBehaviorsPrompt() {
        String template = "{{ assessment framework }} {{ age group }} {{ lesson name }} {{ preparation time }} {{ activity duration }}" +
                "{{ measures }} {{ objectives }} {{ materials }} {{ key vocabulary words }} {{ implementation steps }} {{ generate measures }} ";
        String ageGroup = "Age Group";
        String lessonName = "Lesson Name";
        String prepareTime = "Preparation Time";
        String activityTime = "Activity Time";
        String objectives = "Objectives";
        String materials = "Materials";
        String keyVocabularyWords = "Key Vocabulary Words";
        String implementationSteps = "Implementation Steps";
        String frameworkInfo = "Framework Info";
        List<String> measures = new ArrayList<>();
        measures.add("Measure 1");
        measures.add("Measure 2");
        // 调用方法
        String typicalBehaviorsPrompt = LessonPromptUtil.createTypicalBehaviorsPrompt(template, ageGroup, lessonName, prepareTime, activityTime, objectives, materials, keyVocabularyWords, implementationSteps, frameworkInfo, measures, false);
        // 验证结果
        Assertions.assertNotNull(typicalBehaviorsPrompt);
        // 验证数据
        Assertions.assertTrue(typicalBehaviorsPrompt.contains(lessonName));
        Assertions.assertTrue(typicalBehaviorsPrompt.contains(prepareTime));
        Assertions.assertEquals(typicalBehaviorsPrompt, "Framework Info Age Group Lesson Name Preparation Time Activity TimeMeasure 1, Measure 2 Objectives Materials Key Vocabulary Words Implementation Steps Measure 1, Measure 2 ");

        // 0-1 年龄段
        ageGroup = "Infant (0-1)";
        // 调用方法
        typicalBehaviorsPrompt = LessonPromptUtil.createTypicalBehaviorsPrompt(template, ageGroup, lessonName, prepareTime, activityTime, objectives, materials, keyVocabularyWords, implementationSteps, frameworkInfo, measures, false);        // 验证结果
        Assertions.assertNotNull(typicalBehaviorsPrompt);

        // 2-3 年龄段
        ageGroup = "Toddler (1-3)";
        // 调用方法
        typicalBehaviorsPrompt = LessonPromptUtil.createTypicalBehaviorsPrompt(template, ageGroup, lessonName, prepareTime, activityTime, objectives, materials, keyVocabularyWords, implementationSteps, frameworkInfo, measures, false);        // 验证结果
        Assertions.assertNotNull(typicalBehaviorsPrompt);

        // 4 年龄段
        ageGroup = "PS/PK (3-4)";
        // 调用方法
        typicalBehaviorsPrompt = LessonPromptUtil.createTypicalBehaviorsPrompt(template, ageGroup, lessonName, prepareTime, activityTime, objectives, materials, keyVocabularyWords, implementationSteps, frameworkInfo, measures, false);        // 验证结果
        Assertions.assertNotNull(typicalBehaviorsPrompt);

        // 5 年龄段
        ageGroup = "TK (4-5)";
        // 调用方法
        typicalBehaviorsPrompt = LessonPromptUtil.createTypicalBehaviorsPrompt(template, ageGroup, lessonName, prepareTime, activityTime, objectives, materials, keyVocabularyWords, implementationSteps, frameworkInfo, measures, false);        // 验证结果
        Assertions.assertNotNull(typicalBehaviorsPrompt);
    }

    @Test
    public void testCreateLessonSection() {
        String agrGroup = "Age Group";
        String lessonName = "Lesson Name";
        String prepareTime = "Preparation Time";
        String activityTime = "Activity Time";
        String objectives = "Objectives";
        String materials = "Materials";
        String keyVocabularyWords = "Key Vocabulary Words";
        String implementationSteps = "Implementation Steps";
        String activityType = "Activity Type";
        List<String> measures = new ArrayList<>();
        measures.add("Measure 1");
        measures.add("Measure 2");
        // 调用方法
        String lessonSection = LessonPromptUtil.createLessonSection(agrGroup, lessonName, prepareTime, activityTime, objectives, materials, keyVocabularyWords, implementationSteps, activityType, measures);
        // 验证结果
        Assertions.assertNotNull(lessonSection);
        // 验证数据
        Assertions.assertTrue(lessonSection.contains(lessonName));
        Assertions.assertTrue(lessonSection.contains(prepareTime));
        Assertions.assertEquals(lessonSection, "Lesson Name\n" +
                "Age Group: Age Group\n" +
                "Preparation Time: Preparation Time\n" +
                "Activity Type: Activity Type\n" +
                "Activity Duration: Activity Time\n" +
                "Measures: Measure 1, Measure 2\n" +
                "\n" +
                "Objectives:\n" +
                "Objectives\n" +
                "\n" +
                "Materials:\n" +
                "Materials\n" +
                "\n" +
                "Key Vocabulary Words:\n" +
                "Key Vocabulary Words\n" +
                "\n" +
                "Implementation Steps:\n" +
                "Implementation Steps\n");
    }

    @Test
    public void testCreateUniversalDesignForLearningPrompt() {
        String template = "{{ grade }} {{ lesson_name }} {{ preparation_time }} {{ activity_duration }} {{ objectives }}  {{ materials }}" +
                "{{ key_vocabulary_words }} {{ implementation_steps }} {{ framework_base_info }} {{ measures }}";
        String grade = "Grade";
        String lessonName = "Lesson Name";
        String prepareTime = "Preparation Time";
        String activityTime = "Activity Time";
        String objectives = "Objectives";
        String materials = "Materials";
        String keyVocabularyWords = "Key Vocabulary Words";
        String implementationSteps = "Implementation Steps";
        String frameworkInfo = "Framework Info";
        List<String> measures = new ArrayList<>();
        measures.add("Measure 1");
        measures.add("Measure 2");
        // 调用方法
        String universalDesignForLearningPrompt = LessonPromptUtil.createUniversalDesignForLearningPrompt(template, grade, lessonName, prepareTime, activityTime, objectives, materials, keyVocabularyWords, implementationSteps, frameworkInfo, measures, ClassroomType.IN_PERSON);
        // 验证结果
        Assertions.assertNotNull(universalDesignForLearningPrompt);
        // 验证数据
        Assertions.assertTrue(universalDesignForLearningPrompt.contains(lessonName));
        Assertions.assertTrue(universalDesignForLearningPrompt.contains(prepareTime));
        Assertions.assertEquals(universalDesignForLearningPrompt, "Grade Lesson Name Preparation Time Activity Time Objectives  MaterialsKey Vocabulary Words Implementation Steps Framework Info Measure 1, Measure 2");
    }

    @Test
    public void testCreateHomeActivityPrompt() {
        String template = "{{ grade }} {{ lesson_name }} {{ preparation_time }} {{ activity_duration }} {{ objectives }} " +
                "{{ materials }} {{ key_vocabulary_words }} {{ implementation_steps }} {{ framework_base_info }} {{ measures }}";
        String grade = "Grade";
        String lessonName = "Lesson Name";
        String prepareTime = "Preparation Time";
        String activityTime = "Activity Time";
        String objectives = "Objectives";
        String materials = "Materials";
        String keyVocabularyWords = "Key Vocabulary Words";
        String implementationSteps = "Implementation Steps";
        String frameworkInfo = "Framework Info";
        List<String> measures = new ArrayList<>();
        measures.add("Measure 1");
        measures.add("Measure 2");
        // 调用方法
        String homeActivityPrompt = LessonPromptUtil.createHomeActivityPrompt(template, grade, lessonName, prepareTime, activityTime, objectives, materials, keyVocabularyWords, implementationSteps, frameworkInfo, measures);
        // 验证结果
        Assertions.assertNotNull(homeActivityPrompt);
        // 验证数据
        Assertions.assertTrue(homeActivityPrompt.contains(lessonName));
        Assertions.assertTrue(homeActivityPrompt.contains(prepareTime));
        Assertions.assertEquals(homeActivityPrompt, "Grade Lesson Name Preparation Time Activity Time Objectives Materials Key Vocabulary Words Implementation Steps Framework Info Measure 1, Measure 2");
    }

    @Test
    public void testCreateCulturallyResponsiveInstructionPrompt() {
        String template = "{{ grade }} {{ lesson_name }} {{ preparation_time }} {{ activity_duration }} {{ objectives }} " +
                "{{ materials }} {{ key_vocabulary_words }} {{ implementation_steps }} {{ framework_base_info }} {{ measures }}";
        String grade = "Grade";
        String lessonName = "Lesson Name";
        String prepareTime = "Preparation Time";
        String activityTime = "Activity Time";
        String objectives = "Objectives";
        String materials = "Materials";
        String keyVocabularyWords = "Key Vocabulary Words";
        String implementationSteps = "Implementation Steps";
        String frameworkInfo = "Framework Info";
        List<String> measures = new ArrayList<>();
        measures.add("Measure 1");
        measures.add("Measure 2");
        // 调用方法
        String culturallyResponsiveInstructionPrompt = LessonPromptUtil.createCulturallyResponsiveInstructionPrompt(template, grade, lessonName, prepareTime, activityTime, objectives, materials, keyVocabularyWords, implementationSteps, frameworkInfo, measures, ClassroomType.IN_PERSON);
        // 验证结果
        Assertions.assertNotNull(culturallyResponsiveInstructionPrompt);
        // 验证数据
        Assertions.assertTrue(culturallyResponsiveInstructionPrompt.contains(lessonName));
        Assertions.assertTrue(culturallyResponsiveInstructionPrompt.contains(prepareTime));
    }

    @Test
    public void testCreateEvaluatePlanOverviewPrompt() {
        String template = "{{ unit name }}{{ city }}{{ state }}{{ age group }}{{ generated unit overview, trajectory, concepts and guiding questions }}" +
                "{{ generated weekly themes and overview }}";
        CurriculumUnitEntity unit = new CurriculumUnitEntity();
        unit.setTitle("Unit Name");
        unit.setCity("City");
        unit.setState("State");
        unit.setGrade("Grade");
        unit.setOverview("Overview");
        unit.setTrajectory("Trajectory");
        unit.setConcepts("Concepts");
        unit.setGuidingQuestions("Guiding Questions");
        String generatedPlanContent = "Generated Plan Content";
        // 调用方法
        String evaluatePlanOverviewPrompt = LessonPromptUtil.createEvaluatePlanOverviewPrompt(template, unit, generatedPlanContent);
        // 验证结果
        Assertions.assertNotNull(evaluatePlanOverviewPrompt);
        // 验证数据
        Assertions.assertTrue(evaluatePlanOverviewPrompt.contains(unit.getTitle()));
        Assertions.assertTrue(evaluatePlanOverviewPrompt.contains(unit.getCity()));
        Assertions.assertTrue(evaluatePlanOverviewPrompt.contains(unit.getState()));
    }

    @Test
    public void testCreateEvaluateLessonOverviewPrompt() {
        String template = "{{ unit name }} {{ week number }} {{ assessment framework }} {{ city }}{{ state }}" +
                "{{ age group }} {{ generated lesson plan ideas }}";
        CurriculumUnitEntity unit = new CurriculumUnitEntity();
        unit.setTitle("Unit Name");
        unit.setCity("City");
        unit.setState("State");
        unit.setGrade("Grade");
        String generatedPlanContent = "Generated Plan Content";
        String framework = "Framework";
        // 调用方法
        String evaluateLessonOverviewPrompt = LessonPromptUtil.createEvaluateLessonOverviewPrompt(template, unit, generatedPlanContent, framework);
        // 验证结果
        Assertions.assertNotNull(evaluateLessonOverviewPrompt);
        // 验证数据
        Assertions.assertTrue(evaluateLessonOverviewPrompt.contains(unit.getTitle()));
        Assertions.assertTrue(evaluateLessonOverviewPrompt.contains(unit.getCity()));
        Assertions.assertEquals(evaluateLessonOverviewPrompt, "Unit Name 1 Framework CityStateGrade Generated Plan Content");
    }

    @Test
    public void testCreateEvaluateLessonPrompt() {
        String template = "{{ unit name }} {{ age group }} {{ assessment framework }} {{ generated lesson content }}";
        CurriculumUnitEntity unit = new CurriculumUnitEntity();
        unit.setTitle("Unit Name");
        unit.setGrade("Grade");
        String generatedPlanContent = "Generated Plan Content";
        String framework = "Framework";
        // 调用方法
        String evaluateLessonPrompt = LessonPromptUtil.createEvaluateLessonPrompt(template, unit, generatedPlanContent, framework);
        // 验证结果
        Assertions.assertNotNull(evaluateLessonPrompt);
        // 验证数据
        Assertions.assertTrue(evaluateLessonPrompt.contains(unit.getTitle()));
        Assertions.assertEquals(evaluateLessonPrompt, "Unit Name Grade Framework Generated Plan Content");
    }

    @Test
    public void testCreateEvaluateTypicalBehaviorsPrompt() {
        String template = "{{ unit name }} {{ age group }} {{ assessment framework }} {{ generated typical behaviors content }} {{ generated lesson content }}";
        CurriculumUnitEntity unit = new CurriculumUnitEntity();
        unit.setTitle("Unit Name");
        unit.setGrade("Grade");
        String generatedTypicalContent = "Generated Typical Content";
        String framework = "Framework";
        String generatedLessonContent = "Generated Lesson Content";
        // 调用方法
        String evaluateTypicalBehaviorsPrompt = LessonPromptUtil.createEvaluateTypicalBehaviorsPrompt(template, unit, generatedTypicalContent, framework, generatedLessonContent);
        // 验证结果
        Assertions.assertNotNull(evaluateTypicalBehaviorsPrompt);
        // 验证数据
        Assertions.assertTrue(evaluateTypicalBehaviorsPrompt.contains(unit.getTitle()));
        Assertions.assertEquals(evaluateTypicalBehaviorsPrompt, "Unit Name Grade Framework Generated Typical Content Generated Lesson Content");
    }

    @Test
    public void testGetLessonContent() {
        String lessonName = "Lesson Name";
        String prepareTime = "Preparation Time";
        String activityTime = "Activity Time";
        List<String>measures = new ArrayList<>();
        measures.add("Measure 1");
        measures.add("Measure 2");
        String objectives = "Objectives";
        String materials = "Materials";
        String keyVocabularyWords = "Key Vocabulary Words";
        String implementationSteps = "Implementation Steps";
        // 调用方法
        String lessonContent = LessonPromptUtil.getLessonContent(lessonName, prepareTime, activityTime, measures, objectives, materials, keyVocabularyWords, implementationSteps);
        // 验证结果
        Assertions.assertNotNull(lessonContent);
        // 验证数据
        Assertions.assertTrue(lessonContent.contains(lessonName));
        Assertions.assertTrue(lessonContent.contains(prepareTime));
    }

    /**
     * 测试获取年龄组
     * case：无效年龄组情况，返回 0
     */
    @Test
    public void testGetAgeValueWithoutValue() {
        String ageGroup = "Age Group";
        // 调用方法
        int ageValue = LessonPromptUtil.getAgeValue(ageGroup);
        // 验证数据
        Assertions.assertEquals(ageValue, 0);
    }

    /**
     * 测试获取年龄组
     * case：有效年龄组情况，返回相应的年龄
     */
    @Test
    public void testGetAgeValue() {
        String ageGroup = "Grade 1";
        // 调用方法
        int ageValue = LessonPromptUtil.getAgeValue(ageGroup);
        // 验证数据
        Assertions.assertEquals(ageValue, 7);
    }
    /**
     * 测试组装课程测验题 Prompt
     */
    @Test
    public void testCreateLessonQuizPrompt() {
        String template = "template {{ assessment framework }} {{ unit name }} {{ age group }} {{ lesson name }} {{ preparation time }} {{ activity duration }} " +
                "{{ objectives }} {{ materials }} {{ key vocabulary words }} {{ implementation steps }} {{ measures }} {{ number of measures }}";
        String unitName = "unitName";
        String ageGroup = "Grade 1";
        String lessonName = "lessonName";
        String prepareTime = "15 minutes";
        String activityTime = "20 minutes";
        String objectives = "Objectives";
        String materials = "Materials";
        String keyVocabularyWords = "Key Vocabulary Words";
        String implementationSteps = "Implementation Steps";
        String frameworkInfo = "Framework Info";
        List<String> measures = new ArrayList<>();
        measures.add("Measure 1");
        measures.add("Measure 2");
        // 调用方法
        String lessonQuizPrompt = LessonPromptUtil.createLessonQuizPrompt(template, unitName, null, ageGroup, lessonName, prepareTime, activityTime, objectives, materials, keyVocabularyWords, implementationSteps, frameworkInfo, measures, null);
        // 验证结果
        Assertions.assertNotNull(lessonQuizPrompt);
        Assertions.assertTrue(lessonQuizPrompt.contains("unitName")); // 验证数据中包含 unitName
    }

    /**
     * 测试生成重新生成课程测验题 Prompt
     */
    @Test
    public void testCreateRedesignLessonQuizPrompt() {
        // 准备数据
        String template = "template {{ assessment framework }} {{ age group }} {{ lesson name }} {{ preparation time }} {{ activity duration }} " +
                "{{ objectives }} {{ materials }} {{ key vocabulary words }} {{ implementation steps }} {{ measures }} {{ number of measures }} {{ {{ quiz level }} {{ level description }} {{ quiz question info }}";
        String ageGroup = "Grade 1";
        String lessonName = "lessonName";
        String prepareTime = "15 minutes";
        String activityTime = "20 minutes";
        String objectives = "Objectives";
        String materials = "Materials";
        String keyVocabularyWords = "Key Vocabulary Words";
        String implementationSteps = "Implementation Steps";
        String previousQuestion = "Previous Question";
        String frameworkInfo = "Framework Info";
        String quizLevel = "Quiz Level";
        String levelDescription = "Level Description";
        String questionType = "FILL_IN_BLANK";
        String selectedMeasure = "m1;m2;m3";
        List<String> measures = new ArrayList<>();
        measures.add("Measure 1");
        measures.add("Measure 2");
        // 调用方法
        String lessonQuizPrompt = LessonPromptUtil.createRedesignLessonQuizPrompt(template, ageGroup, lessonName,
                prepareTime, activityTime, objectives,
                materials, keyVocabularyWords, implementationSteps,
                previousQuestion, questionType, selectedMeasure, quizLevel, levelDescription, frameworkInfo, measures);
        // 验证结果
        Assertions.assertNotNull(lessonQuizPrompt);
        Assertions.assertTrue(lessonQuizPrompt.contains("lessonName")); // 验证数据中包含 unitName
    }


    /**
     * 测试生成重新生成单个课程概览 Prompt
     */
    @Test
    public void testCreateSingleLessonOverviewPrompt() {
        String template = "{{ week number }} {{ unit name }}"; // Prompt 模板
        String dayOfWeek = "1"; // 周几
        CurriculumUnitEntity unit = new CurriculumUnitEntity(); // 单元
        unit.setTitle("unit 01"); // 单元名称
        unit.setGrade("grade");
        PlanEntity plan = new PlanEntity(); // 周计划
        plan.setWeek(1); // 周次
        // 项目列表
        List<ItemEntity> itemEntities = new ArrayList<>();
        ItemEntity item1 = new ItemEntity();
        item1.setDayOfWeek(1);
        itemEntities.add(item1);
        ItemEntity item2 = new ItemEntity();
        item2.setDayOfWeek(2);
        itemEntities.add(item2);
        // 调用方法
        String prompt = LessonPromptUtil.createSingleLessonOverviewPrompt(template, unit, plan, itemEntities, "", "1", "", "", false, "",false, false);
        // 验证结果
        Assertions.assertNotNull(prompt); // 结果非空
        Assertions.assertEquals("1 unit 01", prompt); // 验证 Prompt
    }

    /**
     * 测试根据不同的年龄替换课程详情 Prompt 模版中的年龄提示
     * case：年龄组为 0-1
     */
    @Test
    public void testReplaceLessonDetailAgeTip() {
        // 准备数据
        // 模版
        String template = "{{ age tip }} {{ sharing and feedback }} {{ implementation steps }}";
        // 单元实体
        CurriculumUnitEntity unit = new CurriculumUnitEntity();
        unit.setGrade("Infant (0-1)"); // 年龄组
        // 周计划实体
        PlanEntity plan = new PlanEntity();
        // 周计划活动实体
        ItemEntity item = new ItemEntity();
        // 框架信息
        String frameworkInfo = "Framework Info";
        // center 名字
        String centerName = "Center Name";
        // 测评点列表
        List<String> measures = new ArrayList<>();
        measures.add("Measure 1");
        measures.add("Measure 2");


        // 调用方法
        String prompt = LessonPromptUtil.createCenterLessonDetailPrompt(template, unit, plan, item, frameworkInfo, centerName, measures);

        // 验证结果
        Assertions.assertTrue(prompt.contains("Makes sound spontaneously"));  // 验证年龄提示
    }

    /**
     * 测试根据不同的年龄替换课程详情 Prompt 模版中的年龄提示
     * case：年龄组为 1-2
     */
    @Test
    public void testReplaceLessonDetailAgeTip1() {
        // 准备数据
        // 模版
        String template = "{{ age tip }} {{ sharing and feedback }} {{ implementation steps }}";
        // 单元实体
        CurriculumUnitEntity unit = new CurriculumUnitEntity();
        unit.setGrade("Young Toddler (1-2)"); // 年龄组
        // 周计划实体
        PlanEntity plan = new PlanEntity();
        // 周计划活动实体
        ItemEntity item = new ItemEntity();
        // 框架信息
        String frameworkInfo = "Framework Info";
        // center 名字
        String centerName = "Center Name";
        // 测评点列表
        List<String> measures = new ArrayList<>();
        measures.add("Measure 1");
        measures.add("Measure 2");


        // 调用方法
        String prompt = LessonPromptUtil.createCenterLessonDetailPrompt(template, unit, plan, item, frameworkInfo, centerName, measures);

        // 验证结果
        Assertions.assertTrue(prompt.contains("Uses sounds, gestures"));  // 验证年龄提示
    }

    /**
     * 测试根据不同的年龄替换课程详情 Prompt 模版中的年龄提示
     * case：年龄组为 2-3
     */
    @Test
    public void testReplaceLessonDetailAgeTip2() {
        // 准备数据
        // 模版
        String template = "{{ age tip }} {{ sharing and feedback }} {{ implementation steps }}";
        // 单元实体
        CurriculumUnitEntity unit = new CurriculumUnitEntity();
        unit.setGrade("Toddler (1-3)"); // 年龄组
        // 周计划实体
        PlanEntity plan = new PlanEntity();
        // 周计划活动实体
        ItemEntity item = new ItemEntity();
        // 框架信息
        String frameworkInfo = "Framework Info";
        // center 名字
        String centerName = "Center Name";
        // 测评点列表
        List<String> measures = new ArrayList<>();
        measures.add("Measure 1");
        measures.add("Measure 2");


        // 调用方法
        String prompt = LessonPromptUtil.createCenterLessonDetailPrompt(template, unit, plan, item, frameworkInfo, centerName, measures);

        // 验证结果
        Assertions.assertTrue(prompt.contains("Uses a few “first words"));  // 验证年龄提示
    }

    /**
     * 测试根据不同的年龄替换课程详情 Prompt 模版中的年龄提示
     * case：年龄组为 3-4
     */
    @Test
    public void testReplaceLessonDetailAgeTip3() {
        // 准备数据
        // 模版
        String template = "{{ age tip }} {{ sharing and feedback }} {{ implementation steps }}";
        // 单元实体
        CurriculumUnitEntity unit = new CurriculumUnitEntity();
        unit.setGrade("PS/PK (3-4)"); // 年龄组
        // 周计划实体
        PlanEntity plan = new PlanEntity();
        // 周计划活动实体
        ItemEntity item = new ItemEntity();
        // 框架信息
        String frameworkInfo = "Framework Info";
        // center 名字
        String centerName = "Center Name";
        // 测评点列表
        List<String> measures = new ArrayList<>();
        measures.add("Measure 1");
        measures.add("Measure 2");


        // 调用方法
        String prompt = LessonPromptUtil.createCenterLessonDetailPrompt(template, unit, plan, item, frameworkInfo, centerName, measures);

        // 验证结果
        Assertions.assertTrue(prompt.contains("When incorporating writing-related activities, please consider 3-4 years old children’s emergent writing abilities."));  // 验证年龄提示
    }

    /**
     * 测试根据不同的年龄替换课程详情 Prompt 模版中的年龄提示
     * case：年龄组为 4-5
     */
    @Test
    public void testReplaceLessonDetailAgeTip4() {
        // 准备数据
        // 模版
        String template = "{{ age tip }} {{ sharing and feedback }} {{ implementation steps }}";
        // 单元实体
        CurriculumUnitEntity unit = new CurriculumUnitEntity();
        unit.setGrade("TK (4-5)"); // 年龄组
        // 周计划实体
        PlanEntity plan = new PlanEntity();
        // 周计划活动实体
        ItemEntity item = new ItemEntity();
        // 框架信息
        String frameworkInfo = "Framework Info";
        // center 名字
        String centerName = "Center Name";
        // 测评点列表
        List<String> measures = new ArrayList<>();
        measures.add("Measure 1");
        measures.add("Measure 2");


        // 调用方法
        String prompt = LessonPromptUtil.createCenterLessonDetailPrompt(template, unit, plan, item, frameworkInfo, centerName, measures);

        // 验证结果
        Assertions.assertTrue(prompt.contains("When incorporating writing-related activities, please consider children’s emergent writing abilities. At this age, they start to make scribbles, trace pre-writing strokes"));  // 验证年龄提示
    }

    /**
     * 测试根据不同的年龄替换课程详情 Prompt 模版中的年龄提示
     * case：年龄组为 5-6
     */
    @Test
    public void testReplaceLessonDetailAgeTip5() {
        // 准备数据
        // 模版
        String template = "{{ age tip }} {{ sharing and feedback }} {{ implementation steps }}";
        // 单元实体
        CurriculumUnitEntity unit = new CurriculumUnitEntity();
        unit.setGrade("TK (4-5)"); // 年龄组
        // 周计划实体
        PlanEntity plan = new PlanEntity();
        // 周计划活动实体
        ItemEntity item = new ItemEntity();
        // 框架信息
        String frameworkInfo = "Framework Info";
        // center 名字
        String centerName = "Center Name";
        // 测评点列表
        List<String> measures = new ArrayList<>();
        measures.add("Measure 1");
        measures.add("Measure 2");


        // 调用方法
        String prompt = LessonPromptUtil.createCenterLessonDetailPrompt(template, unit, plan, item, frameworkInfo, centerName, measures);

        // 验证结果
        Assertions.assertTrue(prompt.contains("When incorporating writing-related activities, please consider children’s emergent writing abilities"));  // 验证年龄提示
    }

    /**
     * 测试创建 Centers 课程概览 Prompt
     * case：年龄组为 0-1
     */
    @Test
    public void testCreateCenterOverviewPromptV2() {
        // 准备数据
        // 模版
        String template = "{{ assessment framework }} {{ week number }} {{ unit name }} {{ age group }} {{ unit description }} {{ weekly plan names }} {{ center themes name }} {{ description }}"; // Prompt 模板
        // 单元实体
        CurriculumUnitEntity unit = new CurriculumUnitEntity();
        unit.setTitle("Unit Name"); // 单元名称
        unit.setGrade("Infant (0-1)"); // 年龄组
        List<PlanEntity> allPlans = new ArrayList<>(); // 周计划列表
        PlanEntity plan = new PlanEntity(); // 周计划
        plan.setWeek(1); // 周次
        allPlans.add(plan);
        // center 名字
        List<String> centers = new ArrayList<>(
                Arrays.asList("Art","Blocks","Sensory Table","Math","Dramatic Play","Library & Listening","Science & Engineering","Writing & Drawing","Outdoor",
                        "Fine Motor Area","Music and Movement","Library/Quiet Area","Nature Science/Discovery Area","Sand and/or Water Area"));
        String frameworkInfo = "Framework Info"; // 框架信息
        String books = "books"; // 描述
        String previousMathAndArtCenterActivities = "previousMathAndArtCenterActivities"; // 描述

        // 调用方法
        String prompt = LessonPromptUtil.createCenterOverviewPromptV2(template, unit, allPlans, plan, centers, frameworkInfo, books, previousMathAndArtCenterActivities);

        // 验证结果
        Assertions.assertTrue(prompt.contains("Play some soft rhythmic music and encourage children to")); // 验证框架信息
    }

    /**
     * 测试创建 Centers 课程概览 Prompt
     * case：年龄组为 1-2
     */
    @Test
    public void testCreateCenterOverviewPromptV21() {
        // 准备数据
        // 模版
        String template = "{{ assessment framework }} {{ week number }} {{ unit name }} {{ age group }} {{ unit description }} {{ weekly plan names }} {{ center themes name }} {{ description }}"; // Prompt 模板
        // 单元实体
        CurriculumUnitEntity unit = new CurriculumUnitEntity();
        unit.setTitle("Unit Name"); // 单元名称
        unit.setGrade("Young Toddler (1-2)"); // 年龄组
        List<PlanEntity> allPlans = new ArrayList<>(); // 周计划列表
        PlanEntity plan = new PlanEntity(); // 周计划
        plan.setWeek(1); // 周次
        allPlans.add(plan);
        // center 名字
        List<String> centers = new ArrayList<>(
                Arrays.asList("Art","Blocks","Sensory Table","Math","Dramatic Play","Library & Listening","Science & Engineering","Writing & Drawing","Outdoor",
                        "Fine Motor Area","Music and Movement","Library/Quiet Area","Nature Science/Discovery Area","Sand and/or Water Area"));
        String frameworkInfo = "Framework Info"; // 框架信息
        String books = "books"; // 描述
        String previousMathAndArtCenterActivities = "previousMathAndArtCenterActivities"; // 描述

        // 调用方法
        String prompt = LessonPromptUtil.createCenterOverviewPromptV2(template, unit, allPlans, plan, centers, frameworkInfo, books, previousMathAndArtCenterActivities);

        // 验证结果
        Assertions.assertTrue(prompt.contains("Play soft, cheerful animal-themed music and invite the toddlers to move and explore freely.")); // 验证框架信息
    }

    /**
     * 测试创建 Centers 课程概览 Prompt
     * case：年龄组为 2-3
     */
    @Test
    public void testCreateCenterOverviewPromptV22() {
        // 准备数据
        // 模版
        String template = "{{ assessment framework }} {{ week number }} {{ unit name }} {{ age group }} {{ unit description }} {{ weekly plan names }} {{ center themes name }} {{ description }}"; // Prompt 模板
        // 单元实体
        CurriculumUnitEntity unit = new CurriculumUnitEntity();
        unit.setTitle("Unit Name"); // 单元名称
        unit.setGrade("Toddler (1-3)"); // 年龄组
        List<PlanEntity> allPlans = new ArrayList<>(); // 周计划列表
        PlanEntity plan = new PlanEntity(); // 周计划
        plan.setWeek(1); // 周次
        allPlans.add(plan);
        // center 名字
        List<String> centers = new ArrayList<>(
                Arrays.asList("Art","Blocks","Sensory Table","Math","Dramatic Play","Library & Listening","Science & Engineering","Writing & Drawing","Outdoor",
                        "Fine Motor Area","Music and Movement","Library/Quiet Area","Nature Science/Discovery Area","Sand and/or Water Area"));
        String frameworkInfo = "Framework Info"; // 框架信息
        String books = "books"; // 描述
        String previousMathAndArtCenterActivities = "previousMathAndArtCenterActivities"; // 描述

        // 调用方法
        String prompt = LessonPromptUtil.createCenterOverviewPromptV2(template, unit, allPlans, plan, centers, frameworkInfo, books, previousMathAndArtCenterActivities);

        // 验证结果
        Assertions.assertTrue(prompt.contains("Provide various of safe, non-toxic materials like colorful paper")); // 验证框架信息
    }

    /**
     * 测试创建 Centers 课程概览 Prompt
     * case：年龄组为 3-4
     */
    @Test
    public void testCreateCenterOverviewPromptV23() {
        // 准备数据
        // 模版
        String template = "{{ assessment framework }} {{ week number }} {{ unit name }} {{ age group }} {{ unit description }} {{ weekly plan names }} {{ center themes name }} {{ description }}"; // Prompt 模板
        // 单元实体
        CurriculumUnitEntity unit = new CurriculumUnitEntity();
        unit.setTitle("Unit Name"); // 单元名称
        unit.setGrade("PS/PK (3-4)"); // 年龄组
        List<PlanEntity> allPlans = new ArrayList<>(); // 周计划列表
        PlanEntity plan = new PlanEntity(); // 周计划
        plan.setWeek(1); // 周次
        allPlans.add(plan);
        // center 名字
        List<String> centers = new ArrayList<>(
                Arrays.asList("Art","Blocks","Sensory Table","Math","Dramatic Play","Library & Listening","Science & Engineering","Writing & Drawing","Outdoor",
                        "Fine Motor Area","Music and Movement","Library/Quiet Area","Nature Science/Discovery Area","Sand and/or Water Area"));
        String frameworkInfo = "Framework Info"; // 框架信息
        String books = "books"; // 描述
        String previousMathAndArtCenterActivities = "previousMathAndArtCenterActivities"; // 描述

        // 调用方法
        String prompt = LessonPromptUtil.createCenterOverviewPromptV2(template, unit, allPlans, plan, centers, frameworkInfo, books, previousMathAndArtCenterActivities);

        // 验证结果
        Assertions.assertTrue(prompt.contains("xxx")); // 验证框架信息
    }

    /**
     * 测试创建 Centers 课程概览 Prompt
     * case：年龄组为 4-5
     */
    @Test
    public void testCreateCenterOverviewPromptV24() {
        // 准备数据
        // 模版
        String template = "{{ assessment framework }} {{ week number }} {{ unit name }} {{ age group }} {{ unit description }} {{ weekly plan names }} {{ center themes name }} {{ description }}"; // Prompt 模板
        // 单元实体
        CurriculumUnitEntity unit = new CurriculumUnitEntity();
        unit.setTitle("Unit Name"); // 单元名称
        unit.setGrade("TK (4-5)"); // 年龄组
        List<PlanEntity> allPlans = new ArrayList<>(); // 周计划列表
        PlanEntity plan = new PlanEntity(); // 周计划
        plan.setWeek(1); // 周次
        allPlans.add(plan);
        // center 名字
        List<String> centers = new ArrayList<>(
                Arrays.asList("Art","Blocks","Sensory Table","Math","Dramatic Play","Library & Listening","Science & Engineering","Writing & Drawing","Outdoor",
                        "Fine Motor Area","Music and Movement","Library/Quiet Area","Nature Science/Discovery Area","Sand and/or Water Area"));
        String frameworkInfo = "Framework Info"; // 框架信息
        String books = "books"; // 描述
        String previousMathAndArtCenterActivities = "previousMathAndArtCenterActivities"; // 描述

        // 调用方法
        String prompt = LessonPromptUtil.createCenterOverviewPromptV2(template, unit, allPlans, plan, centers, frameworkInfo, books, previousMathAndArtCenterActivities);

        // 验证结果
        Assertions.assertTrue(prompt.contains("xxx")); // 验证框架信息
    }

    /**
     * 测试创建 Centers 课程概览 Prompt
     * case：年龄组为 4-5
     */
    @Test
    public void testCreateCenterOverviewPromptV25() {
        // 准备数据
        // 模版
        String template = "{{ assessment framework }} {{ week number }} {{ unit name }} {{ age group }} {{ unit description }} {{ weekly plan names }} {{ center themes name }} {{ description }}"; // Prompt 模板
        // 单元实体
        CurriculumUnitEntity unit = new CurriculumUnitEntity();
        unit.setTitle("Unit Name"); // 单元名称
        unit.setGrade("TK (4-5)"); // 年龄组
        List<PlanEntity> allPlans = new ArrayList<>(); // 周计划列表
        PlanEntity plan = new PlanEntity(); // 周计划
        plan.setWeek(1); // 周次
        allPlans.add(plan);
        // center 名字
        List<String> centers = new ArrayList<>();
        centers.add("Art");
        String frameworkInfo = "Framework Info"; // 框架信息
        String books = "books"; // 描述
        String previousMathAndArtCenterActivities = "previousMathAndArtCenterActivities"; // 描述

        // 调用方法
        String prompt = LessonPromptUtil.createCenterOverviewPromptV2(template, unit, allPlans, plan, centers, frameworkInfo, books, previousMathAndArtCenterActivities);

        // 验证结果
        Assertions.assertTrue(prompt.contains("xxx")); // 验证框架信息
    }
}
