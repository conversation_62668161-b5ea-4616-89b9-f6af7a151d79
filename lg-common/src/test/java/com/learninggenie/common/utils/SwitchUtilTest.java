package com.learninggenie.common.utils;

import org.junit.Test;

import com.learninggenie.common.monday.util.SwitchUtil;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import static com.learninggenie.common.monday.util.SwitchUtil.*;
import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class SwitchUtilTest {

    @Test
    public void testSwitchActivityLevelIndex() {
        Integer integer = SwitchUtil.switchActivityLevelIndex(0.1);
        Integer integer1 = SwitchUtil.switchActivityLevelIndex(0.5);
        Integer integer2 = SwitchUtil.switchActivityLevelIndex(0.8);

        assertEquals(Integer.valueOf(2), integer);
        assertEquals(Integer.valueOf(0), integer1);
        assertEquals(Integer.valueOf(1), integer2);
    }

    @Test
    public void testSwitchActivityLevelIndex2() {
        Integer integer = SwitchUtil.switchActivityLevelIndex(0.01d, false);
        Integer integer2 = SwitchUtil.switchActivityLevelIndex(0.01d, true);

        assertEquals(Integer.valueOf(17), integer);
        assertEquals(Integer.valueOf(2), integer2);
    }

    @Test
    public void testSwitchParentIndex() {
        Integer integer1 = SwitchUtil.switchParentIndex(0.1);
        Integer integer2 = SwitchUtil.switchParentIndex(0.3);
        Integer integer3 = SwitchUtil.switchParentIndex(0.6);
        Integer integer4 = SwitchUtil.switchParentIndex(0.9);

        assertEquals(Integer.valueOf(2), integer1);
        assertEquals(Integer.valueOf(0), integer2);
        assertEquals(Integer.valueOf(7), integer3);
        assertEquals(Integer.valueOf(1), integer4);
    }

    @Test
    public void testSwitchParentIndex2() {
        Integer integer1 = SwitchUtil.switchParentIndex(0.1, false);
        Integer integer2 = SwitchUtil.switchParentIndex(0.3, true);

        assertEquals(Integer.valueOf(17), integer1);
        assertEquals(Integer.valueOf(0), integer2);
    }

    @Test
    public void testSwitchFourStageIndex() {
        Integer integer1 = SwitchUtil.switchFourStageIndex(0.1);
        Integer integer2 = SwitchUtil.switchFourStageIndex(0.3);
        Integer integer3 = SwitchUtil.switchFourStageIndex(0.6);
        Integer integer4 = SwitchUtil.switchFourStageIndex(0.9);

        assertEquals(Integer.valueOf(2), integer1);
        assertEquals(Integer.valueOf(0), integer2);
        assertEquals(Integer.valueOf(3), integer3);
        assertEquals(Integer.valueOf(1), integer4);
    }

    @Test
    public void testSwitchFourStageIndex2() {
        Integer integer1 = SwitchUtil.switchFourStageIndex(0.1, false);
        Integer integer2 = SwitchUtil.switchFourStageIndex(0.3);

        assertEquals(Integer.valueOf(17), integer1);
        assertEquals(Integer.valueOf(0), integer2);
    }

    @Test
    public void testSwitchBooleanIndex() {
        Integer integer1 = SwitchUtil.switchBooleanIndex(false);

        assertEquals(Integer.valueOf(2), integer1);
    }

    @Test
    public void testSwitchBooleanIndex2() {
        Integer integer1 = SwitchUtil.switchBooleanIndex(true, false);
        Integer integer2 = SwitchUtil.switchBooleanIndex(true, true);

        assertEquals(Integer.valueOf(17), integer1);
        assertEquals(Integer.valueOf(1), integer2);
    }

    @Test
    public void testGetActivityValue() {
        String s1 = SwitchUtil.getActivityValue(null);
        String s2 = SwitchUtil.getActivityValue(0.1);
        String s3 = SwitchUtil.getActivityValue(0.3);
        String s4 = SwitchUtil.getActivityValue(0.6);
        String s5 = SwitchUtil.getActivityValue(0.9);

        assertEquals(STATUS_DEFAULT, s1);
        assertEquals(STATUS_RED_PERCENT, s2);
        assertEquals(STATUS_YELLOW_PERCENT, s3);
        assertEquals(STATUS_BLUE_PERCENT, s4);
        assertEquals(STATUS_GREEN_PERCENT, s5);
    }
}
