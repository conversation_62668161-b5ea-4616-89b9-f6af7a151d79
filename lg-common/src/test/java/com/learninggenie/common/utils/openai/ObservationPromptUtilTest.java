package com.learninggenie.common.utils.openai;

import org.junit.Test;
import org.junit.jupiter.api.Assertions;

import java.util.ArrayList;
import java.util.List;

public class ObservationPromptUtilTest {

    @Test
    public void testCreateGenerateMeasuresAndLevelsPrompt() {
        String template = "";
        String observation = "";
        String frameworkInfo = "";
        // 调用方法
        String result = ObservationPromptUtil.createGenerateMeasuresAndLevelsPrompt(template, observation, frameworkInfo);
        // 断言
        Assertions.assertNotNull(result);
    }

    @Test
    public void testCreateGenerateMeasuresAndLevelsPrompt2() {
        String template = "{{ framework_base_info }} {{ observation }}";
        String observation = "observation";
        String frameworkInfo = "frameworkInfo";
        // 调用方法
        String result = ObservationPromptUtil.createGenerateMeasuresAndLevelsPrompt(template, observation, frameworkInfo);
        // 断言
        Assertions.assertNotNull(result);
        // 验证数据
        Assertions.assertTrue(result.contains(observation));
        Assertions.assertTrue(result.contains(frameworkInfo));
        Assertions.assertEquals(result, frameworkInfo + " " + observation);
    }

    @Test
    public void testCreateImproveObservationPrompt() {
        String template = "";
        String observation = "";
        String frameworkInfo = "";
        // 调用方法
        //String result = ObservationPromptUtil.createImproveObservationPrompt(template, observation, frameworkInfo, null);
        // 断言
       // Assertions.assertNotNull(result);
    }

    @Test
    public void testCreateImproveObservationPrompt2() {
        String template = "{{ framework_base_info }} {{ observation }} {{ measures }}";
        String observation = "observation";
        String frameworkInfo = "frameworkInfo";
        List<String> measures = new ArrayList<>();
        measures.add("measure1");
        measures.add("measure2");
        // 调用方法
//        String result = ObservationPromptUtil.createImproveObservationPrompt(template, observation, frameworkInfo, measures);
//        // 断言
//        Assertions.assertNotNull(result);
//        // 验证数据
//        Assertions.assertTrue(result.contains(observation));
//        Assertions.assertTrue(result.contains(frameworkInfo));
//        Assertions.assertEquals(result, frameworkInfo + " " + observation + " " + String.join(", ", measures));
    }
}
