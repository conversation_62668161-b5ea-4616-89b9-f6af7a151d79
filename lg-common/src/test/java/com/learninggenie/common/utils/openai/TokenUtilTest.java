package com.learninggenie.common.utils.openai;

import com.knuddels.jtokkit.api.Encoding;
import com.knuddels.jtokkit.api.ModelType;
import com.theokanning.openai.completion.chat.ChatMessage;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;

import java.util.ArrayList;
import java.util.List;

public class TokenUtilTest {

    @Test
    public void testGetEncoding() {
        String modelName = ModelType.GPT_4.getName();
        // 调用方法
        Encoding encoding = TokenUtil.getEncoding(modelName);
        // 断言
        Assertions.assertNotNull(encoding);
        // 验证内容
        Assertions.assertEquals("cl100k_base", encoding.getName());
    }

    @Test
    public void testCountTokens() {
        String modelName = ModelType.DAVINCI.getName();
        // 调用方法
        long count = TokenUtil.countTokens("hello world", modelName);
        // 断言
        Assertions.assertEquals(2, count);
    }

    @Test
    public void testCountTokens2() {
        List<ChatMessage> messages = new ArrayList<>();
        ChatMessage chatMessage = new ChatMessage();
        chatMessage.setContent("hello world");
        messages.add(chatMessage);
        String modelName = ModelType.DAVINCI.getName();
        // 调用方法
        long count = TokenUtil.countTokens(messages, modelName);
        // 断言
        Assertions.assertEquals(2, count);
    }

    @Test
    public void testCalculateCost() {
        // 调用方法
        Double cost = TokenUtil.calculateCost("gpt-3.5-turbo", 100, 100);
        // 断言
        Assertions.assertEquals(4.0E-4, cost);
    }

    @Test
    public void testCalculateCost2() {
        // 调用方法
        Double cost = TokenUtil.calculateCost("davinci", 100, 100);
        // 断言
        Assertions.assertNull(cost);
    }

    @Test
    public void testCalculateCost3() {
        // 调用方法
        Double cost = TokenUtil.calculateCost("", 100, 100);
        // 断言
        Assertions.assertNull(cost);
    }
}
