//package com.learninggenie.common.utils;
//
//import com.learninggenie.common.data.model.IntervalModel;
//import org.junit.Test;
//
//import java.util.ArrayList;
//import java.util.Date;
//import java.util.List;
//
//import static org.junit.Assert.*;
//import static org.mockito.Mockito.*;
//
///**
// * Created by starsli on 2017/4/26.
// */
//public class TimeUtilTest {
//
//    @Test
//    public void ValidDate() {
//        assertEquals(true, TimeUtil.isValidDate("2017-02-02"));
//        assertEquals(true, TimeUtil.isValidDate("02-02-2017"));
//        assertEquals(true, TimeUtil.isValidDate("02/02/2017"));
//        assertEquals(true, TimeUtil.isValidDate("2017/02/02"));
//        assertEquals(true, TimeUtil.isValidDate("2/2/2017"));
//        assertEquals(false, TimeUtil.isValidDate("22222/2/2017"));
//        assertEquals(false, TimeUtil.isValidDate("13/13/2017"));
//    }
//
//    @Test
//    public void testMinusDate() {
//        Date date = TimeUtil.parseDate("12/12/2020");
//        assertEquals("12/11/2020", TimeUtil.format(TimeUtil.minusDays(date, 1), "MM/dd/yyyy"));
//        assertEquals("12/02/2020", TimeUtil.format(TimeUtil.minusDays(date, 10), "MM/dd/yyyy"));
//        assertEquals("12/10/2020", TimeUtil.format(TimeUtil.minusDays(date, 2), "MM/dd/yyyy"));
//    }
//
//    @Test
//    public void testGetLastWorkingDay() {
//        Date from = TimeUtil.parse("12/01/2018", TimeUtil.format3);
//        Date to = TimeUtil.parse("12/31/2018", TimeUtil.format3);
//        Date date = TimeUtil.getLastWorkingDay(from, to);
//        assertEquals(TimeUtil.parse("12/31/2018", TimeUtil.format3), date);
//        assertEquals(TimeUtil.parse("12/01/2018", TimeUtil.format3), from);
//        assertEquals(TimeUtil.parse("12/31/2018", TimeUtil.format3), to);
//
//        to = TimeUtil.parse("12/30/2018", TimeUtil.format3);
//        date = TimeUtil.getLastWorkingDay(from, to);
//        assertEquals(TimeUtil.parse("12/28/2018", TimeUtil.format3), date);
//        assertEquals(TimeUtil.parse("12/01/2018", TimeUtil.format3), from);
//        assertEquals(TimeUtil.parse("12/30/2018", TimeUtil.format3), to);
//
//        to = TimeUtil.parse("12/29/2018", TimeUtil.format3);
//        date = TimeUtil.getLastWorkingDay(from, to);
//        assertEquals(TimeUtil.parse("12/28/2018", TimeUtil.format3), date);
//        assertEquals(TimeUtil.parse("12/01/2018", TimeUtil.format3), from);
//        assertEquals(TimeUtil.parse("12/29/2018", TimeUtil.format3), to);
//
//        to = TimeUtil.parse("12/28/2018", TimeUtil.format3);
//        date = TimeUtil.getLastWorkingDay(from, to);
//        assertEquals(TimeUtil.parse("12/28/2018", TimeUtil.format3), date);
//        assertEquals(TimeUtil.parse("12/01/2018", TimeUtil.format3), from);
//        assertEquals(TimeUtil.parse("12/28/2018", TimeUtil.format3), to);
//
//        to = TimeUtil.parse("12/27/2018", TimeUtil.format3);
//        date = TimeUtil.getLastWorkingDay(from, to);
//        assertEquals(TimeUtil.parse("12/27/2018", TimeUtil.format3), date);
//        assertEquals(TimeUtil.parse("12/01/2018", TimeUtil.format3), from);
//        assertEquals(TimeUtil.parse("12/27/2018", TimeUtil.format3), to);
//    }
//
//    @Test
//    public void testGetFirstWorkingDay() {
//        Date from = TimeUtil.parse("12/01/2018", TimeUtil.format3);
//        Date to = TimeUtil.parse("12/31/2018", TimeUtil.format3);
//        Date date = TimeUtil.getFirstWorkingDay(from, to);
//        assertEquals(TimeUtil.parse("12/03/2018", TimeUtil.format3), date);
//        assertEquals(TimeUtil.parse("12/01/2018", TimeUtil.format3), from);
//        assertEquals(TimeUtil.parse("12/31/2018", TimeUtil.format3), to);
//
//        from = TimeUtil.parse("12/02/2018", TimeUtil.format3);
//        date = TimeUtil.getFirstWorkingDay(from, to);
//        assertEquals(TimeUtil.parse("12/03/2018", TimeUtil.format3), date);
//        assertEquals(TimeUtil.parse("12/02/2018", TimeUtil.format3), from);
//        assertEquals(TimeUtil.parse("12/31/2018", TimeUtil.format3), to);
//
//        from = TimeUtil.parse("12/03/2018", TimeUtil.format3);
//        date = TimeUtil.getFirstWorkingDay(from, to);
//        assertEquals(TimeUtil.parse("12/03/2018", TimeUtil.format3), date);
//        assertEquals(TimeUtil.parse("12/03/2018", TimeUtil.format3), from);
//        assertEquals(TimeUtil.parse("12/31/2018", TimeUtil.format3), to);
//
//        from = TimeUtil.parse("12/04/2018", TimeUtil.format3);
//        date = TimeUtil.getFirstWorkingDay(from, to);
//        assertEquals(TimeUtil.parse("12/04/2018", TimeUtil.format3), date);
//        assertEquals(TimeUtil.parse("12/04/2018", TimeUtil.format3), from);
//        assertEquals(TimeUtil.parse("12/31/2018", TimeUtil.format3), to);
//
//        from = TimeUtil.parse("12/07/2018", TimeUtil.format3);
//        date = TimeUtil.getFirstWorkingDay(from, to);
//        assertEquals(TimeUtil.parse("12/07/2018", TimeUtil.format3), date);
//        assertEquals(TimeUtil.parse("12/07/2018", TimeUtil.format3), from);
//        assertEquals(TimeUtil.parse("12/31/2018", TimeUtil.format3), to);
//    }
//
//    @Test
//    public void testGetCurrentMonday() {
//        Date monday = TimeUtil.parse("12/20/2021", TimeUtil.format3);
//        assertEquals(TimeUtil.getCurrentMonday(TimeUtil.parse("12/20/2021", TimeUtil.format3)), monday);
//        assertEquals(TimeUtil.getCurrentMonday(TimeUtil.parse("12/21/2021", TimeUtil.format3)), monday);
//        assertEquals(TimeUtil.getCurrentMonday(TimeUtil.parse("12/22/2021", TimeUtil.format3)), monday);
//        assertEquals(TimeUtil.getCurrentMonday(TimeUtil.parse("12/23/2021", TimeUtil.format3)), monday);
//        assertEquals(TimeUtil.getCurrentMonday(TimeUtil.parse("12/24/2021", TimeUtil.format3)), monday);
//        assertEquals(TimeUtil.getCurrentMonday(TimeUtil.parse("12/25/2021", TimeUtil.format3)), monday);
//        assertEquals(TimeUtil.getCurrentMonday(TimeUtil.parse("12/26/2021", TimeUtil.format3)), monday);
//    }
//
//    @Test
//    public void testGetCurrentFriday() {
//        Date friday = TimeUtil.parse("12/24/2021", TimeUtil.format3);
//        assertEquals(TimeUtil.getCurrentFriday(TimeUtil.parse("12/20/2021", TimeUtil.format3)), friday);
//        assertEquals(TimeUtil.getCurrentFriday(TimeUtil.parse("12/21/2021", TimeUtil.format3)), friday);
//        assertEquals(TimeUtil.getCurrentFriday(TimeUtil.parse("12/22/2021", TimeUtil.format3)), friday);
//        assertEquals(TimeUtil.getCurrentFriday(TimeUtil.parse("12/23/2021", TimeUtil.format3)), friday);
//        assertEquals(TimeUtil.getCurrentFriday(TimeUtil.parse("12/24/2021", TimeUtil.format3)), friday);
//        assertEquals(TimeUtil.getCurrentFriday(TimeUtil.parse("12/25/2021", TimeUtil.format3)), friday);
//        assertEquals(TimeUtil.getCurrentFriday(TimeUtil.parse("12/26/2021", TimeUtil.format3)), friday);
//    }
//
//    @Test
//    public void testMergeIntervals() {
//        List<IntervalModel> intervals = new ArrayList<>();
//        intervals.add(new IntervalModel(TimeUtil.parseDate("2022-02-01"), TimeUtil.parseDate("2022-02-02")));
//        intervals.add(new IntervalModel(TimeUtil.parseDate("2022-02-01"), TimeUtil.parseDate("2022-02-04")));
//        intervals.add(new IntervalModel(TimeUtil.parseDate("2022-02-03"), TimeUtil.parseDate("2022-02-08")));
//        intervals.add(new IntervalModel(TimeUtil.parseDate("2022-02-10"), TimeUtil.parseDate("2022-02-12")));
//        intervals.add(new IntervalModel(TimeUtil.parseDate("2022-02-15"), TimeUtil.parseDate("2022-02-15")));
//        intervals.add(new IntervalModel(TimeUtil.parseDate("2022-02-18"), TimeUtil.parseDate("2022-02-20")));
//        intervals.add(new IntervalModel(TimeUtil.parseDate("2022-02-19"), TimeUtil.parseDate("2022-02-25")));
//        intervals = TimeUtil.mergeIntervals(intervals);
//        assertEquals(4, intervals.size());
//    }
//
//    @Test
//    public void testSecondToMinuteUpFive() {
//        assertEquals(0, TimeUtil.secondToMinuteUpFive(0));
//        assertEquals(5, TimeUtil.secondToMinuteUpFive(1));
//        assertEquals(5, TimeUtil.secondToMinuteUpFive(10));
//        assertEquals(5, TimeUtil.secondToMinuteUpFive(299));
//        assertEquals(5, TimeUtil.secondToMinuteUpFive(300));
//        assertEquals(15, TimeUtil.secondToMinuteUpFive(700));
//        assertEquals(40, TimeUtil.secondToMinuteUpFive(2300));
//        assertEquals(40, TimeUtil.secondToMinuteUpFive(2400));
//    }
//}
