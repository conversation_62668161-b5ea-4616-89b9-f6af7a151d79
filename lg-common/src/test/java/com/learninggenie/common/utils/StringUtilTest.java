package com.learninggenie.common.utils;

import junit.framework.Assert;
import org.junit.Test;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

import static org.junit.Assert.*;
import static org.junit.jupiter.api.Assertions.assertFalse;

public class StringUtilTest {
    @Test
    public void testSubCompletedSentence(){
        String text="word word word word.";
        String newText=StringUtil.subCompletedSentence(text,10);
        Assert.assertEquals("word word",newText);
    }

    @Test
    public void testSubCompletedSentence1(){
        String text="word.";
        String newText=StringUtil.subCompletedSentence(text,10);
        Assert.assertEquals("word.",newText);
    }

    @Test
    public void testSubCompletedSentence2(){
        String text="word word word word.";
        String newText=StringUtil.subCompletedSentence(text,20);
        Assert.assertEquals("word word word word.",newText);
    }

    @Test
    public void testSubCompletedSentence3(){
        String text="word word.word word.";
        String newText=StringUtil.subCompletedSentence(text,15);
        Assert.assertEquals("word word.",newText);
    }

    @Test
    public void testSubCompletedSentence4(){
        String text="word word!word word.";
        String newText=StringUtil.subCompletedSentence(text,15);
        Assert.assertEquals("word word!",newText);
    }

    @Test
    public void testSubCompletedSentence5(){
        String text="word word?word word.";
        String newText=StringUtil.subCompletedSentence(text,15);
        Assert.assertEquals("word word?",newText);
    }
    
    @Test
    public void testSubCompletedSentence6(){
        String text="Today we reviewed the letters, color, and shape of the week. We also practiced our listening skills by dancing \"Shoulders Knees and Toes\". For art we practiced cutting, tracing, and grasping of a pencil. We also baked apples and sprinkled them within cinnamon for a delicious and nutritious snack.";
        String newText=StringUtil.subCompletedSentence(text,140);
        Assert.assertEquals("Today we reviewed the letters, color, and shape of the week. We also practiced our listening skills by dancing \"Shoulders Knees and Toes\".",newText);
    }
    @Test
    public void testSubCompletedSentence7(){
        String text="Franklin stepped back thunderstruck, as well he might be. The coachman, not knowing what to do," +
                " looked towards my lady, still standing immovable on the top step. My lady, with anger and sorrow and " +
                "shame all struggling together in her face, made him a sign to start the horses, and then turned back h" +
                "astily into the house. Mr. Franklin, recovering the use of his speech, called after her, as the carriag" +
                "e drove off, Aunt! you were quite right. Accept my thanks for all your kindness—and let me go.";
        String length_2 = StringUtil.subCompletedSentence(text, 2);
        Assert.assertEquals("Fr", length_2);
        String length_16 = StringUtil.subCompletedSentence(text, 16);
        System.out.println(length_16);
        Assert.assertEquals("Franklin stepped", length_16);
        String length_25 = StringUtil.subCompletedSentence(text, 34);
        System.out.println(length_25);
        Assert.assertEquals("Franklin stepped back", length_25);
        String length_30 = StringUtil.subCompletedSentence(text, 30);
        System.out.println(length_30);
        Assert.assertEquals("Franklin stepped back", length_30);
        String length_50 = StringUtil.subCompletedSentence(text, 50);
        System.out.println(length_50);
        Assert.assertEquals("Franklin stepped back thunderstruck", length_50);
        String length_75 = StringUtil.subCompletedSentence(text, 75);
        System.out.println(length_75);
        Assert.assertEquals("Franklin stepped back thunderstruck, as well he might be.", length_75);
        String length_100 = StringUtil.subCompletedSentence(text, 100);
        System.out.println(length_100);
        Assert.assertEquals("Franklin stepped back thunderstruck, as well he might be.", length_100);
        String length_300 = StringUtil.subCompletedSentence(text, 300);
        System.out.println(length_300);
        Assert.assertEquals("Franklin stepped back thunderstruck, as well he might be. The coachman, not knowing what to do, looked towards my lady, still standing immovable on the top step.", length_300);
        String length_500 = StringUtil.subCompletedSentence(text, 500);
        System.out.println(length_500);
        Assert.assertEquals("Franklin stepped back thunderstruck, as well he might be. The coachman, not knowing what to do, looked towards my lady, still standing immovable on the top step. My lady, with anger and sorrow and shame all struggling together in her face, made him a sign to start the horses, and then turned back hastily into the house. Mr. Franklin, recovering the use of his speech, called after her, as the carriage drove off, Aunt! you were quite right. Accept my thanks for all your kindness—and let me go.", length_500);
        String length_0 = StringUtil.subCompletedSentence(text, 0);
        System.out.println(length_0);
        Assert.assertEquals("",length_0);
        String length_10 = StringUtil.subCompletedSentence(text, 10);
        System.out.println(length_10);
        Assert.assertEquals("Franklin",length_10);
        try {
            StringUtil.subCompletedSentence(text, -20);
        }catch (StringIndexOutOfBoundsException e){
            System.out.println(e);
        }
    }
    @Test
    public void testIsEmail(){
        Assert.assertTrue(StringUtil.isEmail("<EMAIL>"));
        Assert.assertTrue(StringUtil.isEmail("<EMAIL>"));
        Assert.assertTrue(StringUtil.isEmail("<EMAIL>"));
        Assert.assertTrue(StringUtil.isEmail("<EMAIL>"));
        Assert.assertTrue(StringUtil.isEmail("<EMAIL>"));
        Assert.assertTrue(StringUtil.isEmail("<EMAIL>"));
        Assert.assertFalse(StringUtil.isEmail("abc"));
        Assert.assertFalse(StringUtil.isEmail("abc@<EMAIL>"));
        Assert.assertFalse(StringUtil.isEmail("abc@qq"));
    }

    @Test
    public void testCombineWithSpace_Normal() {
        String result = StringUtil.combineWithSpace("aaa", "bbb");
        assertEquals("aaa bbb", result);
    }

    @Test
    public void testCombineWithSpace_SomeStrIsNullOrEmpty() {
        String result1 = StringUtil.combineWithSpace(null, "bbb");
        String result2 = StringUtil.combineWithSpace("", "bbb");
        String result3 = StringUtil.combineWithSpace("", "");
        String result4 = StringUtil.combineWithSpace(null, null);
        String result5 = StringUtil.combineWithSpace("aaa", "");
        String result6 = StringUtil.combineWithSpace("aaa", null);
        String result7 = StringUtil.combineWithSpace(" ", "bbb");
        String result8 = StringUtil.combineWithSpace("aaa", " ");

        assertEquals("bbb", result1);
        assertEquals("bbb", result2);
        assertEquals("", result3);
        assertEquals("", result4);
        assertEquals("aaa", result5);
        assertEquals("aaa", result6);
        assertEquals("bbb", result7);
        assertEquals("aaa", result8);
    }

    @Test
    public void testCombineWithSpace_SomeStrHasSpace() {
        String result1 = StringUtil.combineWithSpace(" aaa", "bbb");
        String result2 = StringUtil.combineWithSpace(" aaa", " bbb");
        String result3 = StringUtil.combineWithSpace(" aaa ", " bbb ");
        String result4 = StringUtil.combineWithSpace("aa a", "bbb");
        String result5 = StringUtil.combineWithSpace(" aa a", "bbb");

        assertEquals("aaa bbb", result1);
        assertEquals("aaa bbb", result2);
        assertEquals("aaa bbb", result3);
        assertEquals("aa a bbb", result4);
        assertEquals("aa a bbb", result5);
    }

    @Test
    public void testCombineWithUnderline() {
        String result1 = StringUtil.combineWithUnderline("aaa", "bbb");
        String result2 = StringUtil.combineWithUnderline(null, "bbb");

        assertEquals("aaa_bbb", result1);
        assertEquals("bbb", result2);
    }

    @Test
    public void testRemoveDigit() {
        assertEquals("LLD", StringUtil.removeDigits("LLD1"));
        assertEquals("LLD", StringUtil.removeDigits("LLD10"));
        assertEquals("LLD", StringUtil.removeDigits("LLD01"));
        assertEquals("LLD", StringUtil.removeDigits("LLD123"));
        assertEquals("LLD", StringUtil.removeDigits("1LLD"));
        assertEquals("LLDLLD", StringUtil.removeDigits("LLD1LLD"));
        assertEquals("LLDLLD", StringUtil.removeDigits("LLD123LLD"));
    }

    @Test
    public void testRemoveNoDigits() {
        assertEquals("1", StringUtil.removeNoDigits("LLD1"));
        assertEquals("1111", StringUtil.removeNoDigits("LLD1111"));
        assertEquals("123", StringUtil.removeNoDigits("LLD123"));
        assertEquals("123", StringUtil.removeNoDigits("LLD123DD"));
    }

    @Test
    public void testIsAContainsB(){
        String a = " ";
        String b = "";
        assertEquals(true,StringUtil.isAContainsB(a,b));
        String a1 = "AA";
        String b1 = "a";
        assertEquals(true,StringUtil.isAContainsB(a1,b1));
        String a2 = "AA";
        String b2 = "aAA";
        assertEquals(false,StringUtil.isAContainsB(a2,b2));
        String c1 = "ATL–REG";
        String c2 = "ATL–REG";
        assertEquals(true,StringUtil.isAContainsB(c1,c2));

    }

    @Test
    public void testRemoveSpacesToOne(){
        String str1 = "aaaa             b";
        assertTrue("aaaa b".equalsIgnoreCase(StringUtil.removeSpacesToOne(str1)));
        String str2 = " a    b";
        assertTrue("a b".equalsIgnoreCase(StringUtil.removeSpacesToOne(str2)));
        String str3 = " a    b ";
        assertTrue("a b".equalsIgnoreCase(StringUtil.removeSpacesToOne(str3)));
        String str4 = "   a     b    ";
        assertTrue("a b".equalsIgnoreCase(StringUtil.removeSpacesToOne(str4)));
    }

    @Test
    public void testEqualsIgnoreCaseAndSpecialChar() {
        assertTrue(StringUtil.equalsIgnoreCaseAndSpecialChar("", ""));
        assertTrue(StringUtil.equalsIgnoreCaseAndSpecialChar("ab", "ab"));
        assertTrue(StringUtil.equalsIgnoreCaseAndSpecialChar("a b", "ab"));
        assertTrue(StringUtil.equalsIgnoreCaseAndSpecialChar("ab", "a b"));
        assertTrue(StringUtil.equalsIgnoreCaseAndSpecialChar("  a    b  ", " a  b"));
        assertTrue(StringUtil.equalsIgnoreCaseAndSpecialChar("a     b", "ab    "));
        assertTrue(StringUtil.equalsIgnoreCaseAndSpecialChar("a     b", " a   b"));
        assertTrue(StringUtil.equalsIgnoreCaseAndSpecialChar(" a　　b", "a b　"));
        assertTrue(StringUtil.equalsIgnoreCaseAndSpecialChar("a　b", "ab"));
        assertTrue(StringUtil.equalsIgnoreCaseAndSpecialChar("a b", "ab"));
    }

    /**
     * 测试 containsIgnoreCase 方法
     */
    @Test
    public void testContainsIgnoreCase() {
        // 测试 list 与 target 均为 null 的情况
        String target = null;
        List<String> list = null;
        assertFalse(CollectionUtil.containsIgnoreCase(list, target));

        // 测试 list 为空数组以及 target 为 null 的情况
        list = new ArrayList<>();
        assertFalse(CollectionUtil.containsIgnoreCase(list, target));

        // 测试 list 为非空数组（元素包含空字符串）以及 target 为 null 的情况
        list.add("Learning Genie");
        list.add("");
        assertFalse(CollectionUtil.containsIgnoreCase(list, target));

        // 测试 list 为非空数组（元素包含空字符串）以及 target 为空字符串的情况
        target = "";
        assertTrue(CollectionUtil.containsIgnoreCase(list, target));

        // 测试 list 为非空数组以及 target 为字符串的情况(与 list 元素大小写相同)
        target = "Learning Genie";
        assertTrue(CollectionUtil.containsIgnoreCase(list, target));

        // 测试 list 为非空数组以及 target 为字符串的情况(与 list 元素大小写不同)
        target = "learning genie";
        assertTrue(CollectionUtil.containsIgnoreCase(list, target));
    }

    @Test
    public void testFilterDuplicate() {
        List<String> test1 = new ArrayList<String>(){{
            add("1");
            add("2");
            add("4");
        }};
        List<String> result1 = CollectionUtil.filterDuplicate(test1);
        assertEquals(3, result1.size());
        assertEquals("1", result1.get(0));
        assertEquals("2", result1.get(1));
        assertEquals("4", result1.get(2));

        List<String> test2 = new ArrayList<String>(){{
            add("1");
            add("2");
            add("2");
            add("5");
        }};
        List<String> result2 = CollectionUtil.filterDuplicate(test2);
        assertEquals(3, result2.size());
        assertEquals("1", result2.get(0));
        assertEquals("2", result2.get(1));
        assertEquals("5", result2.get(2));
    }

    @Test
    public void testFilterDuplicateIds() {
        String test1 = "a,a,b,c,d";
        assertEquals("a,b,c,d", StringUtil.filterDuplicateIds(test1));

        String test2 = "a,b,c,b,d";
        assertEquals("a,b,c,d", StringUtil.filterDuplicateIds(test2));

        String test3 = "a";
        assertEquals("a", StringUtil.filterDuplicateIds(test3));
    }

    @Test
    public void testTryParseInteger() {
        assertEquals(true, 1 == StringUtil.tryParseInteger("1"));
        assertEquals(true, 1 == StringUtil.tryParseInteger("1 "));
        assertEquals(true, 100 == StringUtil.tryParseInteger(" 100  "));
        assertEquals(true, null == StringUtil.tryParseInteger("a1"));
    }

    @Test
    public void testEmail() {
        assertEquals(true, StringUtil.isEmail("<EMAIL>"));
        assertEquals(true, StringUtil.isEmail("<EMAIL>"));
        assertEquals(true, StringUtil.isEmail("ab'<EMAIL>"));
        assertEquals(true, StringUtil.isEmail("ab'<EMAIL>"));
        assertEquals(true, StringUtil.isEmail("ab'<EMAIL>"));
        assertEquals(true, StringUtil.isEmail("AB'<EMAIL>"));
        assertEquals(true, StringUtil.isEmail("ab32'<EMAIL>"));
        assertEquals(true, StringUtil.isEmail("ab'<EMAIL>"));
    }

    @Test
    public void testTitleChangeBookName() {
        String[] titles = {"'We're Going on a Bear Hunt' by Michael Rosen and Helen Oxenbury"
                ,"Where The Wild Things Are Audio Book"
                ,"Knuffle Bunny- A Cautionary Tale by Moe Williams. narrated by Melody Rohlfs"
                ,"No, David! - Animated Children's Book"
                ,"The Cat In The Hat (Read Aloud)"
                ,"Skippyjon Jones by Judy Schachner"
                ,"Llama Llama Red Pajama | Children's Books Read Aloud"
                ,"Pete the Cat: I Love My White Shoes | Read Aloud"
                ,"Green Eggs and Ham By Dr Seuss Books for kids read aloud!"
                ,"I Ain't Gonna Paint No More!"
                ,"Fall Book - Bear Wants More | Autumn Kids Book Read Aloud"
                ,"THE NIGHT BEFORE CHRISTMAS Classic Edition by Clement Moore Read Aloud ~ Christmas Books for Kids"
        };
        for (String title : titles) {
            String bookName = StringUtil.titleChangeBookName(title);
            System.out.println(bookName);
        }

    }
    @Test
    public void testConvertIdsToString() {
        List<String> list = new LinkedList<>();
        list.add(null);
        assertEquals("", StringUtil.convertIdsToString(list));
        list.add("1");
        assertEquals("'1'", StringUtil.convertIdsToString(list));
        list.add("3");
        assertEquals("'1','3'", StringUtil.convertIdsToString(list));
        list.add("2");
        assertEquals("'1','3','2'", StringUtil.convertIdsToString(list));
        list.add("a");
        assertEquals("'1','3','2','a'", StringUtil.convertIdsToString(list));
        list.add("d'a");
        assertEquals("'1','3','2','a','d''a'", StringUtil.convertIdsToString(list));
        list.add("");
        assertEquals("'1','3','2','a','d''a',''", StringUtil.convertIdsToString(list));
    }

    @Test
    public void testConvertIdsToStringSteam() {
        List<String> list = new LinkedList<>();
        list.add(null);
        assertEquals("", StringUtil.convertIdsToString(list.stream()));
        list.add("1");
        assertEquals("'1'", StringUtil.convertIdsToString(list.stream()));
        list.add("3");
        assertEquals("'1','3'", StringUtil.convertIdsToString(list.stream()));
        list.add("2");
        assertEquals("'1','3','2'", StringUtil.convertIdsToString(list.stream()));
        list.add("a");
        assertEquals("'1','3','2','a'", StringUtil.convertIdsToString(list.stream()));
        list.add("d'a");
        assertEquals("'1','3','2','a','d''a'", StringUtil.convertIdsToString(list.stream()));
        list.add("");
        assertEquals("'1','3','2','a','d''a',''", StringUtil.convertIdsToString(list.stream()));
    }
    @Test
    public void testIsTrue() {
        // 覆盖第一个if分支
        String value1 = null;
        assertFalse(StringUtil.isTrue(value1));
        value1 = "";
        assertFalse(StringUtil.isTrue(value1));
        value1 = "  ";
        assertFalse(StringUtil.isTrue(value1));

        // 覆盖第二个if分支
        String value2 = "true";
        assertTrue(StringUtil.isTrue(value2));
        value2 = "1";
        assertTrue(StringUtil.isTrue(value2));
        value2 = "yes";
        assertTrue(StringUtil.isTrue(value2));
        value2 = "YES";
        assertTrue(StringUtil.isTrue(value2));
        value2 = "yEs";
        assertTrue(StringUtil.isTrue(value2));
        value2 = "TRUE";
        assertTrue(StringUtil.isTrue(value2));
        value2 = "TrUe";
        assertTrue(StringUtil.isTrue(value2));

        // 覆盖第三个if分支
        String value3 = "true;1";
        assertFalse(StringUtil.isTrue(value3));
        value3 = "1;yes";
        assertFalse(StringUtil.isTrue(value3));
        value3 = "yes;yes";
        assertFalse(StringUtil.isTrue(value3));
    }

    /**
     * 测试 {@code removeEmptyAndDuplicates} 方法
     * 验证正常字符串的情况
     */
    @Test
    public void testRemoveEmptyAndDuplicates() {
        String input = "4ED0392C-55D3-EB11-9C19-4CCC6ACF6129,54D0392C-55D3-EB11-9C19-4CCC6ACF6129,54D0392C-55D3-EB11-9C19-4CCC6ACF6129,85D0392C-55D3-EB11-9C19-4CCC6ACF6129";
        String result = StringUtil.removeEmptyAndDuplicates(input, ",");
        assertEquals("4ED0392C-55D3-EB11-9C19-4CCC6ACF6129,54D0392C-55D3-EB11-9C19-4CCC6ACF6129,85D0392C-55D3-EB11-9C19-4CCC6ACF6129", result);
    }

    /**
     * 测试 {@code removeEmptyAndDuplicates} 方法
     * 验证输入的字符串为空的情况
     */
    @Test
    public void testRemoveEmptyAndDuplicatesWithEmptyInput() {
        String input = ""; // 字符串为空字符串
        String result = StringUtil.removeEmptyAndDuplicates(input, ",");
        assertEquals("", result); // 验证返回值为空字符串

        input = null; // 字符串为 null
        result = StringUtil.removeEmptyAndDuplicates(input, ",");
        assertNull(result); // 验证返回值为 null
    }

    /**
     * 测试 {@code removeEmptyAndDuplicates} 方法
     * 验证输入包含空值的情况
     */
    @Test
    public void testRemoveEmptyAndDuplicatesWithEmptyValues() {
        // 字符串包含空值的情况
        String input = "4ED0392C-55D3-EB11-9C19-4CCC6ACF6129, ,54D0392C-55D3-EB11-9C19-4CCC6ACF6129,85D0392C-55D3-EB11-9C19-4CCC6ACF6129,85D0392C-55D3-EB11-9C19-4CCC6ACF6129,";
        String result = StringUtil.removeEmptyAndDuplicates(input, ",");
        assertEquals("4ED0392C-55D3-EB11-9C19-4CCC6ACF6129,54D0392C-55D3-EB11-9C19-4CCC6ACF6129,85D0392C-55D3-EB11-9C19-4CCC6ACF6129", result);
    }

    /**
     * 测试 {@code removeEmptyAndDuplicates} 方法
     * 验证使用不同的分隔符的情况
     */
    @Test
    public void testRemoveEmptyAndDuplicatesWithDifferentDelimiter() {
        // 字符串使用分号分隔的情况
        String input = "4ED0392C-55D3-EB11-9C19-4CCC6ACF6129;4ED0392C-55D3-EB11-9C19-4CCC6ACF6129;54D0392C-55D3-EB11-9C19-4CCC6ACF6129;85D0392C-55D3-EB11-9C19-4CCC6ACF6129";
        String result = StringUtil.removeEmptyAndDuplicates(input, ";");
        assertEquals("4ED0392C-55D3-EB11-9C19-4CCC6ACF6129;54D0392C-55D3-EB11-9C19-4CCC6ACF6129;85D0392C-55D3-EB11-9C19-4CCC6ACF6129", result);

        // 字符串使用空格分隔的情况
        input = "4ED0392C-55D3-EB11-9C19-4CCC6ACF6129 85D0392C-55D3-EB11-9C19-4CCC6ACF6129 54D0392C-55D3-EB11-9C19-4CCC6ACF6129 85D0392C-55D3-EB11-9C19-4CCC6ACF6129";
        result = StringUtil.removeEmptyAndDuplicates(input, " ");
        assertEquals("4ED0392C-55D3-EB11-9C19-4CCC6ACF6129 85D0392C-55D3-EB11-9C19-4CCC6ACF6129 54D0392C-55D3-EB11-9C19-4CCC6ACF6129", result);
    }

    @Test
    public void testGetStandardName() {
        // 测试英文名字
        String name = "John Doe";
        String standardName = StringUtil.getStandardName(name); // 调用方法
        assertEquals("John Doe", standardName); // 验证返回值应该是 "John Doe"

        // 测试英文名带符号
        name = "John Doe, Jr.";
        standardName = StringUtil.getStandardName(name); // 调用方法
        assertEquals("John Doe Jr", standardName); // 验证返回值应该是 "John Doe Jr"

        // 测试因为名字带有表情
        name = "John Doe 😊";
        standardName = StringUtil.getStandardName(name); // 调用方法
        assertEquals("John Doe", standardName); // 验证返回值应该是 "John Doe"

        // 测试英文名带有特殊字符
        name = "John$ Doe & Jane *^%(Doe)@";
        standardName = StringUtil.getStandardName(name); // 调用方法
        assertEquals("John Doe Jane Doe", standardName); // 验证返回值应该是 "John Doe Jane Doe"

        // 测试英文名带有数字
        name = "John  Doe 123";
        standardName = StringUtil.getStandardName(name); // 调用方法
        assertEquals("John Doe 123", standardName); // 验证返回值应该是 "John Doe 123"

        // 测试西班牙语名字
        name = "José García";
        standardName = StringUtil.getStandardName(name); // 调用方法
        assertEquals("José García", standardName); // 验证返回值应该是 "José García"

        // 测试西班牙语名字带有表情
        name = "José\uD83D\uDE00\uD83D\uDE0F García 😀";
        standardName = StringUtil.getStandardName(name); // 调用方法
        assertEquals("José García", standardName); // 验证返回值应该是 "José García"

        // 测试西班牙语名字带有西班牙语常用标点
        name = "José García, María";
        standardName = StringUtil.getStandardName(name); // 调用方法
        assertEquals("José García María", standardName); // 验证返回值应该是 "José García María"


        // 测试西班牙语名字带有特殊字符
        name = "José }}{{||García && María";
        standardName = StringUtil.getStandardName(name); // 调用方法
        assertEquals("José García María", standardName); // 验证返回值应该是 "José García María"

        // 测试西班牙语名字带有数字
        name = "José García 123";
        standardName = StringUtil.getStandardName(name); // 调用方法
        assertEquals("José García 123", standardName); // 验证返回值应该是 "José García 123"
    }

    /**
     * 测试判断是否是测试邮件方法
     */
    @Test
    public void testIsTestEmail() {
        // 测试邮箱地址
        assertTrue(StringUtil.isTestEmail("<EMAIL>"));
        assertTrue(StringUtil.isTestEmail("<EMAIL>"));
        assertTrue(StringUtil.isTestEmail("<EMAIL>"));
        assertTrue(StringUtil.isTestEmail("<EMAIL>"));
        assertTrue(StringUtil.isTestEmail("<EMAIL>"));
        assertTrue(StringUtil.isTestEmail("43@e2e_api.com"));
        assertTrue(StringUtil.isTestEmail("<EMAIL>"));
        assertTrue(StringUtil.isTestEmail("<EMAIL>"));
        assertTrue(StringUtil.isTestEmail("<EMAIL>"));
        assertTrue(StringUtil.isTestEmail("<EMAIL>"));
        assertTrue(StringUtil.isTestEmail("<EMAIL>"));
        assertTrue(StringUtil.isTestEmail("<EMAIL>"));
        assertTrue(StringUtil.isTestEmail("<EMAIL>"));
        assertTrue(StringUtil.isTestEmail("<EMAIL>"));
        assertTrue(StringUtil.isTestEmail("<EMAIL>"));
        assertTrue(StringUtil.isTestEmail("<EMAIL>"));
        assertTrue(StringUtil.isTestEmail("<EMAIL>"));
        assertTrue(StringUtil.isTestEmail("<EMAIL>"));
        assertTrue(StringUtil.isTestEmail("<EMAIL>"));
        assertTrue(StringUtil.isTestEmail("<EMAIL>"));
        assertTrue(StringUtil.isTestEmail("<EMAIL>"));
        assertTrue(StringUtil.isTestEmail("test@leaning-genie-test"));
        assertTrue(StringUtil.isTestEmail("<EMAIL>"));
        assertTrue(StringUtil.isTestEmail("<EMAIL>"));
        assertTrue(StringUtil.isTestEmail("<EMAIL>"));
        assertTrue(StringUtil.isTestEmail("<EMAIL>"));
        assertTrue(StringUtil.isTestEmail("<EMAIL>"));
        assertTrue(StringUtil.isTestEmail("<EMAIL>"));
        assertTrue(StringUtil.isTestEmail("<EMAIL>"));
        assertTrue(StringUtil.isTestEmail("<EMAIL>"));
        assertTrue(StringUtil.isTestEmail("user@123.45"));
        assertTrue(StringUtil.isTestEmail("<EMAIL>"));
        assertTrue(StringUtil.isTestEmail("<EMAIL>"));
        assertTrue(StringUtil.isTestEmail("<EMAIL>"));
        assertTrue(StringUtil.isTestEmail("<EMAIL>"));
        assertTrue(StringUtil.isTestEmail("<EMAIL>"));
        assertTrue(StringUtil.isTestEmail("<EMAIL>"));
        assertTrue(StringUtil.isTestEmail("<EMAIL>"));
        assertTrue(StringUtil.isTestEmail("<EMAIL>"));
        assertTrue(StringUtil.isTestEmail("<EMAIL>"));
        assertTrue(StringUtil.isTestEmail("<EMAIL>"));
        assertTrue(StringUtil.isTestEmail("<EMAIL>"));
        assertTrue(StringUtil.isTestEmail("<EMAIL>"));
        assertTrue(StringUtil.isTestEmail("<EMAIL>"));
        assertTrue(StringUtil.isTestEmail("<EMAIL>"));
        assertTrue(StringUtil.isTestEmail("<EMAIL>"));
        assertTrue(StringUtil.isTestEmail("<EMAIL>"));
        assertTrue(StringUtil.isTestEmail("<EMAIL>"));

        // 非测试邮箱地址
        assertFalse(StringUtil.isTestEmail("<EMAIL>"));
        assertFalse(StringUtil.isTestEmail("<EMAIL>"));
        assertFalse(StringUtil.isTestEmail("<EMAIL>"));
        assertFalse(StringUtil.isTestEmail("<EMAIL>"));
        assertFalse(StringUtil.isTestEmail("<EMAIL>"));
        assertFalse(StringUtil.isTestEmail("<EMAIL>"));
        assertFalse(StringUtil.isTestEmail("<EMAIL>"));
        assertFalse(StringUtil.isTestEmail("<EMAIL>"));
        assertFalse(StringUtil.isTestEmail("<EMAIL>"));

        // 空邮箱地址
        assertFalse(StringUtil.isTestEmail(null));
        assertFalse(StringUtil.isTestEmail(""));
    }

    @Test
    public void testIsHTML() {
        // 测试不是 HTML 的情况
        assertFalse(StringUtil.isHTML(null));
        assertFalse(StringUtil.isHTML(""));
        assertFalse(StringUtil.isHTML("This is a test string."));
        assertFalse(StringUtil.isHTML("This is a test <> string."));
        assertFalse(StringUtil.isHTML("<>"));
        assertFalse(StringUtil.isHTML("</>"));
        // 测试是 HTML 的情况
        assertTrue(StringUtil.isHTML("This is a test string. <p>"));
        assertTrue(StringUtil.isHTML("<p>This is a paragraph.</p>"));
        assertTrue(StringUtil.isHTML("<p>This is a paragraph."));
        assertTrue(StringUtil.isHTML("This is a paragraph.</p>"));
        assertTrue(StringUtil.isHTML("<p/>"));
        assertTrue(StringUtil.isHTML("<p>"));
        assertTrue(StringUtil.isHTML("</p>"));
        assertTrue(StringUtil.isHTML("<p></p>"));
        assertTrue(StringUtil.isHTML("<p a=\"\">"));
        assertTrue(StringUtil.isHTML("<p a=\"\"></p>"));
        assertTrue(StringUtil.isHTML("<p a=\"\" />"));
        assertTrue(StringUtil.isHTML("<p b>"));
        assertTrue(StringUtil.isHTML("<p b></p>"));
        assertTrue(StringUtil.isHTML("<p b />"));
        assertTrue(StringUtil.isHTML("<p>This is a paragraph.</p> <p>This is another paragraph.</p>"));
        assertTrue(StringUtil.isHTML("<!DOCTYPE html><html><head><title>Test</title></head><body><p>Test paragraph.</p></body></html>"));
        assertTrue(StringUtil.isHTML("<!DOCTYPE html>aaaaaa"));
    }

    /**
     * 测试 SubstringUnicode 方法
     */
    @Test
    public void testSubstringUnicode() {
        // 测试空字符串
        assertEquals("", StringUtil.substringUnicode("", 0, 0));
        // 测试索引超过字符串情况
        assertEquals("abc", StringUtil.substringUnicode("abc", 3, 4));
        assertEquals("abc", StringUtil.substringUnicode("abc", 0, 3));
        // 测试字符串有 emoji 的情况
        assertEquals("大苏打😊", StringUtil.substringUnicode("大苏打😊s😊111", 0, 4));
        assertEquals("😊", StringUtil.substringUnicode("😊", 0, 2));
        // 测试正常情况
        assertEquals("大苏打😊ss😊1", StringUtil.substringUnicode("大苏打😊ss😊111", 0, 8));
        assertEquals("😊ss😊1", StringUtil.substringUnicode("大苏打😊ss😊111", 3, 8));
        assertEquals("花开时节谁共赏，", StringUtil.substringUnicode("花开时节谁共赏，孤影自怜月下长。", 0, 8));
    }

    /**
     * 测试 {@code removeSpaceAndFullPitch} 方法
     */
    @Test
    public void testRemoveSpaceAndFullPitch() {
        // 测试 null 输入
        assertNull(StringUtil.removeSpaceAndFullPitch(null));

        // 测试空字符串
        assertEquals("", StringUtil.removeSpaceAndFullPitch(""));

        // 测试只有空格的字符串
        assertEquals("", StringUtil.removeSpaceAndFullPitch("     "));

        // 测试只有制表符的字符串
        assertEquals("", StringUtil.removeSpaceAndFullPitch("\t\t\t"));

        // 测试混合空格和制表符
        assertEquals("HelloWorld", StringUtil.removeSpaceAndFullPitch(" Hello\t World \t"));

        // 测试包含全角空格的字符串
        assertEquals("HelloWorld", StringUtil.removeSpaceAndFullPitch("Hello\u3000World"));

        // 测试混合全角和半角字符
        assertEquals("HelloWorld123", StringUtil.removeSpaceAndFullPitch("Hello\u3000World 123"));
        assertEquals("HelloWorld123", StringUtil.removeSpaceAndFullPitch("Hello World 123"));

        // 测试复杂字符串输入
        assertEquals("HelloWo@rld123", StringUtil.removeSpaceAndFullPitch(" Hello\tWo@rld123 "));

        // 测试包含全角字符的字符串
        // 假设 sbc2dbcCase 方法能正确将全角字符转换为半角
        assertEquals("abc123", StringUtil.removeSpaceAndFullPitch("ａｂｃ１２３"));

        // 测试混合内容
        assertEquals("abc123HelloWorld", StringUtil.removeSpaceAndFullPitch("ａｂｃ１２３ Hello\tWorld "));
    }

    @Test
    public void testReplace() {
        StringBuilder builder;

        // 测试 builder 为 ""
        builder = new StringBuilder();
        StringUtil.replace(builder, "", "word");
        Assert.assertEquals("", builder.toString());

        // 测试目标字符串为 null
        builder = new StringBuilder("Hello Word.");
        StringUtil.replace(builder, null, "word");
        Assert.assertEquals("Hello Word.", builder.toString());

        // 测试目标字符串为 ""
        builder = new StringBuilder("Hello Word.");
        StringUtil.replace(builder, "", "word");
        Assert.assertEquals("Hello Word.", builder.toString());

        // 测试目标字符串为 " "
        builder = new StringBuilder("Hello   Word.");
        StringUtil.replace(builder, " ", "word");
        Assert.assertEquals("HellowordwordwordWord.", builder.toString());

        // 测试替换字符串为 null
        builder = new StringBuilder("Hello Word.");
        StringUtil.replace(builder, "Hello", null);
        Assert.assertEquals("Hello Word.", builder.toString());

        // 测试替换字符串为 ""
        builder = new StringBuilder("Hello Word.");
        StringUtil.replace(builder, "Hello", "");
        Assert.assertEquals(" Word.", builder.toString());

        // 测试替换字符串为 " "
        builder = new StringBuilder("Hello Word.");
        StringUtil.replace(builder, "Hello", " ");
        Assert.assertEquals("  Word.", builder.toString());

        // 测试不包含替换目标的情况
        builder = new StringBuilder("Hello Word.");
        StringUtil.replace(builder, "www", "word");
        Assert.assertEquals("Hello Word.", builder.toString());

        // 测试包含多个替换目标的情况
        builder = new StringBuilder("Hello Word. Hello Word.");
        StringUtil.replace(builder, "Word", "world");
        Assert.assertEquals("Hello world. Hello world.", builder.toString());

        // 测试替换字符串中包含目标字符串的情况
        builder = new StringBuilder("{a}");
        StringUtil.replace(builder, "{a}", "aaa{a}");
        Assert.assertEquals("aaa{a}", builder.toString());

        // 测试替换字符串中包含多个目标字符串的情况
        builder = new StringBuilder("{a}");
        StringUtil.replace(builder, "{a}", "aaa{a}{a}");
        Assert.assertEquals("aaa{a}{a}", builder.toString());

        // 测试目标字符串中包含替换字符串的情况
        builder = new StringBuilder("aaa{a}");
        StringUtil.replace(builder, "aaa{a}", "{a}");
        Assert.assertEquals("{a}", builder.toString());

        // 测试目标字符串中包含多个替换字符串的情况
        builder = new StringBuilder("aaa{a}{a}");
        StringUtil.replace(builder, "aaa{a}{a}", "{a}");
        Assert.assertEquals("{a}", builder.toString());

        // 测试正常情况
        builder = new StringBuilder("Hello word.");
        StringUtil.replace(builder, "word", "world");
        Assert.assertEquals("Hello world.", builder.toString());
    }

}
