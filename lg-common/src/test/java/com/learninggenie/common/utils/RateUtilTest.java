package com.learninggenie.common.utils;

import org.junit.Test;
import org.junit.jupiter.api.Assertions;

import java.util.HashMap;
import java.util.Map;


public class RateUtilTest {

    /**
     * 测试 isNotITCOrITEFramework 方法
     */
    @Test
    public void testIsNotITCOrITEFramework() {
        // 数据准备
        String frameworkId = "F00001";
        // 方法模拟
        boolean result1 = RateUtil.isNotITCOrITEFramework(null);
        boolean result2 = RateUtil.isNotITCOrITEFramework(frameworkId);

        // 验证
        Assertions.assertEquals(false, result1);
        Assertions.assertEquals(true, result2);

    }


    /**
     * 测试 hasELD 方法
     */
    @Test
    public void testHasELD() {
        // 数据准备
        String childId = "C00001";
        Map<String, Map<String, Boolean>> childIdToIepAndEldMap = new HashMap<>();
        Map<String, Boolean> eldMap = new HashMap<>();
        eldMap.put("ELD", true);
        eldMap.put("IEP/IFSP", true);
        childIdToIepAndEldMap.put(childId, eldMap);

        // 方法模拟
        boolean result1 = RateUtil.hasELD(childIdToIepAndEldMap, childId);
        boolean result2 = RateUtil.hasELD(null, childId);
        boolean result3 = RateUtil.hasELD(childIdToIepAndEldMap, null);
        boolean result4 = RateUtil.hasELD(childIdToIepAndEldMap, "C00003");

        // 验证
        Assertions.assertEquals(true, result1);
        Assertions.assertEquals(false, result2);
        Assertions.assertEquals(false, result3);
        Assertions.assertEquals(false, result4);

    }

    /**
     * 测试 hasIEP 方法
     */
    @Test
    public void testHasIEP() {
        // 数据准备
        String childId = "C00002";
        Map<String, Map<String, Boolean>> childIdToIepAndEldMap = new HashMap<>();
        Map<String, Boolean> eldMap = new HashMap<>();
        eldMap.put("ELD", true);
        eldMap.put("IEP/IFSP", true);
        childIdToIepAndEldMap.put(childId, eldMap);

        // 方法模拟
        boolean result1 = RateUtil.hasIEP(childIdToIepAndEldMap, childId);
        boolean result2 = RateUtil.hasIEP(null, childId);
        boolean result3 = RateUtil.hasIEP(childIdToIepAndEldMap, null);
        boolean result4 = RateUtil.hasIEP(childIdToIepAndEldMap, "C00004");

        // 验证
        Assertions.assertEquals(true, result1);
        Assertions.assertEquals(false, result2);
        Assertions.assertEquals(false, result3);
        Assertions.assertEquals(false, result4);
    }

    @Test
    public void testIsPsfFramework_notPsf() {
        boolean result = RateUtil.isPSFFramework(null);
        Assertions.assertFalse(result);
    }

    @Test
    public void testIsPsfFramework_Psf() {
        boolean result = RateUtil.isPSFFramework(RateUtil.FRAMEWORK_PSF_ID);
        Assertions.assertTrue(result);
    }

    /**
     * 测试是否是 IL CCSS-K 框架方法
     * Case: frameworkId 为 null， 返回 false
     */
    @Test
    public void testIsILCCSSKFramework1() {
        boolean result = RateUtil.isILCCSSKFramework(null);
        Assertions.assertFalse(result);
    }

    /**
     * 测试是否是 IL CCSS-K 框架方法
     * Case: frameworkId 为 IL CCSS-K 框架 ID，返回 true
     */
    @Test
    public void testIsILCCSSKFramework2() {
        boolean result = RateUtil.isILCCSSKFramework("E83EB44A-BD11-4003-B32A-79B17065A408");
        Assertions.assertTrue(result);
    }

    /**
     * 测试是否是 IL CCSS-K 框架方法
     * Case: frameworkId 为其他框架 ID，返回 false
     */
    @Test
    public void testIsILCCSSKFramework3() {
        boolean result = RateUtil.isILCCSSKFramework("E83EB44A-BD11-4003-B32A-79B17065A409");
        Assertions.assertFalse(result);
    }

    /**
     * 测试过滤测评点字符 v3
     */
    @Test
    public void testFilterMeasureCharsV3() {
        String filterMeasure = RateUtil.filterMeasureCharsV3("4.b");
        Assertions.assertEquals("4.B", filterMeasure);
    }
}