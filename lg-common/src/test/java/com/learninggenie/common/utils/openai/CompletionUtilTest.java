package com.learninggenie.common.utils.openai;

import com.learninggenie.common.data.entity.lesson2.LessonEntity;
import com.learninggenie.common.data.model.openai.completion.chat.ChatCompletionChunk;
import com.learninggenie.common.data.model.openai.completion.chat.ChatCompletionChunkChoice;
import com.learninggenie.common.data.model.openai.completion.chat.ChatCompletionResult;
import com.learninggenie.common.data.model.openai.parse.ParseField;
import com.theokanning.openai.completion.chat.ChatCompletionChoice;
import com.theokanning.openai.completion.chat.ChatMessage;
import org.checkerframework.checker.units.qual.A;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;


public class CompletionUtilTest {

    @Test
    public void testIsChatCompletionChunkEnd() {
        // 入参
        List<ChatCompletionChunk> chunks = new ArrayList<>();
        ChatCompletionChunk chunk = new ChatCompletionChunk();
        chunk.setEnd(true);
        chunks.add(chunk);
        // 调用方法
        boolean chunkEnd = CompletionUtil.isChatCompletionChunkEnd(chunks);
        // 断言
        Assertions.assertTrue(chunkEnd);
    }

    @Test
    public void testIsChatCompletionChunkEnd2() {
        // 入参
        List<ChatCompletionChunk> chunks = new ArrayList<>();
        ChatCompletionChunk chunk = new ChatCompletionChunk();
        chunk.setEnd(false);
        chunks.add(chunk);
        // 调用方法
        boolean chunkEnd = CompletionUtil.isChatCompletionChunkEnd(chunks);
        // 断言
        Assertions.assertFalse(chunkEnd);
    }

    @Test
    public void testMergeChatCompletionChunks() {
        // 入参
        List<ChatCompletionChunk> chunks = new ArrayList<>();
        ChatCompletionChunk chunk = new ChatCompletionChunk();
        List<ChatCompletionChunkChoice> choices = new ArrayList<>();
        ChatCompletionChunkChoice choice = new ChatCompletionChunkChoice();
        ChatMessage message = new ChatMessage();
        message.setContent("abc");
        choice.setDelta(message);
        choices.add(choice);
        chunk.setChoices(choices);
        chunks.add(chunk);
        ChatCompletionChunk chunk2 = new ChatCompletionChunk();
        chunks.add(chunk2);
        ChatCompletionChunk chunk3 = new ChatCompletionChunk();
        List<ChatCompletionChunkChoice> choices2 = new ArrayList<>();
        ChatCompletionChunkChoice choice2 = new ChatCompletionChunkChoice();
        ChatMessage message2 = new ChatMessage();
        choice2.setDelta(message2);
        choices2.add(choice2);
        chunk3.setChoices(choices2);
        chunks.add(chunk3);
        // 调用方法
        String text = CompletionUtil.mergeChatCompletionChunks(chunks);
        // 断言
        Assertions.assertNotNull(text);
        // 断言数据
        Assertions.assertEquals("^abc", text);
    }

    @Test
    public void testDecodeChatCompletionContents() {
        // 入参
        List<String> contents = new ArrayList<>();
        contents.add("Hello, world!");
        contents.add("^This is a test^\\n");
        contents.add(null);
        // 调用方法
        String result = CompletionUtil.decodeChatCompletionContents(contents);
        // 断言
        Assertions.assertNotNull(result);
        // 断言数据
        Assertions.assertEquals("Hello, world!This is a test^\n", result);
    }

    @Test
    public void parseChatCompletionChunks() {
        String content = "{\n" +
                "\t\"error\": {\n" +
                "\t\t\"message\": \"message\",\n" +
                "\t\t\"type\": \"type\",\n" +
                "\t\t\"param\": \"param\",\n" +
                "\t\t\"code\": \"500\"\n" +
                "\t},\n" +
                "\t\"code\": 500,\n" +
                "\t\"message\": \"Internal server error\"\n" +
                "}";
        List<ChatCompletionChunk> chunks = CompletionUtil.parseChatCompletionChunks(content, null);
        Assertions.assertNotNull(chunks);
    }

    @Test
    public void parseChatCompletionChunks2() {
        String content = "data: {\"id\":\"John\", \"object\":\"John\", \"created\":30, \"model\":\"John\", \"end\":true} \n";
        List<ChatCompletionChunk> chunks = CompletionUtil.parseChatCompletionChunks(content, null);
        Assertions.assertNotNull(chunks);
    }

    @Test
    public void parseChatCompletionChunks3() {
        String content = "data: {\"id\":\"John\", \"object\":\"John\", \"created\":30, \"model\":\"John\", \"end\":false} \n";
        List<ChatCompletionChunk> chunks = CompletionUtil.parseChatCompletionChunks(content, null);
        Assertions.assertNotNull(chunks);
        // 断言数据
        Assertions.assertEquals(1, chunks.size());
        Assertions.assertFalse(chunks.get(0).isEnd());
        Assertions.assertEquals("John", chunks.get(0).getId());
    }


    @Test
    public void parseChatCompletionChunk() {
        String line = "data: {\"id\":\"John\", \"object\":\"John\", \"created\":30, \"model\":\"John\", \"end\":true}";
        ChatCompletionChunk chunk = CompletionUtil.parseChatCompletionChunk(line, null);
        Assertions.assertNotNull(chunk);
        // 断言数据
        Assertions.assertTrue(chunk.isEnd());
        Assertions.assertEquals("John", chunk.getId());
        Assertions.assertEquals("John", chunk.getObject());
    }

    @Test
    public void parseChatCompletionChunk2() {
        String doneLine = "[DONE]";
        ChatCompletionChunk chunk = CompletionUtil.parseChatCompletionChunk(doneLine, null);
        Assertions.assertNotNull(chunk);
        // 断言数据
        Assertions.assertTrue(chunk.isEnd());
    }

    /**
     * 测试解析 GPT 响应信息
     * Case: 数据为空的情况
     */
    @Test
    public void parseChatCompletionChunkWithEmptyData() {
        // 执行测试方法
        ChatCompletionChunk chunk = CompletionUtil.parseChatCompletionChunk(null, null);
        // 验证结果
        Assertions.assertNull(chunk); // 结果为空
    }

    /**
     * 测试解析 GPT 响应信息
     * Case: 解析出错，且没有缓存响应信息的情况
     */
    @Test
    public void parseChatCompletionChunkWithParseErrorAndNoCache() {
        // 模拟 Json 格式错误的数据
        String errorJson = "{\"content\": \"a"; // 错误 JSON
        String line = "data: " + errorJson; // 行数据
        StringBuffer responseCache = new StringBuffer(); // 缓存信息
        // 执行测试方法
        ChatCompletionChunk chunk = CompletionUtil.parseChatCompletionChunk(line, responseCache);
        // 验证结果
        Assertions.assertNull(chunk); // 结果为空
        Assertions.assertNotEquals(0, responseCache.length()); // 缓存不为空
        Assertions.assertEquals(errorJson, responseCache.toString()); // 缓存内容为错误 JSON
    }

    /**
     * 测试解析 GPT 响应信息
     * Case: 解析出错，且已有缓存响应信息的情况
     */
    @Test
    public void parseChatCompletionChunkWithParseErrorAndHasCache() {
        // 模拟 Json 格式错误的数据
        String errorJson = "{\"content\": \"a"; // 错误 JSON
        String line = "data: " + errorJson; // 行数据
        StringBuffer responseCache = new StringBuffer(); // 缓存信息
        responseCache.append("abc"); // 已有缓存信息
        // 执行测试方法
        ChatCompletionChunk chunk = CompletionUtil.parseChatCompletionChunk(line, responseCache);
        // 验证结果
        Assertions.assertNull(chunk); // 结果为空
        Assertions.assertEquals(0, responseCache.length()); // 缓存为空
    }

    @Test
    public void testParseCompletionObject() {
        // 要解析的内容
        String content = " Name: ABC \n" +
                "Objectives: \n" +
                "a\n" +
                "b\n" +
                "c\n" +
                "Materials: 1\n" +
                "2\n" +
                "3";
        // 字段映射关系
        List<ParseField> fields = new ArrayList<>();
        fields.add(new ParseField("name", new String[]{"Name"}));
        fields.add(new ParseField("objectives", new String[]{"Objectives", "Objective"}));
        fields.add(new ParseField("materials", new String[]{"Materials"}));
        // 解析
        LessonEntity lesson = CompletionUtil.parseCompletionObject(content, fields, LessonEntity.class);
        // 断言
        Assertions.assertNotNull(lesson);
        Assertions.assertEquals(lesson.getName(), "ABC");
        Assertions.assertEquals(lesson.getObjectives(), "a\nb\nc");
        Assertions.assertEquals(lesson.getMaterials(), "1\n2\n3");
    }

    @Test
    public void testParseCompletionList() {
        // 要解析的内容
        String content = " Name: ABC \n" +
                "Objectives: \n" +
                "a\n" +
                "b\n" +
                "c\n" +
                "Materials: 1\n" +
                "2\n" +
                "3";
        // 字段映射关系
        List<ParseField> fields = new ArrayList<>();
        fields.add(new ParseField("name", new String[]{"Name"}));
        fields.add(new ParseField("objectives", new String[]{"Objectives", "Objective"}));
        fields.add(new ParseField("materials", new String[]{"Materials"}));
        // 解析
        List<LessonEntity> lessonEntities = CompletionUtil.parseCompletionList(content, fields, LessonEntity.class);
        // 断言
        Assertions.assertNotNull(lessonEntities);
        // 断言数据
        Assertions.assertEquals(1, lessonEntities.size());
        Assertions.assertEquals(lessonEntities.get(0).getName(), "ABC");
        Assertions.assertEquals(lessonEntities.get(0).getObjectives(), "a\nb\nc");
    }

    @Test
    public void testGetCompletionContent() {
        ChatCompletionResult completionResult = null;
        String content = CompletionUtil.getCompletionContent(completionResult);
        Assertions.assertNull(content);
    }

    @Test
    public void testGetCompletionContent2() {
        ChatCompletionResult completionResult = new ChatCompletionResult();
        String content = CompletionUtil.getCompletionContent(completionResult);
        Assertions.assertNull(content);
    }

    @Test
    public void testGetCompletionContent3() {
        ChatCompletionResult completionResult = new ChatCompletionResult();
        List<ChatCompletionChoice> choices = new ArrayList<>();
        choices.add(null);
        completionResult.setChoices(choices);
        String content = CompletionUtil.getCompletionContent(completionResult);
        Assertions.assertNull(content);
    }

    @Test
    public void testGetCompletionContent4() {
        ChatCompletionResult completionResult = new ChatCompletionResult();
        List<ChatCompletionChoice> choices = new ArrayList<>();
        ChatCompletionChoice choice = new ChatCompletionChoice();
        ChatMessage message = new ChatMessage();
        message.setContent("content");
        choice.setMessage(message);
        choices.add(choice);
        completionResult.setChoices(choices);
        String content = CompletionUtil.getCompletionContent(completionResult);
        Assertions.assertNotNull(content);
        // 断言数据
        Assertions.assertEquals("content", content);
    }

    /**
     * 测试解析评分
     */
    @Test
    public void testParseEvaluateScore() {
        // 要解析的内容
        String content = "Total Score: 90/100";
        // 调用方法
        Double score = CompletionUtil.parseEvaluateScore(content);
        // 断言数据
        Assertions.assertNotNull(score);
        Assertions.assertEquals(90, score.intValue());
    }

    /**
     * 测试解析评分
     */
    @Test
    public void testParseEvaluateScore2() {
        // 要解析的内容
        String content = "Total Score: 90";
        // 调用方法
        Double score = CompletionUtil.parseEvaluateScore(content);
        // 断言数据
        Assertions.assertNotNull(score);
        Assertions.assertEquals(90, score.intValue());
    }
}
