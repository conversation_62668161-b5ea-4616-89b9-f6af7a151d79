package com.learninggenie.common.utils;
import junit.framework.Assert;
import org.junit.Test;

/**
 * 资源工具类 测试类
 */
public class ResourceUtilTest {
    /**
     * 测试获取邮件模板
     */
    @Test
    public void testGetEmailTemplateAsString() {
        String html = ResourceUtil.getEmailTemplateAsString("v1", "parent_progress_report_download_email.html");
        Assert.assertNotNull(html);
    }

    /**
     * 测试字符串占位符替换
     */
    @Test
    public void testReplacePlaceholders() {
        String str = "Hello, {m7}! You are in {m8} class at {m9} center.";
        String result = ResourceUtil.replacePlaceholders(str);
        Assert.assertEquals("Hello, Child Name! You are in Class Name class at Center Name center.", result);
    }

    /**
     * 测试字符串占位符多语言替换
     */
    @Test
    public void testReplacePlaceholders_Lang() {
        String str = "Hello, {m7}! You are in {m8} class at {m9} center.";
        String result = ResourceUtil.replacePlaceholders(str, "en-US");
        Assert.assertEquals("Hello, Child Name! You are in Class Name class at Center Name center.", result);

        str = "你好，{m7}！你在{m8}班，就读于{m9}学校。";
        result = ResourceUtil.replacePlaceholders(str, "zh-CN");
        Assert.assertEquals("你好，学生名！你在班级名班，就读于学校名学校。", result);

        str = "Hola, {m7}! Estás en la clase {m8} en el centro {m9}.";
        result = ResourceUtil.replacePlaceholders(str, "es-ES");
        Assert.assertEquals("Hola, Nombre de niño! Estás en la clase Nombre de la clase en el centro Nombre del centro.", result);
    }

    /**
     * 测试获取资源文件内容
     */
    @Test
    public void testGetResourceAsString() {
        String str = ResourceUtil.getResourceAsString("email_templates/v1/account_cancel.html");
        Assert.assertFalse(str.matches("\\{m\\d+\\}"));

        str = ResourceUtil.getResourceAsString("email_templates/v1/account_cancel.html", "en-US");
        Assert.assertFalse(str.matches("\\{m\\d+\\}"));

        str = ResourceUtil.getResourceAsString("email_templates/v1/account_cancel.html", "zh-CN");
        Assert.assertFalse(str.matches("\\{m\\d+\\}"));

        str = ResourceUtil.getResourceAsString("email_templates/v1/account_cancel.html", "es-ES");
        Assert.assertFalse(str.matches("\\{m\\d+\\}"));
    }
}
