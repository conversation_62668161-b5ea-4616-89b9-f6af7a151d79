package com.learninggenie.common.utils;

import com.deepoove.poi.util.BufferedImageUtils;
import com.learninggenie.common.utils.FileUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockitoAnnotations;

import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;

import static org.junit.jupiter.api.Assertions.*;

/**
 * FileUtilTest类用于测试FileUtil工具类的各个方法。
 */
public class FileUtilTest {

    /**
     * 在每个测试方法执行前初始化Mockito注解。
     */
    @BeforeEach
    public void setup() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试createTempFile方法是否返回带有正确后缀的临时文件。
     */
    @Test
    public void createTempFileReturnsFileWithCorrectSuffix() throws IOException {
        // 设定临时文件后缀
        String suffix = ".tmp";
        // 调用方法创建临时文件
        File tempFile = FileUtil.createTempFile(suffix);
        // 断言临时文件名称以指定后缀结尾
        assertTrue(tempFile.getName().endsWith(suffix));
        // 删除临时文件
        FileUtil.deleteFile(tempFile.getAbsolutePath());
    }

    /**
     * 测试createTempFile方法是否返回位于临时目录中的文件。
     */
    @Test
    public void createTempFileReturnsFileInTempDirectory() throws IOException {
        // 调用方法创建临时文件
        File tempFile = FileUtil.createTempFile();
        // 获取系统临时目录
        String tempDir = System.getProperty("java.io.tmpdir");
        // 断言临时文件的绝对路径以系统临时目录开头
        assertTrue(tempFile.getAbsolutePath().startsWith(tempDir));
        // 删除临时文件
        FileUtil.deleteFile(tempFile.getAbsolutePath());
    }

    /**
     * 测试randomTempFilePath方法是否返回位于临时目录中的文件路径。
     */
    @Test
    public void randomTempFilePathReturnsPathInTempDirectory() {
        // 调用方法获取随机临时文件路径
        String tempPath = FileUtil.randomTempFilePath(".txt");
        // 获取系统临时目录
        String tempDir = System.getProperty("java.io.tmpdir");
        // 断言返回的文件路径以系统临时目录开头
        assertTrue(tempPath.startsWith(tempDir));
        // 删除临时文件
        FileUtil.deleteFile(tempPath);
    }

    /**
     * 测试randomTempFilePath方法是否返回带有正确扩展名的文件路径。
     */
    @Test
    public void randomTempFilePathReturnsPathWithCorrectExtension() {
        // 指定文件扩展名
        String extension = ".txt";
        // 调用方法获取带有指定扩展名的随机临时文件路径
        String tempPath = FileUtil.randomTempFilePath(extension);
        // 断言返回的文件路径以指定扩展名结尾
        assertTrue(tempPath.endsWith(extension));
        // 删除临时文件
        FileUtil.deleteFile(tempPath);
    }

    /**
     * 测试deleteDir方法是否成功删除目录。
     */
    @Test
    public void deleteDirRemovesDirectory() {
        // 创建测试目录路径
        String dirPath = System.getProperty("java.io.tmpdir") + "/testDir";
        // 创建测试目录
        File dir = new File(dirPath);
        dir.mkdir();
        // 断言目录存在
        assertTrue(dir.exists());
        // 调用方法删除目录
        FileUtil.deleteDir(dirPath);
        // 断言目录不存在
        assertFalse(dir.exists());
    }

    /**
     * 测试deleteFile方法是否成功删除文件。
     */
    @Test
    public void deleteFileRemovesFile() throws IOException {
        // 创建临时文件
        File tempFile = FileUtil.createTempFile();
        // 断言文件存在
        assertTrue(tempFile.exists());
        // 调用方法删除文件
        FileUtil.deleteFile(tempFile.getAbsolutePath());
        // 断言文件不存在
        assertFalse(tempFile.exists());
    }

    /**
     * 测试getBufferByteArray方法是否为有效图像和格式返回正确的字节数组。
     */
    @Test
    public void getBufferByteArrayReturnsCorrectByteArrayForValidImageAndFormat() throws IOException {
        // 获取测试图像
        BufferedImage testImage = BufferedImageUtils.getUrlBufferedImage("https://s3.amazonaws.com//com.learning-genie.prod.us/4979c885-1634-4a95-b9c4-c0d95b4da272.png");
        // 指定图像格式
        String format = "png";
        // 指定目标纵横比
        double targetAspectRatio = 1.0;

        // 调用方法获取图像字节数组
        byte[] result = FileUtil.getBufferByteArray(testImage, format, targetAspectRatio);

        // 断言结果不为空且长度大于0
        assertNotNull(result);
        assertTrue(result.length > 0);
    }

    /**
     * 测试getBufferByteArray方法是否对空图像返回null。
     */
    @Test
    public void getBufferByteArrayReturnsNullForNullImage() throws IOException {
        // 设置空图像
        BufferedImage testImage = null;
        // 指定图像格式
        String format = "png";
        // 指定目标纵横比
        double targetAspectRatio = 1.0;

        // 调用方法获取图像字节数组
        byte[] result = FileUtil.getBufferByteArray(testImage, format, targetAspectRatio);

        // 断言结果为空
        assertNull(result);
    }

    /**
     * 测试getBufferByteArray方法是否对无效格式返回正确的字节数组。
     */
    @Test
    public void getBufferByteArrayReturnsCorrectByteArrayForInvalidFormat() throws IOException {
        // 创建虚拟图像
        BufferedImage testImage = new BufferedImage(100, 100, BufferedImage.TYPE_INT_RGB);
        // 指定无效格式
        String format = "invalidFormat";
        // 指定目标纵横比
        double targetAspectRatio = 1.0;

        // 调用方法获取图像字节数组
        byte[] result = FileUtil.getBufferByteArray(testImage, format, targetAspectRatio);

        // 断言结果不为空且长度大于0
        assertNotNull(result);
        assertTrue(result.length > 0);
    }


    @Test
    public void downLoadFromUrlReturnsFileInputStreamForValidUrl() throws IOException {
        String urlStr = "https://s3.amazonaws.com//com.learning-genie.prod.us/4979c885-1634-4a95-b9c4-c0d95b4da272.png";
        String fileName = "testfile.txt";
        String savePath = System.getProperty("java.io.tmpdir");

        FileInputStream result = FileUtil.downLoadFromUrl(urlStr, fileName, savePath);

        assertNotNull(result);
        assertTrue(new File(savePath, fileName).exists());
        FileUtil.deleteFile(savePath + "/" + fileName);
    }

    @Test
    public void downLoadFromUrlThrowsIOExceptionForInvalidUrl() {
        String urlStr = "https://example.com/nonexistentfile.txt";
        String fileName = "testfile.txt";
        String savePath = System.getProperty("java.io.tmpdir");

        assertThrows(IOException.class, () -> {
            FileInputStream result = FileUtil.downLoadFromUrl(urlStr, fileName, savePath);
        });
        FileUtil.deleteFile(savePath + "/" + fileName);
    }

    @Test
    public void downLoadFromUrlThrowsIOExceptionForNonexistentFile() {
        String urlStr = "https://example.com/nonexistentfile.txt";
        String fileName = "nonexistentfile.txt";
        String savePath = System.getProperty("java.io.tmpdir");

        assertThrows(IOException.class, () -> {
            FileInputStream result = FileUtil.downLoadFromUrl(urlStr, fileName, savePath);
        });
        FileUtil.deleteFile(savePath + "/" + fileName);
    }
}
