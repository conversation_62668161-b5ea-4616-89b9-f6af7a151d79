package com.learninggenie.common.utils;

import org.junit.Test;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.assertEquals;
/**
 * Created by zjj on 2016/9/27.
 */
public class ExpUtilTest {
    @Test
    public void testEval() {
        Map<String, Object> vars = new HashMap<>();
        vars.put("a", 1);
        vars.put("b", 2);
        float result = ExpUtil.evaluate("a+b", vars);
        assertEquals("3.0", result + "");
    }
}
