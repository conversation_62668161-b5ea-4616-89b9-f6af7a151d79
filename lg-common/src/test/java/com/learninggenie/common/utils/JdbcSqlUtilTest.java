package com.learninggenie.common.utils;

import com.learninggenie.common.data.entity.AlbumEntity;
import com.learninggenie.common.data.entity.AlbumPageEntity;
import com.learninggenie.common.data.entity.AlbumSpecEntity;
import org.junit.Test;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

public class JdbcSqlUtilTest {
    @Test
    public void testInsert(){
        AlbumPageEntity page = new AlbumPageEntity();
        page.setId("id");
        page.setPageNum(0);
        page.setHtml("html");
        page.setText("text");
        page.setImgIds("imgIds");
        page.setType("type");
        page.setDateString("2018-02-01");
        page.setNoteCreateAtLocal(new Date());
        page.setCreateAtUtc(new Date());
        page.setUpdateAtUtc(new Date());
        AlbumEntity album = new AlbumEntity();
        AlbumSpecEntity spec = new AlbumSpecEntity();
//        EnrollmentEntity enrollment=new EnrollmentEntity();
//        enrollment.setId("enrollmentId");
        spec.setId("specId");
        album.setSpec(spec);
//        album.setEnrollment(enrollment);
        album.setId(UUID.randomUUID().toString());
        page.setAlbum(album);
        page.setTemplateId("templateId");
        page.setCreateByManual(true);
        page.setTexts("texts");
        page.setImgTextIndex("ImgTextIndex");
        page.setEnrollmentId("EnrollmentId");
        page.setGroupId("GroupId");
        page.setNewTexts("NewTexts");
        List<Object> objects = new ArrayList<>();
        try {
            String sql = JdbcSqlUtil.insert(page, objects);
            System.out.println(sql);
            System.out.println(objects.size());
            objects.forEach(x-> System.out.print(x+"\t"));
            System.out.println();
            objects.clear();
            sql = JdbcSqlUtil.insert(album,objects);
            System.out.println(objects.size());
            System.out.println(sql);
            objects.forEach(x-> System.out.print(x+"\t"));
            System.out.println();
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        }
    }
    @Test
    public void testInsertList(){
        AlbumEntity album = new AlbumEntity();
        album.setId(UUID.randomUUID().toString());
        AlbumPageEntity page = new AlbumPageEntity();
        page.setId("id");
        page.setPageNum(0);
        page.setHtml("html");
        page.setText("text");
        page.setImgIds("imgIds");
        page.setType("type");
        page.setDateString("2018-02-01");
        page.setNoteCreateAtLocal(new Date());
        page.setCreateAtUtc(new Date());
        page.setUpdateAtUtc(new Date());
        page.setAlbum(album);
        page.setTemplateId("templateId");
        page.setCreateByManual(true);
        page.setTexts("texts");
        page.setImgTextIndex("ImgTextIndex");
        page.setEnrollmentId("EnrollmentId");
        page.setGroupId("GroupId");
        page.setNewTexts("NewTexts");
        AlbumPageEntity page2 = new AlbumPageEntity();
        page2.setId("id2");
        page2.setPageNum(1);
        page2.setHtml("html2");
        page2.setText("text2");
        page2.setImgIds("imgIds2");
        page2.setType("type2");
        page2.setNoteCreateAtLocal(new Date());
        page2.setCreateAtUtc(new Date());
        page2.setUpdateAtUtc(new Date());
        page2.setAlbum(album);
        page2.setTemplateId("templateId");
        page2.setCreateByManual(true);
        page2.setTexts("texts2");
        page2.setGroupId("GroupId2");
        page2.setNewTexts("NewTexts2");
        List<Object> objects = new ArrayList<>();
        List<AlbumPageEntity> albumPages = new ArrayList<>();
        albumPages.add(page);
        albumPages.add(page2);
        try {
            String sql = JdbcSqlUtil.insert(AlbumPageEntity.class,albumPages, objects);
            System.out.println(sql);
            System.out.println(sql.replaceAll("[^\\?]","").length());
            System.out.println(objects.size());
            objects.forEach(x-> System.out.print(x+"\t"));
            System.out.println();
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        }
    }
}
