package com.learninggenie.common.utils.openai;

import com.learninggenie.common.data.enums.notes.MeasureType;
import com.learninggenie.common.data.model.DomainEntity;
import com.learninggenie.common.data.model.LevelEntity;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.*;

public class FrameworkPromptUtilTest {

    @Test
    public void testGetFrameworkPrompt() {
        // 模拟入参
        List<DomainEntity> domains = new ArrayList<>();
        // 调用方法
        String frameworkPrompt = FrameworkPromptUtil.generateFrameworkInfo(domains);
        // 验证结果
        Assertions.assertNotNull(frameworkPrompt);
        // 验证
        Assertions.assertEquals("", frameworkPrompt);
    }

    @Test
    @Ignore
    public void testGenerateMeasureDesc() {
        // 模拟入参
        DomainEntity measure = new DomainEntity();
        measure.setName("Measure Name");
        measure.setAbbreviation("Measure Abbreviation");
        measure.setDescription("Measure Description");
        // 调用方法
        String measureDesc = FrameworkPromptUtil.generateMeasureDesc(measure, MeasureType.MEASURE);
        // 验证结果
        Assertions.assertNotNull(measureDesc);
        // 验证数据
        Assertions.assertEquals("Measure Abbreviation: Measure Name\n" +
                "Measure Description: Measure Description\n", measureDesc);

    }

    @Test
    @Ignore
    public void testGenerateFrameworkInfo() {
        // 模拟入参
        List<DomainEntity> domains = new ArrayList<>();
        DomainEntity domain = new DomainEntity();
        domain.setName("Measure Name");
        domain.setAbbreviation("Measure Abbreviation");
        domain.setDescription("Measure Description");
        List<LevelEntity> levels = new ArrayList<>();
        LevelEntity level = new LevelEntity();
        level.setName("Responding Earlier");
        level.setTip("Tip");
        levels.add(level);
        domain.setLevels(levels);
        String ageGroup = "Infant (0-1)";
        domains.add(domain);
        // 调用方法
        String frameworkInfo = FrameworkPromptUtil.generateFrameworkInfo(domains, ageGroup);
        // 验证结果
        Assertions.assertNotNull(frameworkInfo);
        // 验证数据
        Assertions.assertEquals("Measure Abbreviation: Measure Name\n" +
                "Measure Description: Measure Description\n" +
                "Child's Abilities at this age:\n" +
                "Tip\n", frameworkInfo);

    }

    @Test
    @Ignore
    public void testGenerateFrameworkInfo2() {
        // 模拟入参
        List<DomainEntity> domains = new ArrayList<>();
        List<DomainEntity> domains1 = new ArrayList<>();
        DomainEntity domain = new DomainEntity();
        domain.setName("Measure Name");
        domain.setAbbreviation("Measure Abbreviation");
        domain.setDescription("Measure Description");
        List<LevelEntity> levels = new ArrayList<>();
        LevelEntity level = new LevelEntity();
        level.setName("Responding Earlier");
        level.setTip("Tip");
        levels.add(level);
        domain.setLevels(levels);
        String ageGroup = "Infant (0-1)";
        DomainEntity domain1 = new DomainEntity();
        domain1.setName("Measure Name");
        domain1.setAbbreviation("Measure Abbreviation");
        domain1.setDescription("Measure Description");
        List<LevelEntity> levels1 = new ArrayList<>();
        LevelEntity level1 = new LevelEntity();
        level1.setName("Responding Earlier");
        level1.setTip("Tip");
        levels1.add(level1);
        domains.add(domain);
        domain1.setLevels(levels1);
        domain1.setNodes(domains);
        domains1.add(domain1);
        domains1.add(domain1);
        // 调用方法
        String frameworkInfo = FrameworkPromptUtil.generateFrameworkInfo(domains1, ageGroup);
        // 验证结果
        Assertions.assertNotNull(frameworkInfo);
        // 验证数据
        Assertions.assertEquals("Domain: Measure Name(Measure Abbreviation)\n" +
                "Measures:\n" +
                "Measure Abbreviation: Measure Name\n" +
                "Measure Description: Measure Description\n" +
                "Child's Abilities at this age:\n" +
                "Tip\n" +
                "\n" +
                "Domain: Measure Name(Measure Abbreviation)\n" +
                "Measures:\n" +
                "Measure Abbreviation: Measure Name\n" +
                "Measure Description: Measure Description\n" +
                "Child's Abilities at this age:\n" +
                "Tip\n",frameworkInfo);

    }

    /**
     * 测试 generateFrameworkInfo 方法，包含所有分支
     */
    @Test
    public void testGenerateFrameworkInfo_EmptyDomains() {
        // 当 domains 为空时，期望返回空字符串
        List<DomainEntity> emptyDomains = new ArrayList<>();
        String result = FrameworkPromptUtil.generateFrameworkInfo(emptyDomains, "ageGroup", true);
        assertEquals("", result);
    }

    @Test
    public void testGenerateFrameworkInfo_NonEmptyDomains_WithK12Framework() {
        // 准备测试数据
        DomainEntity domain1 = new DomainEntity();
        domain1.setApplicableFramework("K12_STANDARDS");
        domain1.setNodes(Collections.emptyList());

        List<DomainEntity> domains = Collections.singletonList(domain1);

        // 调用被测试方法
        String result = FrameworkPromptUtil.generateFrameworkInfo(domains, "ageGroup", true);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.contains("K12_STANDARDS"));
    }

    @Test
    public void testGenerateFrameworkInfo_NonEmptyDomains_WithoutK12Framework() {
        // 准备测试数据
        DomainEntity domain1 = new DomainEntity();
        domain1.setApplicableFramework("NON_K12_STANDARDS");
        domain1.setNodes(Collections.emptyList());

        List<DomainEntity> domains = Collections.singletonList(domain1);

        // 调用被测试方法
        String result = FrameworkPromptUtil.generateFrameworkInfo(domains, "ageGroup", true);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.contains("NON_K12_STANDARDS"));
    }

    @Test
    public void testGenerateFrameworkInfo_WithChildNodes() {
        // 准备测试数据
        DomainEntity childDomain = new DomainEntity();
        childDomain.setApplicableFramework("CHILD_FRAMEWORK");
        childDomain.setNodes(Collections.emptyList());

        DomainEntity parentDomain = new DomainEntity();
        parentDomain.setApplicableFramework("PARENT_FRAMEWORK");
        parentDomain.setNodes(Collections.singletonList(childDomain));

        List<DomainEntity> domains = Collections.singletonList(parentDomain);

        // 调用被测试方法
        String result = FrameworkPromptUtil.generateFrameworkInfo(domains, "ageGroup", true);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.contains("PARENT_FRAMEWORK"));
        assertFalse(result.contains("CHILD_FRAMEWORK"));
    }

    @Test
    public void testGenerateFrameworkInfo_MultipleLevels() {
        // 准备测试数据
        DomainEntity level3Domain = new DomainEntity();
        level3Domain.setApplicableFramework("LEVEL3_FRAMEWORK");
        level3Domain.setNodes(Collections.emptyList());

        DomainEntity level2Domain = new DomainEntity();
        level2Domain.setApplicableFramework("LEVEL2_FRAMEWORK");
        level2Domain.setNodes(Collections.singletonList(level3Domain));

        DomainEntity level1Domain = new DomainEntity();
        level1Domain.setApplicableFramework("LEVEL1_FRAMEWORK");
        level1Domain.setNodes(Collections.singletonList(level2Domain));

        List<DomainEntity> domains = Collections.singletonList(level1Domain);

        // 调用被测试方法
        String result = FrameworkPromptUtil.generateFrameworkInfo(domains, "ageGroup", true);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.contains("LEVEL1_FRAMEWORK"));
        assertFalse(result.contains("LEVEL2_FRAMEWORK"));
        assertFalse(result.contains("LEVEL3_FRAMEWORK"));
    }


    /**
     * 测试 generateMeasureDesc 方法，包含所有分支
     */
    @Test
    public void testGenerateMeasureDesc_Include_Subject() {
        // 准备测试数据

        // MeasureType.SUBJECT_DOMAIN
        DomainEntity subjectDomain = new DomainEntity();
        subjectDomain.setName("Mathematics");
        subjectDomain.setAbbreviation("MATH");
        subjectDomain.setMappingAbbr("MATH_SD");
        subjectDomain.setDescription("Subject Domain Description");
        subjectDomain.setLevels(new ArrayList<>());

        // MeasureType.DOMAIN
        DomainEntity domain = new DomainEntity();
        domain.setName("Algebra");
        domain.setAbbreviation("ALG");
        domain.setMappingAbbr("ALG_D");
        domain.setDescription("Domain Description");
        domain.setLevels(new ArrayList<>());

        // MeasureType.SUB_DOMAIN
        DomainEntity subDomain = new DomainEntity();
        subDomain.setName("Linear Equations");
        subDomain.setAbbreviation("LE");
        subDomain.setMappingAbbr("LE_SD");
        subDomain.setDescription("Sub Domain Description");
        subDomain.setLevels(new ArrayList<>());

        // MeasureType.MEASURE with levels
        LevelEntity level1 = new LevelEntity();
        level1.setName("Level 1");
        level1.setTip("Tip 1");
        level1.setValue("1");

        LevelEntity level2 = new LevelEntity();
        level2.setName("Level 2");
        level2.setTip("Tip 2");
        level2.setValue("2");

        DomainEntity measureWithLevels = new DomainEntity();
        measureWithLevels.setName("Solve Equations");
        measureWithLevels.setAbbreviation("SE");
        measureWithLevels.setMappingAbbr("SE_M");
        measureWithLevels.setDescription("Measure Description");
        measureWithLevels.setLevels(Arrays.asList(level1, level2));

        // MeasureType.MEASURE without levels
        DomainEntity measureWithoutLevels = new DomainEntity();
        measureWithoutLevels.setName("Graph Equations");
        measureWithoutLevels.setAbbreviation("GE");
        measureWithoutLevels.setMappingAbbr("GE_M");
        measureWithoutLevels.setDescription("Measure Description");
        measureWithoutLevels.setLevels(new ArrayList<>());

        // 调用被测试方法并验证结果
        String resultSubjectDomain = FrameworkPromptUtil.generateMeasureDesc(subjectDomain, MeasureType.SUBJECT_DOMAIN, true);
        assertTrue(resultSubjectDomain.contains("Subject Name: Mathematics"));

        String resultDomain = FrameworkPromptUtil.generateMeasureDesc(domain, MeasureType.DOMAIN, true);
        assertTrue(resultDomain.contains("Domain: Algebra"));

        String resultSubDomain = FrameworkPromptUtil.generateMeasureDesc(subDomain, MeasureType.SUB_DOMAIN, true);
        assertTrue(resultSubDomain.contains("Sub domain: Linear Equations"));

        String resultMeasureWithLevels = FrameworkPromptUtil.generateMeasureDesc(measureWithLevels, MeasureType.MEASURE, true);
        assertTrue(resultMeasureWithLevels.contains("Measure Abbreviation: SE_M"));
        assertTrue(resultMeasureWithLevels.contains("Measure Name: Solve Equations"));
        assertTrue(resultMeasureWithLevels.contains("Measure Description: Measure Description"));
        assertTrue(resultMeasureWithLevels.contains("Developmental levels:"));
        assertTrue(resultMeasureWithLevels.contains("Level 1"));
        assertTrue(resultMeasureWithLevels.contains("Level 2"));

        String resultMeasureWithoutLevels = FrameworkPromptUtil.generateMeasureDesc(measureWithoutLevels, MeasureType.MEASURE, false);
        assertTrue(resultMeasureWithoutLevels.contains("Measure Abbreviation: GE_M"));
        assertTrue(resultMeasureWithoutLevels.contains("Measure Name: Graph Equations"));
        assertTrue(resultMeasureWithoutLevels.contains("Measure Description: Measure Description"));
        assertFalse(resultMeasureWithoutLevels.contains("Developmental levels:"));
    }

}
