//package com.learninggenie.common.weekly;
//
//import com.learninggenie.common.data.dao.CacheDao;
//import com.learninggenie.common.data.dao.NoteDao;
//import com.learninggenie.common.data.dao.impl.AgencyDaoImpl;
//import com.learninggenie.common.data.dao.impl.CenterDaoImpl;
//import com.learninggenie.common.data.dao.impl.ReportDaoImpl;
//import com.learninggenie.common.data.dao.impl.StudentDaoImpl;
//import com.learninggenie.common.data.dao.impl.*;
//import com.learninggenie.common.data.dto.AgencyAdminCenterView;
//import com.learninggenie.common.data.dto.CenterGroupEnrollmentModel;
//import com.learninggenie.common.data.entity.CacheEntity;
//import com.learninggenie.common.data.dto.WeeklyUserView;
//import com.learninggenie.common.data.entity.*;
//import com.learninggenie.common.data.entity.CenterEntity;
//import com.learninggenie.common.data.entity.DomainEntity;
//import com.learninggenie.common.data.entity.GroupEntity;
//import com.learninggenie.common.data.entity.NoteEntity;
//import com.learninggenie.common.data.model.*;
//import com.learninggenie.common.messaging.EmailModel;
//import com.learninggenie.common.messaging.EmailService;
//import com.learninggenie.common.utils.ResourceUtil;
//import com.learninggenie.common.utils.TimeUtil;
//import org.junit.Assert;
//import org.junit.Ignore;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.Mockito;
//import org.powermock.api.mockito.PowerMockito;
//import org.powermock.core.classloader.annotations.PrepareForTest;
//import org.powermock.modules.junit4.PowerMockRunner;
//
//import java.util.*;
//
//import static org.mockito.Matchers.anyByte;
//import static org.mockito.Matchers.anyString;
//import static org.mockito.Mockito.times;
//import static org.mockito.Mockito.verify;
//import static org.mockito.Mockito.when;
//
///**
// * Created by Sena on 2017/6/5.
// */
//@RunWith(PowerMockRunner.class)
//@PrepareForTest({TimeUtil.class, ResourceUtil.class})
//public class WeeklyServiceImplTest {
//    @InjectMocks
//    private WeeklyServiceImpl weeklyService;
//    @Mock
//    private ReportDaoImpl reportDao;
//    @Mock
//    private AgencyDaoImpl agencyDao;
//    @Mock
//    private StudentDaoImpl studentDao;
//    @Mock
//    private CenterDaoImpl centerDao;
//    @Mock
//    private EmailService emailService;
//    @Mock
//    private NoteDao noteDao;
//    @Mock
//    private UserDaoImpl userDao;
//    @Mock
//    private CacheDaoImpl cacheDao;
//    @Mock
//    private DomainDaoImpl domainDao;
//    @Mock
//    private ScoreDaoImpl scoreDao;
//    @Mock
//    private GroupDaoImpl groupDao;
//
//    /**
//     * Case : 如果Agency当天未发送过, 会进行发送
//     */
//    @Test
//    public void testStatisticsAgencyWeekly_Send() throws Exception {
//        List<AgencyModel> agencies = new ArrayList<>();
//        AgencyModel agency = new AgencyModel();
//        agency.setId("A123");
//        agency.setAgencyTimeZone("America/Los_Angeles");
//        agencies.add(agency);
//
//        when(userDao.getAgencyOwnerLang(anyString())).thenReturn("en");
//        List<AgencySummaryReportEntity> summaryReportEntities = new ArrayList<>();
//        AgencySummaryReportEntity report = new AgencySummaryReportEntity();
//
//        Calendar cal = Calendar.getInstance();
//        cal.setTime(TimeUtil.getUtcNow());
//        cal.add(Calendar.DATE, -6);
//        report.setCreateAtUtc(cal.getTime());
//        summaryReportEntities.add(report);
//        when(reportDao.getWeeklySummaryRecord(anyString(), anyByte(), anyByte())).thenReturn(summaryReportEntities);
//        List<CenterGroupEnrollmentModel> centerGroupEnrollmentModels = new ArrayList<>();
//        for (int i = 0; i < 5; i++) {
//            CenterGroupEnrollmentModel centerGroupEnrollment = new CenterGroupEnrollmentModel();
//            centerGroupEnrollment.setCenterId("C" + i);
//            centerGroupEnrollment.setGroupId("G" + i);
//            centerGroupEnrollment.setGroupAlias("GA123");
//            centerGroupEnrollment.setGroupIsDeleted(false);
//            centerGroupEnrollment.setGroupIsInvatived(false);
//            centerGroupEnrollment.setChildPeriodIsActived(false);
//            centerGroupEnrollment.setEnrollmentIsDeleted(false);
//            centerGroupEnrollment.setEnrollmentId("E" + i);
//            centerGroupEnrollment.setEnrollmentAlias("EA123");
//            centerGroupEnrollment.setCenterName("C_NAME");
//            centerGroupEnrollment.setDomainId("D123");
//            centerGroupEnrollment.setDomainName("D_NAME");
//            centerGroupEnrollment.setCenterTimeZone("America/Los_Angeles");
//            centerGroupEnrollmentModels.add(centerGroupEnrollment);
//        }
//        CacheEntity cache = new CacheEntity();
//        cache.setValue("");
//        when(cacheDao.getByKey(anyString())).thenReturn(cache);
//        when(agencyDao.getCenterGroupEnrollmentByAgencyId(anyString())).thenReturn(centerGroupEnrollmentModels);
//        when(agencyDao.getMostUsedAliasByAgencyId(anyString())).thenReturn("Alias");
//
//        List<UserModel> agencyAdmins = new ArrayList<>();
//        UserModel user = new UserModel();
//        user.setId("U123");
//        user.setEmail("<EMAIL>");
//        agencyAdmins.add(user);
//        when(userDao.getAgencyAdminsByAgencyId(anyString())).thenReturn(agencyAdmins);
//        PowerMockito.mockStatic(ResourceUtil.class);
//        PowerMockito.when(ResourceUtil.getResourceAsStringNoLang(anyString())).thenReturn("<p>123{AllSiteNotes}");
//        weeklyService.statisticsAgencyWeekly(agencies);
//        //当天未发送
//        verify(agencyDao, times(1)).getCenterGroupEnrollmentByAgencyId(Mockito.anyString());
//        verify(agencyDao, times(1)).getMostUsedAliasByAgencyId(Mockito.anyString());
//        verify(emailService, times(1)).sendAsync(Mockito.any(EmailModel.class));
//        verify(userDao, times(1)).getUserLang(Mockito.any(String.class));
//    }
//
//    /**
//     * Case : 获取需要发送的Agency, 开始周期未到
//     */
//    @Ignore
//    @Test
//    public void test_get_weekly_send_agency_no_start() throws Exception {
//        List<AgencyModel> agencies = new ArrayList<>();
//        AgencyModel agency1 = new AgencyModel();
//        agency1.setId("A1");
//
//        agencies.add(agency1);
//
//        final Calendar cal = Calendar.getInstance();
//        cal.set(2017, Calendar.JULY, 28, 16, 6, 2);
//        PowerMockito.spy(TimeUtil.class); // 创建spy,如果不创建的话，后面调用WebChatUtil就都是Mock类，这里创建了spy后，只有设置了mock的方法才会调用mock行为
//        //PowerMockito.doReturn(cal.getTime()).when(TimeUtil.class, "getUtcNow"); //Mock私有方法
//        PowerMockito.when(TimeUtil.getUtcNow()).thenReturn(cal.getTime());
//
//        List<RatingPeriodEntity> periodEntities1 = new ArrayList<>();
//        RatingPeriodEntity period11 = new RatingPeriodEntity();
//        cal.add(Calendar.DATE, 9);
//        period11.setFromAtLocal(cal.getTime());
//        cal.add(Calendar.DATE, 5);
//        period11.setToAtLocal(cal.getTime());
//        periodEntities1.add(period11);
//        //设置第一个Agency开始周期未到
//        when(studentDao.getChildPeriodByAgencyId("A1")).thenReturn(periodEntities1);
//        when(noteDao.getAgencyNoteByDate(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(3);
//        when(centerDao.getFirstCenterTimeZoneByAgencyId(Mockito.anyString())).thenReturn("America/Los_Angeles");
//
//        List<AgencyModel> agencyModels = weeklyService.getWeeklySendAgency(agencies);
//
//        Assert.assertTrue(agencyModels.size() == 0);
//    }
//
//    /**
//     * Case : 获取需要发送的Agency, 周期已经过期14天
//     */
//    @Ignore
//    @Test
//    public void test_get_weekly_send_agency_expired() throws Exception {
//        List<AgencyModel> agencies = new ArrayList<>();
//        AgencyModel agency2 = new AgencyModel();
//        agency2.setId("A2");
//        agencies.add(agency2);
//
//        final Calendar cal = Calendar.getInstance();
//        cal.set(2017, Calendar.JULY, 28, 16, 6, 2);
//        PowerMockito.spy(TimeUtil.class); // 创建spy,如果不创建的话，后面调用WebChatUtil就都是Mock类，这里创建了spy后，只有设置了mock的方法才会调用mock行为
//        //PowerMockito.doReturn(cal.getTime()).when(TimeUtil.class, "getUtcNow"); //Mock私有方法
//        PowerMockito.when(TimeUtil.getUtcNow()).thenReturn(cal.getTime());
//
//        cal.add(Calendar.DATE, -30);
//        List<RatingPeriodEntity> periodEntities2 = new ArrayList<>();
//        RatingPeriodEntity period21 = new RatingPeriodEntity();
//        period21.setToAtLocal(cal.getTime());
//        cal.add(Calendar.DATE, -1);
//        period21.setFromAtLocal(cal.getTime());
//        periodEntities2.add(period21);
//        //第二个设置过期14天
//        when(studentDao.getChildPeriodByAgencyId("A2")).thenReturn(periodEntities2);
//        when(noteDao.getAgencyNoteByDate(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(3);
//        when(centerDao.getFirstCenterTimeZoneByAgencyId(Mockito.anyString())).thenReturn("America/Los_Angeles");
//
//        List<AgencyModel> agencyModels = weeklyService.getWeeklySendAgency(agencies);
//
//        Assert.assertTrue(agencyModels.size() == 0);
//    }
//
//    /**
//     * Case : 获取需要发送的Agency, 没有周期
//     */
//    @Ignore
//    @Test
//    public void test_get_weekly_send_agency_no_period() throws Exception {
//        List<AgencyModel> agencies = new ArrayList<>();
//        AgencyModel agency3 = new AgencyModel();
//        agency3.setId("A3");
//        agencies.add(agency3);
//
//        final Calendar cal = Calendar.getInstance();
//        cal.set(2017, Calendar.JULY, 28, 16, 6, 2);
//        PowerMockito.spy(TimeUtil.class); // 创建spy,如果不创建的话，后面调用WebChatUtil就都是Mock类，这里创建了spy后，只有设置了mock的方法才会调用mock行为
//        //PowerMockito.doReturn(cal.getTime()).when(TimeUtil.class, "getUtcNow"); //Mock私有方法
//        PowerMockito.when(TimeUtil.getUtcNow()).thenReturn(cal.getTime());
//
//        List<RatingPeriodEntity> periodEntities3 = new ArrayList<>();
//        //第三个设置为空
//        when(studentDao.getChildPeriodByAgencyId("A3")).thenReturn(periodEntities3);
//        when(noteDao.getAgencyNoteByDate(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(3);
//        when(centerDao.getFirstCenterTimeZoneByAgencyId(Mockito.anyString())).thenReturn("America/Los_Angeles");
//
//        List<AgencyModel> agencyModels = weeklyService.getWeeklySendAgency(agencies);
//
//        Assert.assertTrue(agencyModels.size() == 0);
//    }
//
//    /**
//     * Case : 获取需要发送的Agency, 周期通过
//     */
//    @Ignore
//    @Test
//    public void test_get_weekly_send_agency_pass() throws Exception {
//        List<AgencyModel> agencies = new ArrayList<>();
//        AgencyModel agency4 = new AgencyModel();
//        agency4.setId("A4");
//        agencies.add(agency4);
//
//        final Calendar cal = Calendar.getInstance();
//        cal.set(2017, Calendar.JULY, 28, 16, 6, 2);
//        PowerMockito.spy(TimeUtil.class); // 创建spy,如果不创建的话，后面调用WebChatUtil就都是Mock类，这里创建了spy后，只有设置了mock的方法才会调用mock行为
//        //PowerMockito.doReturn(cal.getTime()).when(TimeUtil.class, "getUtcNow"); //Mock私有方法
//        PowerMockito.when(TimeUtil.getUtcNow()).thenReturn(cal.getTime());
//
//        List<RatingPeriodEntity> periodEntities4 = new ArrayList<>();
//        RatingPeriodEntity period41 = new RatingPeriodEntity();
//        //第四个为正常
//        Calendar calS = Calendar.getInstance();
//        calS.set(2017, Calendar.JULY, 28, 16, 6, 2);//上午9点
//        calS.add(Calendar.DATE, -3);
//        period41.setFromAtLocal(calS.getTime());
//        calS.add(Calendar.DATE, 8);
//        period41.setToAtLocal(calS.getTime());
//        periodEntities4.add(period41);
//        when(studentDao.getChildPeriodByAgencyId("A4")).thenReturn(periodEntities4);
//        when(noteDao.getAgencyNoteByDate(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(3);
//        when(centerDao.getFirstCenterTimeZoneByAgencyId(Mockito.anyString())).thenReturn("America/Los_Angeles");
//
//        List<AgencyModel> agencyModels = weeklyService.getWeeklySendAgency(agencies);
//
//        Assert.assertTrue(agencyModels.size() == 1);
//        Assert.assertTrue(agencyModels.get(0).getId().equalsIgnoreCase("A4"));
//    }
//
//    /**
//     * Case : Agency下所有学校都没有时区
//     */
//    @Ignore
//    @Test
//    public void test_agency_no_timezone() {
//        List<AgencyModel> agencies = new ArrayList<>();
//        AgencyModel agency1 = new AgencyModel();
//        agency1.setId("A1");
//        agencies.add(agency1);
//        String timezone = "";
//        when(centerDao.getFirstCenterTimeZoneByAgencyId(anyString())).thenReturn(timezone);
//        List<AgencyModel> agencyModels = weeklyService.getWeeklySendAgency(agencies);
//
//        Assert.assertTrue(agencyModels.size() == 0);
//        verify(reportDao, times(0)).getWeeklySummaryRecord(anyString(), anyByte(), anyByte());
//    }
//
//    /**
//     * Case   : 获取需要发送的Agency, Agency本周发送过
//     */
//    @Ignore
//    @Test
//    public void test_sendAgency_agency_never_send() {
//        List<AgencyModel> agencies = new ArrayList<>();
//        AgencyModel agency1 = new AgencyModel();
//        agency1.setId("A1");
//        agencies.add(agency1);
//
//        List<AgencySummaryReportEntity> summaryReportEntities = new ArrayList<>();
//        AgencySummaryReportEntity report = new AgencySummaryReportEntity();
//        report.setCreateAtUtc(TimeUtil.getUtcNow());
//        summaryReportEntities.add(report);
//        when(reportDao.getWeeklySummaryRecord(anyString(), anyByte(), anyByte())).thenReturn(summaryReportEntities);
//        when(centerDao.getFirstCenterTimeZoneByAgencyId(Mockito.anyString())).thenReturn("America/Los_Angeles");
//        List<AgencyModel> agencyModels = weeklyService.getWeeklySendAgency(agencies);
//
//        Assert.assertTrue(agencyModels.size() == 0);
//        verify(noteDao, times(0)).getAgencyNoteByDate(anyString(), anyString(), anyString());
//    }
//
//    /**
//     * Case   : 获取需要发送的Agency, Agency下没有周期
//     */
//    @Ignore
//    @Test
//    public void test_sendAgency_agency_no_period() {
//        List<AgencyModel> agencies = new ArrayList<>();
//        AgencyModel agency1 = new AgencyModel();
//        agency1.setId("A1");
//        agencies.add(agency1);
//        when(centerDao.getFirstCenterTimeZoneByAgencyId(Mockito.anyString())).thenReturn("America/Los_Angeles");
//        //Mock Agency period 周期数据
//        List<RatingPeriodEntity> periodEntities = new ArrayList<>();
//        when(studentDao.getChildPeriodByAgencyId(anyString())).thenReturn(periodEntities);
//        List<AgencyModel> agencyModels = weeklyService.getWeeklySendAgency(agencies);
//        Assert.assertTrue(agencyModels.size() == 0);
//        verify(noteDao, times(0)).getAgencyNoteByDate(anyString(), anyString(), anyString());
//    }
//
//    /**
//     * Case   : 获取需要发送的Agency, Agency 七天内没有新增Note
//     */
//    @Ignore
//    @Test
//    public void test_sendAgency_agency_no_note_for_seven_day() {
//        List<AgencyModel> agencies = new ArrayList<>();
//        AgencyModel agency1 = new AgencyModel();
//        agency1.setId("A1");
//        agencies.add(agency1);
//        when(centerDao.getFirstCenterTimeZoneByAgencyId(Mockito.anyString())).thenReturn("America/Los_Angeles");
//        //Mock Agency period 周期数据
//        List<RatingPeriodEntity> periodEntities = new ArrayList<>();
//        RatingPeriodEntity period = new RatingPeriodEntity();
//        PowerMockito.spy(TimeUtil.class); // 创建spy,如果不创建的话，后面调用WebChatUtil就都是Mock类，这里创建了spy后，只有设置了mock的方法才会调用mock行为
//        //PowerMockito.doReturn(cal.getTime )).when(TimeUtil.class, "getUtcNow"); //Mock私有方法
//        Calendar cal = Calendar.getInstance();
//        cal.set(2017, Calendar.JULY, 28, 16, 6, 2);//星期5 上午9点
//        PowerMockito.when(TimeUtil.getUtcNow()).thenReturn(cal.getTime());
//        cal.add(Calendar.DATE, -3);
//        period.setFromAtLocal(cal.getTime());
//        cal.add(Calendar.DATE, 8);
//        period.setToAtLocal(cal.getTime());
//        periodEntities.add(period);
//        when(studentDao.getChildPeriodByAgencyId(anyString())).thenReturn(periodEntities);
//        List<AgencyModel> agencyModels = weeklyService.getWeeklySendAgency(agencies);
//        Assert.assertTrue(agencyModels.size() == 0);
//        verify(noteDao, times(1)).getAgencyNoteByDate(anyString(), anyString(), anyString());
//    }
//
//    /**
//     * Case   : 获取需要发送的Agency, Agency满足发送条件
//     */
//    @Ignore
//    @Test
//    public void test_sendAgency_agency_pass() {
//        List<AgencyModel> agencies = new ArrayList<>();
//        AgencyModel agency1 = new AgencyModel();
//        agency1.setId("A1");
//        agencies.add(agency1);
//        when(centerDao.getFirstCenterTimeZoneByAgencyId(Mockito.anyString())).thenReturn("America/Los_Angeles");
//        //Mock Agency period 周期数据
//        List<RatingPeriodEntity> periodEntities = new ArrayList<>();
//        RatingPeriodEntity period = new RatingPeriodEntity();
//        PowerMockito.spy(TimeUtil.class); // 创建spy,如果不创建的话，后面调用WebChatUtil就都是Mock类，这里创建了spy后，只有设置了mock的方法才会调用mock行为
//        //PowerMockito.doReturn(cal.getTime )).when(TimeUtil.class, "getUtcNow"); //Mock私有方法
//        Calendar cal = Calendar.getInstance();
//        cal.set(2017, Calendar.JULY, 28, 16, 6, 2);//星期5 上午9点
//        PowerMockito.when(TimeUtil.getUtcNow()).thenReturn(cal.getTime());
//        cal.add(Calendar.DATE, -3);
//        period.setFromAtLocal(cal.getTime());
//        cal.add(Calendar.DATE, 8);
//        period.setToAtLocal(cal.getTime());
//        periodEntities.add(period);
//        when(noteDao.getAgencyNoteByDate(anyString(), anyString(), anyString())).thenReturn(1);
//        when(studentDao.getChildPeriodByAgencyId(anyString())).thenReturn(periodEntities);
//        List<AgencyModel> agencyModels = weeklyService.getWeeklySendAgency(agencies);
//        Assert.assertTrue(agencyModels.size() == 1);
//        verify(noteDao, times(1)).getAgencyNoteByDate(anyString(), anyString(), anyString());
//    }
//
//    /**
//     * Case : Mock 学校班级孩子数据, 计算最终分数
//     */
//    @Ignore
//    @Test
//    public void test_weekly_agency_admin_view() {
//        AgencyModel agency = new AgencyModel();
//        List<CenterEntity> centerEntities = new ArrayList<>();
//        this.mockData(agency, centerEntities);
//        WeeklyUserView weeklyUserView = weeklyService.getWeeklyAgencyAdminView(agency, centerEntities, "2016-2017 Summary");
//        Map<String, List<AgencyAdminCenterView>> map = weeklyUserView.getWeeklyAgencyAdminModel().getAgencyAdminCenterViews();
//        Assert.assertTrue(map.size() == 2);
//        Assert.assertTrue(map.get("U123").size() == 6);
//        Assert.assertTrue(map.get("U123").get(0).getNoteCount() == 25);
//        Assert.assertTrue(map.get("U123").get(0).getRatedMeasure().equalsIgnoreCase("6.6%"));
//    }
//
//    /**
//     * Case : Mock 学校班级孩子数据, 计算老师最终对应的分数
//     * Result : 通过
//     */
//    @Ignore
//    @Test
//    public void test_teacher_site_admin_view() {
//        AgencyModel agency = new AgencyModel();
//        List<CenterEntity> centerEntities = new ArrayList<>();
//        this.mockData(agency, centerEntities);
//
//        WeeklyUserView weeklyUserView = weeklyService.getWeeklyTeacherAndSiteAdminView(agency, centerEntities);
//        Map<String, List<Map<String, Object>>> map = weeklyUserView.getWeeklyTeacherAndSiteAdminModel().getAllSiteAdminAndTeacherEmail();
//        Assert.assertTrue(map.size() == 2);
//        Assert.assertTrue(map.get("U123").get(0).get("ratedMeasure").equals("6.6%"));
//        Assert.assertTrue(map.get("U123").get(0).get("groupNodes").equals("25"));
//        Assert.assertTrue(((Map<String, Integer>) map.get("U123").get(0).get("allChildNoteCount")).size() == 1);
//    }
//
//    /**
//     * Case : Mock 学校班级孩子数据, 对比老师最终需要发送的邮件
//     * Result : 通过
//     */
//    @Ignore
//    @Test
//    public void test_send_teacher_site_admin_email() {
//        AgencyModel agency = new AgencyModel();
//        List<CenterEntity> centerEntities = new ArrayList<>();
//        this.mockData(agency, centerEntities);
//
//        WeeklyUserView weeklyUserView = weeklyService.getWeeklyTeacherAndSiteAdminView(agency, centerEntities);
//        Map<String, List<Map<String, Object>>> map = weeklyUserView.getWeeklyTeacherAndSiteAdminModel().getAllSiteAdminAndTeacherEmail();
//        Assert.assertTrue(map.size() == 2);
//        Assert.assertTrue(map.get("U123").get(0).get("ratedMeasure").equals("6.6%"));
//        Assert.assertTrue(map.get("U123").get(0).get("groupNodes").equals("25"));
//        Assert.assertTrue(((Map<String, Integer>) map.get("U123").get(0).get("allChildNoteCount")).size() == 1);
//
//        UserModel user = new UserModel();
//        user.setId("U123");
//        user.setEmail("<EMAIL>");
//        PowerMockito.spy(TimeUtil.class); // 创建spy,如果不创建的话，后面调用WebChatUtil就都是Mock类，这里创建了spy后，只有设置了mock的方法才会调用mock行为
//        //PowerMockito.doReturn(cal.getTime )).when(TimeUtil.class, "getUtcNow"); //Mock私有方法
//        Calendar cal = Calendar.getInstance();
//        cal.set(2017, Calendar.JULY, 28, 16, 6, 2);//星期5 上午9点
//        PowerMockito.when(TimeUtil.getUtcNow()).thenReturn(cal.getTime());
//        String html = weeklyService.sendTeacherAndSiteAdminEmail(user, weeklyUserView.getWeeklyTeacherAndSiteAdminModel());
//        String htmlTemp = "<tr><td colspan=\"4\" style=\"padding:0 20px 20px 15px;\"><strong style=\"padding:0;margin: 0;font-size: 19px;\">Weekly SummaryJul 28, 2017</strong></td></tr><tr><td style=\"padding:0 15px; font-size: 18px;\" colspan=\"4\">All Sites <span>({AllSitesCount})</span></td></tr>";
//        Assert.assertTrue(htmlTemp.equalsIgnoreCase(html));
//    }
//
//    private void mockData(AgencyModel agency, List<CenterEntity> centerEntities) {
//        agency.setId("A123");
//        agency.setName("AName");
//        Set<DomainEntity> domainEntities = new HashSet<>();
//        Set<EnrollmentEntity> enrollmentEntities = new HashSet<>();
//        String mostUsedAlias = "2016-2017 Summary";
//
//        for (int i = 0; i < 5; i++) {
//            DomainEntity domain = new DomainEntity();
//            domain.setId("D" + i);
//            domainEntities.add(domain);
//        }
//
//        for (int i = 0; i < 5; i++) {
//            CenterEntity center = new CenterEntity();
//            center.setId("C" + i);
//            center.setName("C" + i);
//            DomainEntity domain = new DomainEntity();
//            domain.setId("D" + i);
//            domain.setName("D" + i);
//            GroupPeriodEntity groupPeriod = new GroupPeriodEntity();
//            groupPeriod.setAlias(mostUsedAlias);
//            GroupEntity group = new GroupEntity();
//            group.setId("G" + i);
//            group.setName("G" + i);
//            group.setDomain(domain);
//            group.setCurrentPeriod(groupPeriod);
//            for (int j = 0; j < 5; j++) {
//                EnrollmentEntity enrollment = new EnrollmentEntity();
//                enrollment.setId("E" + i);
//                enrollment.setDisplayName("E" + i);
//                group.getEnrollments().add(enrollment);
//                enrollmentEntities.add(enrollment);
//            }
//            center.getGroups().add(group);
//            centerEntities.add(center);
//        }
//        List<UserModel> users = new ArrayList<>();
//        UserModel user = new UserModel();
//        user.setId("U123");
//        user.setEmail("<EMAIL>");
//        users.add(user);
//
//        user = new UserModel();
//        user.setId("U1234");
//        user.setEmail("<EMAIL>");
//        users.add(user);
//
//        CacheEntity cache = new CacheEntity();
//        cache.setId(1);
//        cache.setValue("D0");
//
//        String html = "<tr><td colspan=\"4\" style=\"padding:0 20px 20px 15px;\"><strong style=\"padding:0;margin: 0;font-size: 19px;\">" +
//                "Weekly Summary{NowDate}</strong></td></tr><tr><td style=\"padding:0 15px; font-size: 18px;\" colspan=\"4\">All Sites " +
//                "<span>({AllSitesCount})</span></td></tr>";
//
//        List<NoteEntity> notes = new ArrayList<>();
//        NoteEntity note;
//        for (int i = 0; i < 5; i++) {
//            note = new NoteEntity();
//            note.setId("N" + i);
//            note.setDomains(domainEntities);
//            note.setEnrollments(enrollmentEntities);
//            for (int j = 0; j < 5; j++) {
//                GroupEntity group = new GroupEntity();
//                group.setId("G" + j);
//                group.setName("G" + j);
//                note.getGroups().add(group);
//            }
//            notes.add(note);
//        }
//
//        List<String> domainIds = new ArrayList<>();
//        domainIds.add("D0");
//        domainIds.add("D2");
//        domainIds.add("D1");
//
//        List<StudentScoreEntity> scoreEntities = new ArrayList<>();
//        StudentScoreEntity score;
//        for (int i = 0; i < 5; i++) {
//            score = new StudentScoreEntity();
//            score.setId("S" + i);
//            score.setDomainId("D" + i);
//            score.setStudentId("E" + i);
//            score.setGroupId("G" + i);
//            scoreEntities.add(score);
//        }
//
//        List<com.learninggenie.common.data.model.UserEntity> userEntities = new ArrayList<>();
//        com.learninggenie.common.data.model.UserEntity userEntity = new com.learninggenie.common.data.model.UserEntity();
//        userEntity.setId("U123");
//        userEntity.setEmail("<EMAIL>");
//        userEntities.add(userEntity);
//
//        PowerMockito.mockStatic(ResourceUtil.class);
//        PowerMockito.when(ResourceUtil.getResourceAsStringNoLang(anyString())).thenReturn(html);
//        when(scoreDao.getNoteScore(anyString(), anyString(), anyString(), anyString())).thenReturn(scoreEntities);
//        when(domainDao.getAllChildDomainIds(anyString())).thenReturn(domainIds);
//        when(noteDao.getAllNotes(anyString(), anyString(), anyString(), anyString(), anyString())).thenReturn(notes);
//        when(noteDao.getAllNotes(anyString(), anyString(), anyString(), anyString())).thenReturn(notes);
//        when(cacheDao.getByKey(anyString())).thenReturn(cache);
//        when(userDao.getAgencyAdminsByAgencyId(anyString())).thenReturn(users);
//        when(userDao.getTeacherByAgencyId(anyString())).thenReturn(users);
//        userEntity = new com.learninggenie.common.data.model.UserEntity();
//        when(groupDao.getTeacherByGroupIdNoSiteAdmin("G1")).thenReturn(userEntities);
//        userEntity.setId("U1234");
//        userEntity.setEmail("<EMAIL>");
//        userEntities.add(userEntity);
//        when(groupDao.getTeacherByGroupIdNoSiteAdmin("G0")).thenReturn(userEntities);
//    }
//
//    /**
//     * 测试Weekly多语言显示
//     */
//    @Test
//    public void testStatisticsAgencyWeekly_lang() throws Exception {
//        List<AgencyModel> agencies = new ArrayList<>();
//        AgencyModel agency = new AgencyModel();
//        agency.setId("A123");
//        agency.setAgencyTimeZone("America/Los_Angeles");
//        agencies.add(agency);
//
//        List<AgencySummaryReportEntity> summaryReportEntities = new ArrayList<>();
//        AgencySummaryReportEntity report = new AgencySummaryReportEntity();
//
//        Calendar cal = Calendar.getInstance();
//        cal.setTime(TimeUtil.getUtcNow());
//        cal.add(Calendar.DATE, -6);
//        report.setCreateAtUtc(cal.getTime());
//        summaryReportEntities.add(report);
//        List<CenterGroupEnrollmentModel> centerGroupEnrollmentModels = new ArrayList<>();
//        for (int i = 0; i < 5; i++) {
//            CenterGroupEnrollmentModel centerGroupEnrollment = new CenterGroupEnrollmentModel();
//            centerGroupEnrollment.setCenterId("C" + i);
//            centerGroupEnrollment.setGroupId("G" + i);
//            centerGroupEnrollment.setGroupAlias("GA123");
//            centerGroupEnrollment.setGroupIsDeleted(false);
//            centerGroupEnrollment.setGroupIsInvatived(false);
//            centerGroupEnrollment.setChildPeriodIsActived(false);
//            centerGroupEnrollment.setEnrollmentIsDeleted(false);
//            centerGroupEnrollment.setEnrollmentId("E" + i);
//            centerGroupEnrollment.setEnrollmentAlias("EA123");
//            centerGroupEnrollment.setCenterName("C_NAME");
//            centerGroupEnrollment.setDomainId("D123");
//            centerGroupEnrollment.setDomainName("D_NAME");
//            centerGroupEnrollment.setCenterTimeZone("America/Los_Angeles");
//            centerGroupEnrollmentModels.add(centerGroupEnrollment);
//        }
//
//        List<UserModel> agencyAdmins = new ArrayList<>();
//        UserModel user1 = new UserModel();
//        user1.setId("U123");
//        user1.setEmail("<EMAIL>");
//        agencyAdmins.add(user1);
//
//        UserModel user2 = new UserModel();
//        user2.setId("U122");
//        user1.setEmail("<EMAIL>");
//        agencyAdmins.add(user2);
//
//        UserModel user3 = new UserModel();
//        user2.setId("U121");
//        user1.setEmail("<EMAIL>");
//        agencyAdmins.add(user3);
//
//        CacheEntity cache = new CacheEntity();
//        cache.setValue("");
//
//        when(userDao.getUserLang("U123")).thenReturn("zh-CN");
//        when(userDao.getUserLang("U122")).thenReturn("en");
//        when(userDao.getUserLang("U121")).thenReturn("");
//
//        when(cacheDao.getByKey(anyString())).thenReturn(cache);
//        when(agencyDao.getCenterGroupEnrollmentByAgencyId(anyString())).thenReturn(centerGroupEnrollmentModels);
//        when(agencyDao.getMostUsedAliasByAgencyId(anyString())).thenReturn("Alias");
//        when(reportDao.getWeeklySummaryRecord(anyString(), anyByte(), anyByte())).thenReturn(summaryReportEntities);
//        when(userDao.getAgencyAdminsByAgencyId(anyString())).thenReturn(agencyAdmins);
//
//        PowerMockito.mockStatic(ResourceUtil.class);
//        PowerMockito.when(ResourceUtil.getResourceAsStringNoLang(anyString())).thenReturn("<p>123{AllSiteNotes}");
//
//        weeklyService.statisticsAgencyWeekly(agencies);
//        //当天未发送
//        verify(agencyDao, times(1)).getCenterGroupEnrollmentByAgencyId(Mockito.anyString());
//        verify(agencyDao, times(1)).getMostUsedAliasByAgencyId(Mockito.anyString());
//        verify(emailService, times(3)).sendAsync(Mockito.any(EmailModel.class));
//        verify(userDao, times(3)).getUserLang(Mockito.any(String.class));
//
//        String user1Html = weeklyService.sendUserWeeklyEmail(user1, "{m1}Test{m2}Test{m3}", "", "");
//        String user2Html = weeklyService.sendUserWeeklyEmail(user2, "{m1}Test{m2}Test{m3}", "", "");
//        String user3Html = weeklyService.sendUserWeeklyEmail(user3, "{m1}Test{m2}Test{m3}", "", "");
//
//        Assert.assertTrue(user2Html.equalsIgnoreCase(user3Html));
//        Assert.assertTrue("学生数Test学生总数:Test完全完成".equalsIgnoreCase(user1Html));
//        Assert.assertTrue("Total children countTestTotal Children Counts:TestFully Completed".equalsIgnoreCase(user3Html));
//    }
//
//}