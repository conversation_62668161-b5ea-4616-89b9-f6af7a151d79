//package com.learninggenie.common.push;
//
//import com.learninggenie.common.comm.CommService;
//import com.learninggenie.common.data.dao.*;
//import com.learninggenie.common.data.dao.impl.UserDaoImpl;
//import com.learninggenie.common.data.entity.*;
//import com.learninggenie.common.data.entity.CenterEntity;
//import com.learninggenie.common.data.enums.AgencyMetaKey;
//import com.learninggenie.common.data.model.*;
//import com.learninggenie.common.data.model.UserEntity;
//import com.learninggenie.common.data.model.hyphenate.GoogleVideoHyphenate;
//import com.learninggenie.common.data.model.push.*;
//import com.learninggenie.common.data.repository.CenterRepository;
//import com.learninggenie.common.data.repository.GroupRepository;
//import com.learninggenie.common.exception.LearningGenieRuntimeException;
//import com.learninggenie.common.filesystem.FileSystem;
//import com.learninggenie.common.region.RegionService;
//import com.learninggenie.common.utils.ResourceUtil;
//import com.learninggenie.common.utils.RestApiUtil;
//import com.learninggenie.common.utils.TimeUtil;
//import org.junit.Assert;
//import org.junit.Ignore;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.Mockito;
//import org.powermock.api.mockito.PowerMockito;
//import org.powermock.core.classloader.annotations.PrepareForTest;
//import org.powermock.modules.junit4.PowerMockRunner;
//
//import java.util.*;
//
//import static org.mockito.Mockito.when;
//@Ignore
//@RunWith(PowerMockRunner.class)
//@PrepareForTest({TimeUtil.class, ResourceUtil.class, RestApiUtil.class})
//public class PushServiceImplTest {
//    @InjectMocks
//    private PushServiceImpl pushService;
//    @Mock
//    private AgencyDao agencyDao;
//    @Mock
//    private PushNotificationDao pushNotificationDao;
//    @Mock
//    private UserDaoImpl userDao;
//    @Mock
//    private MediaBookDao mediaBookDao;
//    @Mock
//    private CenterDao centerDao;
//    @Mock
//    private RegionService regionService;
//    @Mock
//    private CommService commService;
//    @Mock
//    private StudentDao studentDao;
//    @Mock
//    private FileSystem fileSystem;
//    @Mock
//    private GroupRepository groupRepository;
//    @Mock
//    private GroupDao groupDao;
//    @Mock
//    private CenterRepository centerRepository;
//
//    /**
//     * 正常推送一条消息
//     * 时区洛杉矶
//     * 家长开通聊天功能
//     * 未给此家长发送过
//     *
//     * @throws Exception
//     */
//    @Test
//    public void pushNotificationServices_America() throws Exception {
//
//        String timeZone = "America/Los_Angeles";
//
//        Date utcNow = TimeUtil.getUtcNow();
//        Date localNow = TimeUtil.convertUtcToLocal(utcNow, timeZone);
//
//        AgencyModel agencyModel = new AgencyModel();
//        agencyModel.setId("a001");
//        agencyModel.setName("toto");
//
//        List<AgencyModel> agencyModels = new ArrayList<>();
//        agencyModels.add(agencyModel);
//
//        PushNotificationModel pushNotificationModel = new PushNotificationModel();
//        pushNotificationModel.setId("p001");
//        pushNotificationModel.setPushId("p001");
//        pushNotificationModel.setPushTime(localNow);
//        pushNotificationModel.setAgencyId("a001");
//        pushNotificationModel.setMediaId("v001");
//        pushNotificationModel.setMediaCategory("Shawn Brown");
//        pushNotificationModel.setCenterId("c001");
//        pushNotificationModel.setGroupId("g001");
//
//        List<PushNotificationModel> pushes = new ArrayList<>();
//        pushes.add(pushNotificationModel);
//
//        GoogleVideoHyphenate googleVideo = new GoogleVideoHyphenate();
//        googleVideo.setSongInfo("songInfo");
//        googleVideo.setTitle("hi");
//        googleVideo.setImgUrl("imgUrl");
//
//        UserModel user = new UserModel();
//        user.setRole("AGENCY_OWNER");
//        user.setId("u001");
//        user.setDisplayName("agencyOwner");
//
//        String parentId = "p001";
//        List<String> parentsId = new ArrayList<>();
//
//        EnrollmentEntity enrollment = new EnrollmentEntity();
//        enrollment.setId("e001");
//        enrollment.setDisplayName("enrollment");
//
//        List<EnrollmentEntity> enrollmentEntityList = new ArrayList<>();
//        enrollmentEntityList.add(enrollment);
//
//        UserEntity parent = new UserEntity();
//        parent.setId("parent001");
//        parent.setName("parent");
//
//        List<UserEntity> parents = new ArrayList<>();
//        parents.add(parent);
//
//        ResultPojo resultPojo = new ResultPojo();
//        resultPojo.setStatus(200);
//
//        UserModel parentModel = new UserModel();
//        parentModel.setId("parent001");
//        parentModel.setRole("parent");
//
//        AgencyEntity agencyEntity = new AgencyEntity();
//        agencyEntity.setId("a001");
//        agencyEntity.setName("toto");
//
//        List<AgencyEntity> agencyEntities = new ArrayList<>();
//        agencyEntities.add(agencyEntity);
//
//        List<EnrollmentModel> enrollmentModels = new ArrayList<>();
//
//        EnrollmentModel enrollmentModel = new EnrollmentModel();
//        enrollmentModel.setId("e001");
//        enrollmentModel.setDisplayName("enrollment");
//        enrollmentModels.add(enrollmentModel);
//
//        List<String> ids = new ArrayList<>();
//        ids.add("g001");
//
//        Mockito.when(agencyDao.getAgencyByParentId("parent001")).thenReturn(agencyEntities);
//        Mockito.when(userDao.getUserById("parent001")).thenReturn(parentModel);
//        PowerMockito.when(agencyDao.getPushNotifyAgency("IS_PUSH", "true")).thenReturn(agencyModels);
//        Mockito.when(centerDao.getFirstCenterTimeZoneByAgencyId("a001")).thenReturn(timeZone);
//        Mockito.when(pushNotificationDao.getTodayAgencyPushByTime(Mockito.anyString(), Mockito.anyString())).thenReturn(pushes);
//        Mockito.when(commService.getGoogleVideos("v001", "Shawn Brown", null)).thenReturn(googleVideo);
//        Mockito.when(pushNotificationDao.getParentsPushRecord("txt", "p001")).thenReturn(parentsId);
//        Mockito.when(studentDao.getEnrollmentByAgencyId("a001")).thenReturn(enrollmentEntityList);
//        Mockito.when(userDao.getParentsByStudentId("e001")).thenReturn(parents);
//        Mockito.when(commService.getUser("PARENT001LG")).thenReturn(resultPojo);
//        Mockito.when(commService.sendMessages(Mockito.anyObject(), Mockito.eq("a001"), Mockito.eq("America/Los_Angeles"), Mockito.eq("v001"), Mockito.eq("p001"))).thenReturn(resultPojo);
//        Mockito.when(studentDao.getChildrenByGroupIds(ids, null)).thenReturn(enrollmentModels);
//        Mockito.when(studentDao.getChildrenByCenterIds(ids, null)).thenReturn(enrollmentModels);
//
//        pushService.pushNotificationServices();
//
//        Mockito.verify(userDao, Mockito.times(1)).getParentsByStudentId("e001");
//        Mockito.verify(commService, Mockito.times(1)).getUser("PARENT001LG");
//        Mockito.verify(commService, Mockito.times(1)).sendMessages(Mockito.anyObject(), Mockito.eq("a001"), Mockito.eq("America/Los_Angeles"), Mockito.eq("v001"), Mockito.eq("p001"));
////        Mockito.verify(commService, Mockito.times(1)).addPushReport(Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject());
//        Mockito.verify(commService, Mockito.times(1)).sendMessages(Mockito.anyObject(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString());
//        Mockito.verify(pushNotificationDao, Mockito.times(1)).setSendStatus(Mockito.eq("p001"), Mockito.eq(true), Mockito.anyObject());
//    }
//
//
//    /**
//     * 正常推送一条消息
//     * 时区上海
//     * 家长没有开通聊天功能
//     * 给此家长发送过
//     *
//     * @throws Exception
//     */
//    @Test
//    public void pushNotificationServices_Shanghai() throws Exception {
//
//        String timeZone = "Asia/Shanghai";
//
//        Date utcNow = TimeUtil.getUtcNow();
//        Date localNow = TimeUtil.convertUtcToLocal(utcNow, timeZone);
//
//        AgencyModel agencyModel = new AgencyModel();
//        agencyModel.setId("a001");
//        agencyModel.setName("toto");
//
//        List<AgencyModel> agencyModels = new ArrayList<>();
//        agencyModels.add(agencyModel);
//
//        PushNotificationModel pushNotificationModel = new PushNotificationModel();
//        pushNotificationModel.setId("p001");
//        pushNotificationModel.setPushId("p001");
//        pushNotificationModel.setPushTime(localNow);
//        pushNotificationModel.setAgencyId("a001");
//        pushNotificationModel.setMediaId("v001");
//        pushNotificationModel.setMediaCategory("Shawn Brown");
//        pushNotificationModel.setCenterId("c001");
//        pushNotificationModel.setGroupId("g001");
//
//        List<PushNotificationModel> pushes = new ArrayList<>();
//        pushes.add(pushNotificationModel);
//
//        GoogleVideoHyphenate googleVideo = new GoogleVideoHyphenate();
//        googleVideo.setSongInfo("songInfo");
//        googleVideo.setTitle("hi");
//        googleVideo.setImgUrl("imgUrl");
//
//        UserModel user = new UserModel();
//        user.setRole("AGENCY_OWNER");
//        user.setId("u001");
//        user.setDisplayName("agencyOwner");
//
//        String parentId = "p001";
//        List<String> parentsId = new ArrayList<>();
//
//        EnrollmentEntity enrollment = new EnrollmentEntity();
//        enrollment.setId("e001");
//        enrollment.setDisplayName("enrollment");
//
//        List<EnrollmentEntity> enrollmentEntityList = new ArrayList<>();
//        enrollmentEntityList.add(enrollment);
//
//        UserEntity parent = new UserEntity();
//        parent.setId("parent001");
//        parent.setName("parent");
//
//        List<UserEntity> parents = new ArrayList<>();
//        parents.add(parent);
//
//        ResultPojo resultPojo = new ResultPojo();
//        resultPojo.setStatus(200);
//
//        ResultPojo resultPojoError = new ResultPojo();
//        resultPojo.setStatus(500);
//
//        UserModel parentModel = new UserModel();
//        parentModel.setId("parent001");
//        parentModel.setRole("parent");
//
//        AgencyEntity agencyEntity = new AgencyEntity();
//        agencyEntity.setId("a001");
//        agencyEntity.setName("toto");
//
//        List<AgencyEntity> agencyEntities = new ArrayList<>();
//        agencyEntities.add(agencyEntity);
//
//        List<EnrollmentModel> enrollmentModels = new ArrayList<>();
//
//        EnrollmentModel enrollmentModel = new EnrollmentModel();
//        enrollmentModel.setId("e001");
//        enrollmentModel.setDisplayName("enrollment");
//        enrollmentModels.add(enrollmentModel);
//
//        List<String> ids = new ArrayList<>();
//        ids.add("g001");
//
//        Mockito.when(agencyDao.getAgencyByParentId("parent001")).thenReturn(agencyEntities);
//        Mockito.when(userDao.getUserById("parent001")).thenReturn(parentModel);
//
//        PowerMockito.when(agencyDao.getPushNotifyAgency("IS_PUSH", "true")).thenReturn(agencyModels);
//        Mockito.when(centerDao.getFirstCenterTimeZoneByAgencyId("a001")).thenReturn(timeZone);
//        Mockito.when(pushNotificationDao.getTodayAgencyPushByTime(Mockito.anyString(), Mockito.anyString())).thenReturn(pushes);
//        Mockito.when(commService.getGoogleVideos("v001", "Shawn Brown", null)).thenReturn(googleVideo);
//        Mockito.when(pushNotificationDao.getParentsPushRecord("txt", "p001")).thenReturn(parentsId);
//        Mockito.when(studentDao.getEnrollmentByAgencyId("a001")).thenReturn(enrollmentEntityList);
//        Mockito.when(userDao.getParentsByStudentId("e001")).thenReturn(parents);
//        Mockito.when(commService.getUser("PARENT001LG")).thenReturn(resultPojoError);
//        Mockito.when(commService.sendMessages(Mockito.anyObject(), Mockito.eq("a001"), Mockito.eq("Asia/Shanghai"), Mockito.eq("v001"), Mockito.eq("p001"))).thenReturn(resultPojo);
//        Mockito.when(studentDao.getChildrenByGroupIds(ids, null)).thenReturn(enrollmentModels);
//        Mockito.when(studentDao.getChildrenByCenterIds(ids, null)).thenReturn(enrollmentModels);
//
//        pushService.pushNotificationServices();
//
//        Mockito.verify(userDao, Mockito.times(1)).getParentsByStudentId("e001");
//        Mockito.verify(commService, Mockito.times(1)).getUser("PARENT001LG");
//        Mockito.verify(commService, Mockito.times(0)).sendMessages(Mockito.anyObject(), Mockito.eq("a001"), Mockito.eq("America/Los_Angeles"), Mockito.eq("v001"), Mockito.eq("p001"));
////        Mockito.verify(commService, Mockito.times(1)).addPushReport(Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject());
//        Mockito.verify(commService, Mockito.times(1)).signUser("parent001");
//        Mockito.verify(commService, Mockito.times(4)).sendMessages(Mockito.anyObject(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString());
//        Mockito.verify(pushNotificationDao, Mockito.times(1)).setSendStatus(Mockito.eq("p001"), Mockito.eq(true), Mockito.anyObject());
//    }
//
//    /**
//     * 没有时区
//     *
//     * @throws Exception
//     */
//    @Test
//    public void pushNotificationServices_NoTimeZone() throws Exception {
//
//        String timeZone = "";
//
//        Date utcNow = TimeUtil.getUtcNow();
//        Date localNow = TimeUtil.convertUtcToLocal(utcNow, timeZone);
//
//        AgencyModel agencyModel = new AgencyModel();
//        agencyModel.setId("a001");
//        agencyModel.setName("toto");
//
//        List<AgencyModel> agencyModels = new ArrayList<>();
//        agencyModels.add(agencyModel);
//
//        PushNotificationModel pushNotificationModel = new PushNotificationModel();
//        pushNotificationModel.setId("p001");
//        pushNotificationModel.setPushId("p001");
//        pushNotificationModel.setPushTime(localNow);
//        pushNotificationModel.setAgencyId("a001");
//        pushNotificationModel.setMediaId("v001");
//        pushNotificationModel.setMediaCategory("Shawn Brown");
//
//        List<PushNotificationModel> pushes = new ArrayList<>();
//        pushes.add(pushNotificationModel);
//
//        GoogleVideoHyphenate googleVideo = new GoogleVideoHyphenate();
//        googleVideo.setSongInfo("songInfo");
//        googleVideo.setTitle("hi");
//        googleVideo.setImgUrl("imgUrl");
//
//        UserModel user = new UserModel();
//        user.setRole("AGENCY_OWNER");
//        user.setId("u001");
//        user.setDisplayName("agencyOwner");
//
//        String parentId = "p001";
//        List<String> parentsId = new ArrayList<>();
//        parentsId.add(parentId);
//
//        EnrollmentEntity enrollment = new EnrollmentEntity();
//        enrollment.setId("e001");
//        enrollment.setDisplayName("enrollment");
//
//        List<EnrollmentEntity> enrollmentEntityList = new ArrayList<>();
//        enrollmentEntityList.add(enrollment);
//
//        UserEntity parent = new UserEntity();
//        parent.setId("parent001");
//        parent.setName("parent");
//
//        List<UserEntity> parents = new ArrayList<>();
//        parents.add(parent);
//
//        ResultPojo resultPojo = new ResultPojo();
//        resultPojo.setStatus(200);
//
//        ResultPojo resultPojoError = new ResultPojo();
//        resultPojo.setStatus(500);
//
//        UserModel parentModel = new UserModel();
//        parentModel.setId("parent001");
//        parentModel.setRole("parent");
//
//        AgencyEntity agencyEntity = new AgencyEntity();
//        agencyEntity.setId("a001");
//        agencyEntity.setName("toto");
//
//        List<AgencyEntity> agencyEntities = new ArrayList<>();
//        agencyEntities.add(agencyEntity);
//
//        Mockito.when(agencyDao.getAgencyByParentId("parent001")).thenReturn(agencyEntities);
//        Mockito.when(userDao.getUserById("parent001")).thenReturn(parentModel);
//        PowerMockito.when(agencyDao.getPushNotifyAgency("IS_PUSH", "true")).thenReturn(agencyModels);
//        Mockito.when(centerDao.getFirstCenterTimeZoneByAgencyId("a001")).thenReturn(timeZone);
//        Mockito.when(pushNotificationDao.getTodayAgencyPushByTime(Mockito.anyString(), Mockito.anyString())).thenReturn(pushes);
//        Mockito.when(commService.getGoogleVideos("v001", "Shawn Brown", null)).thenReturn(googleVideo);
//        Mockito.when(pushNotificationDao.getParentsPushRecord("txt", "p001")).thenReturn(parentsId);
//        Mockito.when(studentDao.getEnrollmentByAgencyId("a001")).thenReturn(enrollmentEntityList);
//        Mockito.when(userDao.getParentsByStudentId("e001")).thenReturn(parents);
//        Mockito.when(commService.getUser("PARENT001LG")).thenReturn(resultPojoError);
//        Mockito.when(commService.sendMessages(Mockito.anyObject(), Mockito.eq("a001"), Mockito.eq("Asia/Shanghai"), Mockito.eq("v001"), Mockito.eq("p001"))).thenReturn(resultPojo);
//
//        pushService.pushNotificationServices();
//
//        Mockito.verify(studentDao, Mockito.times(0)).getEnrollmentByAgencyId("a001");
//        Mockito.verify(userDao, Mockito.times(0)).getParentsByStudentId("e001");
//        Mockito.verify(commService, Mockito.times(0)).getUser("PARENT001LG");
//        Mockito.verify(commService, Mockito.times(0)).sendMessages(Mockito.anyObject(), Mockito.eq("a001"), Mockito.eq("America/Los_Angeles"), Mockito.eq("v001"), Mockito.eq("p001"));
////        Mockito.verify(commService, Mockito.times(0)).addPushReport(Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject());
//        Mockito.verify(commService, Mockito.times(0)).signUser("PARENT001LG");
//        Mockito.verify(commService, Mockito.times(0)).sendMessages(Mockito.anyObject(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString());
//        Mockito.verify(pushNotificationDao, Mockito.times(0)).setSendStatus(Mockito.eq("p001"), Mockito.eq(true), Mockito.anyObject());
//    }
//
//    /**
//     * 没有推送
//     *
//     * @throws Exception
//     */
//    @Test
//    public void pushNotificationServices_NoPushes() throws Exception {
//
//        String timeZone = "Asia/Shanghai";
//
//        Date utcNow = TimeUtil.getUtcNow();
//        Date localNow = TimeUtil.convertUtcToLocal(utcNow, timeZone);
//
//        AgencyModel agencyModel = new AgencyModel();
//        agencyModel.setId("a001");
//        agencyModel.setName("toto");
//
//        List<AgencyModel> agencyModels = new ArrayList<>();
//        agencyModels.add(agencyModel);
//
//        PushNotificationModel pushNotificationModel = new PushNotificationModel();
//        pushNotificationModel.setId("p001");
//        pushNotificationModel.setPushId("p001");
//        pushNotificationModel.setPushTime(localNow);
//        pushNotificationModel.setAgencyId("a001");
//        pushNotificationModel.setMediaId("v001");
//        pushNotificationModel.setMediaCategory("Shawn Brown");
//
//        List<PushNotificationModel> pushes = new ArrayList<>();
//
//        GoogleVideoHyphenate googleVideo = new GoogleVideoHyphenate();
//        googleVideo.setSongInfo("songInfo");
//        googleVideo.setTitle("hi");
//        googleVideo.setImgUrl("imgUrl");
//
//        UserModel user = new UserModel();
//        user.setRole("AGENCY_OWNER");
//        user.setId("u001");
//        user.setDisplayName("agencyOwner");
//
//        String parentId = "p001";
//        List<String> parentsId = new ArrayList<>();
//        parentsId.add(parentId);
//
//        EnrollmentEntity enrollment = new EnrollmentEntity();
//        enrollment.setId("e001");
//        enrollment.setDisplayName("enrollment");
//
//        List<EnrollmentEntity> enrollmentEntityList = new ArrayList<>();
//        enrollmentEntityList.add(enrollment);
//
//        UserEntity parent = new UserEntity();
//        parent.setId("parent001");
//        parent.setName("parent");
//
//        List<UserEntity> parents = new ArrayList<>();
//        parents.add(parent);
//
//        ResultPojo resultPojo = new ResultPojo();
//        resultPojo.setStatus(200);
//
//        ResultPojo resultPojoError = new ResultPojo();
//        resultPojo.setStatus(500);
//
//        UserModel parentModel = new UserModel();
//        parentModel.setId("parent001");
//        parentModel.setRole("parent");
//
//        AgencyEntity agencyEntity = new AgencyEntity();
//        agencyEntity.setId("a001");
//        agencyEntity.setName("toto");
//
//        List<AgencyEntity> agencyEntities = new ArrayList<>();
//        agencyEntities.add(agencyEntity);
//
//        Mockito.when(agencyDao.getAgencyByParentId("parent001")).thenReturn(agencyEntities);
//        Mockito.when(userDao.getUserById("parent001")).thenReturn(parentModel);
//        PowerMockito.when(agencyDao.getPushNotifyAgency("IS_PUSH", "true")).thenReturn(agencyModels);
//        Mockito.when(centerDao.getFirstCenterTimeZoneByAgencyId("a001")).thenReturn(timeZone);
//        Mockito.when(pushNotificationDao.getTodayAgencyPushByTime(Mockito.anyString(), Mockito.anyString())).thenReturn(pushes);
//        Mockito.when(commService.getGoogleVideos("v001", "Shawn Brown", null)).thenReturn(googleVideo);
//        Mockito.when(pushNotificationDao.getParentsPushRecord("txt", "p001")).thenReturn(parentsId);
//        Mockito.when(studentDao.getEnrollmentByAgencyId("a001")).thenReturn(enrollmentEntityList);
//        Mockito.when(userDao.getParentsByStudentId("e001")).thenReturn(parents);
//        Mockito.when(commService.getUser("PARENT001LG")).thenReturn(resultPojoError);
//        Mockito.when(commService.sendMessages(Mockito.anyObject(), Mockito.eq("a001"), Mockito.eq("Asia/Shanghai"), Mockito.eq("v001"), Mockito.eq("p001"))).thenReturn(resultPojo);
//
//        pushService.pushNotificationServices();
//
//        Mockito.verify(studentDao, Mockito.times(0)).getEnrollmentByAgencyId("a001");
//        Mockito.verify(userDao, Mockito.times(0)).getParentsByStudentId("e001");
//        Mockito.verify(commService, Mockito.times(0)).getUser("PARENT001LG");
//        Mockito.verify(commService, Mockito.times(0)).sendMessages(Mockito.anyObject(), Mockito.eq("a001"), Mockito.eq("America/Los_Angeles"), Mockito.eq("v001"), Mockito.eq("p001"));
////        Mockito.verify(commService, Mockito.times(0)).addPushReport(Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject());
//        Mockito.verify(commService, Mockito.times(0)).signUser("PARENT001LG");
//        Mockito.verify(commService, Mockito.times(0)).sendMessages(Mockito.anyObject(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString());
//        Mockito.verify(pushNotificationDao, Mockito.times(0)).setSendStatus(Mockito.eq("p001"), Mockito.eq(true), Mockito.anyObject());
//    }
//
//    /**
//     * 没有agencyOwner
//     *
//     * @throws Exception
//     */
//    @Test
//    public void pushNotificationServices_NoAgencyOwner() throws Exception {
//
//        String timeZone = "Asia/Shanghai";
//
//        Date utcNow = TimeUtil.getUtcNow();
//        Date localNow = TimeUtil.convertUtcToLocal(utcNow, timeZone);
//
//        AgencyModel agencyModel = new AgencyModel();
//        agencyModel.setId("a001");
//        agencyModel.setName("toto");
//
//        List<AgencyModel> agencyModels = new ArrayList<>();
//        agencyModels.add(agencyModel);
//
//        PushNotificationModel pushNotificationModel = new PushNotificationModel();
//        pushNotificationModel.setId("p001");
//        pushNotificationModel.setPushId("p001");
//        pushNotificationModel.setPushTime(localNow);
//        pushNotificationModel.setAgencyId("a001");
//        pushNotificationModel.setMediaId("v001");
//        pushNotificationModel.setMediaCategory("Shawn Brown");
//
//        List<PushNotificationModel> pushes = new ArrayList<>();
//        pushes.add(pushNotificationModel);
//
//        GoogleVideoHyphenate googleVideo = new GoogleVideoHyphenate();
//        googleVideo.setSongInfo("songInfo");
//        googleVideo.setTitle("hi");
//        googleVideo.setImgUrl("imgUrl");
//
//        UserModel user = new UserModel();
//        user.setRole("AGENCY_OWNER");
//        user.setId("u001");
//        user.setDisplayName("agencyOwner");
//
//        List<UserModel> AgencyUsers = new ArrayList<>();
//
//
//        String parentId = "p001";
//        List<String> parentsId = new ArrayList<>();
//        parentsId.add(parentId);
//
//        EnrollmentEntity enrollment = new EnrollmentEntity();
//        enrollment.setId("e001");
//        enrollment.setDisplayName("enrollment");
//
//        List<EnrollmentEntity> enrollmentEntityList = new ArrayList<>();
//        enrollmentEntityList.add(enrollment);
//
//        UserEntity parent = new UserEntity();
//        parent.setId("parent001");
//        parent.setName("parent");
//
//        List<UserEntity> parents = new ArrayList<>();
//        parents.add(parent);
//
//        ResultPojo resultPojo = new ResultPojo();
//        resultPojo.setStatus(200);
//
//        ResultPojo resultPojoError = new ResultPojo();
//        resultPojo.setStatus(500);
//
//        UserModel parentModel = new UserModel();
//        parentModel.setId("parent001");
//        parentModel.setRole("parent");
//
//        AgencyEntity agencyEntity = new AgencyEntity();
//        agencyEntity.setId("a001");
//        agencyEntity.setName("toto");
//
//        List<AgencyEntity> agencyEntities = new ArrayList<>();
//        agencyEntities.add(agencyEntity);
//
//        Mockito.when(agencyDao.getAgencyByParentId("parent001")).thenReturn(agencyEntities);
//        Mockito.when(userDao.getUserById("parent001")).thenReturn(parentModel);
//        PowerMockito.when(agencyDao.getPushNotifyAgency("IS_PUSH", "true")).thenReturn(agencyModels);
//        Mockito.when(centerDao.getFirstCenterTimeZoneByAgencyId("a001")).thenReturn(timeZone);
//        Mockito.when(pushNotificationDao.getTodayAgencyPushByTime(Mockito.anyString(), Mockito.anyString())).thenReturn(pushes);
//        Mockito.when(commService.getGoogleVideos("v001", "Shawn Brown", null)).thenReturn(googleVideo);
//        Mockito.when(pushNotificationDao.getParentsPushRecord("txt", "p001")).thenReturn(parentsId);
//        Mockito.when(studentDao.getEnrollmentByAgencyId("a001")).thenReturn(enrollmentEntityList);
//        Mockito.when(userDao.getParentsByStudentId("e001")).thenReturn(parents);
//        Mockito.when(commService.getUser("PARENT001LG")).thenReturn(resultPojoError);
//        Mockito.when(commService.sendMessages(Mockito.anyObject(), Mockito.eq("a001"), Mockito.eq("Asia/Shanghai"), Mockito.eq("v001"), Mockito.eq("p001"))).thenReturn(resultPojo);
//        Mockito.when(userDao.getAgencyOwnerByAgencyId("a001")).thenReturn(AgencyUsers);
//
//        pushService.pushNotificationServices();
//
//        Mockito.verify(userDao, Mockito.times(1)).getAgencyOwnerByAgencyId("a001");
//        Mockito.verify(commService, Mockito.times(0)).sendMessages(Mockito.anyObject(), Mockito.eq("a001"), Mockito.eq("America/Los_Angeles"), Mockito.eq("v001"), Mockito.eq("p001"));
//    }
//
//    /**
//     * 已经发送过
//     *
//     * @throws Exception
//     */
//    @Test
//    public void pushNotificationServices_HadSend() throws Exception {
//
//        String timeZone = "America/Los_Angeles";
//
//        Date utcNow = TimeUtil.getUtcNow();
//        Date localNow = TimeUtil.convertUtcToLocal(utcNow, timeZone);
//
//        AgencyModel agencyModel = new AgencyModel();
//        agencyModel.setId("a001");
//        agencyModel.setName("toto");
//
//        List<AgencyModel> agencyModels = new ArrayList<>();
//        agencyModels.add(agencyModel);
//
//        PushNotificationModel pushNotificationModel = new PushNotificationModel();
//        pushNotificationModel.setId("p001");
//        pushNotificationModel.setPushId("p001");
//        pushNotificationModel.setPushTime(localNow);
//        pushNotificationModel.setAgencyId("a001");
//        pushNotificationModel.setMediaId("v001");
//        pushNotificationModel.setMediaCategory("Shawn Brown");
//        pushNotificationModel.setCenterId("c001");
//        pushNotificationModel.setGroupId("g001");
//
//        List<PushNotificationModel> pushes = new ArrayList<>();
//        pushes.add(pushNotificationModel);
//
//        GoogleVideoHyphenate googleVideo = new GoogleVideoHyphenate();
//        googleVideo.setSongInfo("songInfo");
//        googleVideo.setTitle("hi");
//        googleVideo.setImgUrl("imgUrl");
//
//        UserModel user = new UserModel();
//        user.setRole("AGENCY_OWNER");
//        user.setId("u001");
//        user.setDisplayName("agencyOwner");
//
//        String parentId = "PARENT001LG";
//        List<String> parentsId = new ArrayList<>();
//        parentsId.add(parentId);
//
//        EnrollmentEntity enrollment = new EnrollmentEntity();
//        enrollment.setId("e001");
//        enrollment.setDisplayName("enrollment");
//
//        List<EnrollmentEntity> enrollmentEntityList = new ArrayList<>();
//        enrollmentEntityList.add(enrollment);
//
//        UserEntity parent = new UserEntity();
//        parent.setId("parent001");
//        parent.setName("parent");
//
//        List<UserEntity> parents = new ArrayList<>();
//        parents.add(parent);
//
//        ResultPojo resultPojo = new ResultPojo();
//        resultPojo.setStatus(200);
//
//        UserModel parentModel = new UserModel();
//        parentModel.setId("parent001");
//        parentModel.setRole("parent");
//
//        AgencyEntity agencyEntity = new AgencyEntity();
//        agencyEntity.setId("a001");
//        agencyEntity.setName("toto");
//
//        List<AgencyEntity> agencyEntities = new ArrayList<>();
//        agencyEntities.add(agencyEntity);
//
//        List<EnrollmentModel> enrollmentModels = new ArrayList<>();
//
//        EnrollmentModel enrollmentModel = new EnrollmentModel();
//        enrollmentModel.setId("e001");
//        enrollmentModel.setDisplayName("enrollment");
//        enrollmentModels.add(enrollmentModel);
//
//        List<String> ids = new ArrayList<>();
//        ids.add("g001");
//
//        List<UserModel> agencyUsers = new ArrayList<>();
//        agencyUsers.add(user);
//
//        Mockito.when(userDao.getParentsByStudentId(Mockito.anyString())).thenReturn(parents);
//        Mockito.when(userDao.getAgencyOwnerByAgencyId(Mockito.anyString())).thenReturn(agencyUsers);
//        Mockito.when(studentDao.getChildrenByGroupIds(ids, null)).thenReturn(enrollmentModels);
//        Mockito.when(studentDao.getChildrenByCenterIds(ids, null)).thenReturn(enrollmentModels);
//        Mockito.when(agencyDao.getAgencyByParentId("parent001")).thenReturn(agencyEntities);
//        Mockito.when(userDao.getUserById("parent001")).thenReturn(parentModel);
//        PowerMockito.when(agencyDao.getPushNotifyAgency("IS_PUSH", "true")).thenReturn(agencyModels);
//        Mockito.when(centerDao.getFirstCenterTimeZoneByAgencyId("a001")).thenReturn(timeZone);
//        Mockito.when(pushNotificationDao.getTodayAgencyPushByTime(Mockito.anyString(), Mockito.anyString())).thenReturn(pushes);
//        Mockito.when(commService.getGoogleVideos("v001", "Shawn Brown", null)).thenReturn(googleVideo);
//        Mockito.when(pushNotificationDao.getParentsPushRecord("txt", "p001")).thenReturn(parentsId);
//        Mockito.when(studentDao.getEnrollmentByAgencyId("a001")).thenReturn(enrollmentEntityList);
//        Mockito.when(userDao.getParentsByStudentId("e001")).thenReturn(parents);
//        Mockito.when(commService.getUser("PARENT001LG")).thenReturn(resultPojo);
//        Mockito.when(commService.sendMessages(Mockito.anyObject(), Mockito.eq("a001"), Mockito.eq("America/Los_Angeles"), Mockito.eq("v001"), Mockito.eq("p001"))).thenReturn(resultPojo);
//
//        pushService.pushNotificationServices();
//
//        Mockito.verify(userDao, Mockito.times(1)).getParentsByStudentId("e001");
//        Mockito.verify(commService, Mockito.times(1)).getUser("PARENT001LG");
//        Mockito.verify(commService, Mockito.times(0)).sendMessages(Mockito.anyObject(), Mockito.eq("a001"), Mockito.eq("America/Los_Angeles"), Mockito.eq("v001"), Mockito.eq("p001"));
////        Mockito.verify(commService, Mockito.times(0)).addPushReport(Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject());
//        Mockito.verify(commService, Mockito.times(0)).sendMessages(Mockito.anyObject(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString());
//        Mockito.verify(pushNotificationDao, Mockito.times(1)).setSendStatus(Mockito.eq("p001"), Mockito.eq(true), Mockito.anyObject());
//    }
//
//
//    /**
//     * 开关有总开关开，其他只有activity开
//     *
//     * @throws Exception
//     */
//    @Test
//    public void getAdminPushSwitch() throws Exception {
//        String userId = "u001";
//        UserModel userModel = new UserModel();
//        userModel.setId(userId);
//        userModel.setRole("AGENCY_OWNER");
//
//        AgencyModel agencyModel = new AgencyModel();
//        agencyModel.setId("a001");
//
//        List<AgencyModel> agencyModels = new ArrayList<>();
//        agencyModels.add(agencyModel);
//
//        AgencyMetaDataEntity metaData = new AgencyMetaDataEntity();
//        metaData.setAgencyId("a001");
//        metaData.setMetaValue("{\"ALLOW\":true,\"READING\":false,\"INCIDENT\":false,\"SONG\":false,\"ACTIVITY\":true}");
//        metaData.setMetaKey("PUSH_TYPE");
//        metaData.setId("m001");
//
//        Mockito.when(userDao.getUserLang("u001")).thenReturn("en-US");
//        Mockito.when(userDao.getUserById("u001")).thenReturn(userModel);
//        Mockito.when(userDao.getAgencyByAgencyAdminId("u001")).thenReturn(agencyModels);
//        Mockito.when(agencyDao.getMeta("a001", "PUSH_TYPE")).thenReturn(metaData);
//        when(regionService.isChina()).thenReturn(false);
//
//        PushSwitchResponse response = pushService.getAdminPushSwitch(userId, null);
//
//        Assert.assertEquals(true, response.getAll().isSelected());
//        Assert.assertEquals("Activity", response.getItemSettings().get(0).getName());
//        Assert.assertEquals(true, response.getItemSettings().get(0).isSelected());
//        Assert.assertEquals("Book", response.getItemSettings().get(1).getName());
//        Assert.assertEquals(false, response.getItemSettings().get(1).isSelected());
//        Assert.assertEquals("Learning Media", response.getItemSettings().get(2).getName());
//        Assert.assertEquals(false, response.getItemSettings().get(2).isSelected());
//        Assert.assertEquals("Incident", response.getItemSettings().get(3).getName());
//        Assert.assertEquals(false, response.getItemSettings().get(3).isSelected());
//    }
//
//    /**
//     * 开关有总开关开，其他只有activity开
//     * 用户语言是空
//     *
//     * @throws Exception
//     */
//    @Test
//    public void getAdminPushSwitch_NoUserLang() throws Exception {
//        String userId = "u001";
//        UserModel userModel = new UserModel();
//        userModel.setId(userId);
//        userModel.setRole("AGENCY_OWNER");
//
//        AgencyModel agencyModel = new AgencyModel();
//        agencyModel.setId("a001");
//
//        List<AgencyModel> agencyModels = new ArrayList<>();
//        agencyModels.add(agencyModel);
//
//        AgencyMetaDataEntity metaData = new AgencyMetaDataEntity();
//        metaData.setAgencyId("a001");
//        metaData.setMetaValue("{\"ALLOW\":true,\"READING\":false,\"INCIDENT\":false,\"SONG\":false,\"ACTIVITY\":true}");
//        metaData.setMetaKey("PUSH_TYPE");
//        metaData.setId("m001");
//
//        Mockito.when(userDao.getUserLang("u001")).thenReturn("");
//        Mockito.when(userDao.getUserById("u001")).thenReturn(userModel);
//        Mockito.when(userDao.getAgencyByAgencyAdminId("u001")).thenReturn(agencyModels);
//        Mockito.when(agencyDao.getMeta("a001", "PUSH_TYPE")).thenReturn(metaData);
//
//        PushSwitchResponse response = pushService.getAdminPushSwitch(userId, null);
//
//        Assert.assertEquals(true, response.getAll().isSelected());
//        Assert.assertEquals("Activity", response.getItemSettings().get(0).getName());
//        Assert.assertEquals(true, response.getItemSettings().get(0).isSelected());
//        Assert.assertEquals("Book", response.getItemSettings().get(1).getName());
//        Assert.assertEquals(false, response.getItemSettings().get(1).isSelected());
//        Assert.assertEquals("Learning Media", response.getItemSettings().get(2).getName());
//        Assert.assertEquals(false, response.getItemSettings().get(2).isSelected());
//        Assert.assertEquals("Incident", response.getItemSettings().get(3).getName());
//        Assert.assertEquals(false, response.getItemSettings().get(3).isSelected());
//    }
//
//    /**
//     * 没有Agnecy
//     *
//     * @throws Exception
//     */
//    @Test(expected = LearningGenieRuntimeException.class)
//    public void getAdminPushSwitch_NoAgency() throws Exception {
//        String userId = "u001";
//        UserModel userModel = new UserModel();
//        userModel.setId(userId);
//        userModel.setRole("AGENCY_OWNER");
//
//        AgencyModel agencyModel = new AgencyModel();
//        agencyModel.setId("a001");
//
//        List<AgencyModel> agencyModels = new ArrayList<>();
//
//        AgencyMetaDataEntity metaData = new AgencyMetaDataEntity();
//        metaData.setAgencyId("a001");
//        metaData.setMetaValue("{\"ALLOW\":true,\"READING\":false,\"INCIDENT\":false,\"SONG\":false,\"ACTIVITY\":true}");
//        metaData.setMetaKey("PUSH_TYPE");
//        metaData.setId("m001");
//
//        Mockito.when(userDao.getUserLang("u001")).thenReturn("en-US");
//        Mockito.when(userDao.getUserById("u001")).thenReturn(userModel);
//        Mockito.when(userDao.getAgencyByAgencyAdminId("u001")).thenReturn(agencyModels);
//        Mockito.when(agencyDao.getMeta("a001", "PUSH_TYPE")).thenReturn(metaData);
//
//        PushSwitchResponse response = pushService.getAdminPushSwitch(userId, null);
//
//        Assert.assertEquals(true, response.getAll().isSelected());
//        Assert.assertEquals("Activity", response.getItemSettings().get(0).getName());
//        Assert.assertEquals(true, response.getItemSettings().get(0).isSelected());
//        Assert.assertEquals("Book", response.getItemSettings().get(1).getName());
//        Assert.assertEquals(false, response.getItemSettings().get(1).isSelected());
//        Assert.assertEquals("Learning Media", response.getItemSettings().get(2).getName());
//        Assert.assertEquals(false, response.getItemSettings().get(2).isSelected());
//        Assert.assertEquals("Incident", response.getItemSettings().get(3).getName());
//        Assert.assertEquals(false, response.getItemSettings().get(3).isSelected());
//    }
//
//    /**
//     * @throws Exception
//     */
//    @Test
//    public void getAdminPushSwitch_NoMetaData() throws Exception {
//        String userId = "u001";
//        UserModel userModel = new UserModel();
//        userModel.setId(userId);
//        userModel.setRole("AGENCY_OWNER");
//
//        AgencyModel agencyModel = new AgencyModel();
//        agencyModel.setId("a001");
//
//        List<AgencyModel> agencyModels = new ArrayList<>();
//        agencyModels.add(agencyModel);
//
//        AgencyMetaDataEntity metaData = new AgencyMetaDataEntity();
//        metaData.setAgencyId("a001");
//        metaData.setMetaValue("");
//        metaData.setMetaKey("PUSH_TYPE");
//        metaData.setId("m001");
//
//        Mockito.when(userDao.getUserLang("u001")).thenReturn("en-US");
//        Mockito.when(userDao.getUserById("u001")).thenReturn(userModel);
//        Mockito.when(userDao.getAgencyByAgencyAdminId("u001")).thenReturn(agencyModels);
//        Mockito.when(agencyDao.getMeta("a001", "PUSH_TYPE")).thenReturn(null);
//
//        PushSwitchResponse response = pushService.getAdminPushSwitch(userId, null);
//
//        Assert.assertEquals(true, response.getAll().isSelected());
//        Assert.assertEquals("Activity", response.getItemSettings().get(0).getName());
//        Assert.assertEquals(true, response.getItemSettings().get(0).isSelected());
//        Assert.assertEquals("Book", response.getItemSettings().get(1).getName());
//        Assert.assertEquals(true, response.getItemSettings().get(1).isSelected());
//        Assert.assertEquals("Learning Media", response.getItemSettings().get(2).getName());
//        Assert.assertEquals(true, response.getItemSettings().get(2).isSelected());
//        Assert.assertEquals("Incident", response.getItemSettings().get(3).getName());
//        Assert.assertEquals(true, response.getItemSettings().get(3).isSelected());
//    }
//
//    /**
//     * 设置agency的开关，开两个，activity，book
//     */
//    @Test
//    public void setAdminPushSwitch_OpenTwo() {
//        SetPushSwitchRequest request = new SetPushSwitchRequest();
//        request.setAll(false);
//        PushNotifyRequest pushNotifyRequest = new PushNotifyRequest();
//        pushNotifyRequest.setAgencyId("a001");
//        pushNotifyRequest.setCenterIds(null);
//        pushNotifyRequest.setGroupIds(null);
//        pushNotifyRequest.setVideoIds(Collections.singletonList("1"));
//        Long time = new Date().getTime() + 60000L;
//        pushNotifyRequest.setPushTime(TimeUtil.format(new Date(time), TimeUtil.format2));
//        pushNotifyRequest.setMediaCategory("Shawn Brown");
//
//        List<String> items = new ArrayList<>();
//        items.add("ACTIVITY");
//        items.add("READING");
//        request.setItemValues(items);
//
//        UserModel userModel = new UserModel();
//        userModel.setId("u001");
//        userModel.setRole("AGENCY_OWNER");
//
//        AgencyModel agencyModel = new AgencyModel();
//        agencyModel.setId("a001");
//
//        List<AgencyModel> agencyModels = new ArrayList<>();
//        agencyModels.add(agencyModel);
//
//        AgencyMetaDataEntity metaData = new AgencyMetaDataEntity();
//        metaData.setAgencyId("a001");
//        metaData.setMetaValue("{\"SONG\":false,\"INCIDENT\":false,\"READING\":true,\"ALLOW\":false,\"ACTIVITY\":true}");
//        metaData.setMetaKey("PUSH_TYPE");
//        metaData.setId("m001");
//
//        SwitchAndAgencyPushRequest switchAndAgencyPushRequest = new SwitchAndAgencyPushRequest();
//        switchAndAgencyPushRequest.setSetPushSwitchRequest(request);
//        switchAndAgencyPushRequest.setPushNotifyRequest(pushNotifyRequest);
//
//        Mockito.when(centerDao.getFirstCenterTimeZoneByAgencyId(agencyModel.getId())).thenReturn(new Date().toString());
//        Mockito.when(userDao.getAgencyByAgencyAdminId("u001")).thenReturn(agencyModels);
//        Mockito.when(userDao.getUserById("u001")).thenReturn(userModel);
//        Mockito.when(agencyDao.getMeta("a001", "PUSH_TYPE")).thenReturn(metaData);
//
//        PushSwitchResponse response = pushService.setAdminPushSwitch(request, "u001", null);
//
//        Mockito.verify(agencyDao, Mockito.times(1)).setMeta("a001", AgencyMetaKey.PUSH_TYPE.toString(), "{\"SONG\":false,\"INCIDENT\":false,\"READING\":true,\"ALLOW\":false,\"ACTIVITY\":true}");
//        Assert.assertEquals(false, response.getAll().isSelected());
//        Assert.assertEquals("Activity", response.getItemSettings().get(0).getName());
//        Assert.assertEquals(true, response.getItemSettings().get(0).isSelected());
//        Assert.assertEquals("Book", response.getItemSettings().get(1).getName());
//        Assert.assertEquals(true, response.getItemSettings().get(1).isSelected());
//        Assert.assertEquals("Learning Media", response.getItemSettings().get(2).getName());
//        Assert.assertEquals(false, response.getItemSettings().get(2).isSelected());
//        Assert.assertEquals("Incident", response.getItemSettings().get(3).getName());
//        Assert.assertEquals(false, response.getItemSettings().get(3).isSelected());
//    }
//
//    /**
//     * 设置agency的开关,全开
//     */
//    @Test
//    public void setAdminPushSwitch_OpenAll() {
//        SetPushSwitchRequest request = new SetPushSwitchRequest();
//        request.setAll(true);
//        PushNotifyRequest pushNotifyRequest = new PushNotifyRequest();
//        pushNotifyRequest.setAgencyId("a001");
//        pushNotifyRequest.setCenterIds(null);
//        pushNotifyRequest.setGroupIds(null);
//        pushNotifyRequest.setVideoIds(Collections.singletonList("1"));
//        Long time = new Date().getTime() + 60000L;
//        pushNotifyRequest.setPushTime(TimeUtil.format(new Date(time), TimeUtil.format2));
//        pushNotifyRequest.setMediaCategory("Shawn Brown");
//
//        List<String> items = new ArrayList<>();
//        items.add("ACTIVITY");
//        items.add("READING");
//        items.add("SONG");
//        items.add("INCIDENT");
//        request.setItemValues(items);
//
//        UserModel userModel = new UserModel();
//        userModel.setId("u001");
//        userModel.setRole("AGENCY_OWNER");
//
//        AgencyModel agencyModel = new AgencyModel();
//        agencyModel.setId("a001");
//
//        List<AgencyModel> agencyModels = new ArrayList<>();
//        agencyModels.add(agencyModel);
//
//        AgencyMetaDataEntity metaData = new AgencyMetaDataEntity();
//        metaData.setAgencyId("a001");
//        metaData.setMetaValue("{\"SONG\":true,\"INCIDENT\":true,\"READING\":true,\"ALLOW\":true,\"ACTIVITY\":true}");
//        metaData.setMetaKey("PUSH_TYPE");
//        metaData.setId("m001");
//
//        SwitchAndAgencyPushRequest switchAndAgencyPushRequest = new SwitchAndAgencyPushRequest();
//        switchAndAgencyPushRequest.setSetPushSwitchRequest(request);
//        switchAndAgencyPushRequest.setPushNotifyRequest(pushNotifyRequest);
//
//        Mockito.when(centerDao.getFirstCenterTimeZoneByAgencyId(Mockito.anyString())).thenReturn("Asia/Shanghai");
//        Mockito.when(userDao.getAgencyByAgencyAdminId("u001")).thenReturn(agencyModels);
//        Mockito.when(userDao.getUserById("u001")).thenReturn(userModel);
//        Mockito.when(agencyDao.getMeta("a001", "PUSH_TYPE")).thenReturn(metaData);
//
//        PushSwitchResponse response = pushService.setAdminPushSwitch(request, "u001", null);
//        Mockito.verify(agencyDao, Mockito.times(1)).setMeta("a001", AgencyMetaKey.PUSH_TYPE.toString(), "{\"SONG\":true,\"INCIDENT\":true,\"READING\":true,\"ALLOW\":true,\"ACTIVITY\":true}");
//        Assert.assertEquals(true, response.getAll().isSelected());
//        Assert.assertEquals("Activity", response.getItemSettings().get(0).getName());
//        Assert.assertEquals(true, response.getItemSettings().get(0).isSelected());
//        Assert.assertEquals("Book", response.getItemSettings().get(1).getName());
//        Assert.assertEquals(true, response.getItemSettings().get(1).isSelected());
//        Assert.assertEquals("Learning Media", response.getItemSettings().get(2).getName());
//        Assert.assertEquals(true, response.getItemSettings().get(2).isSelected());
//        Assert.assertEquals("Incident", response.getItemSettings().get(3).getName());
//        Assert.assertEquals(true, response.getItemSettings().get(3).isSelected());
//    }
//
//    /**
//     * 用户不是agencyOwner
//     */
//    @Test(expected = LearningGenieRuntimeException.class)
//    public void setAdminPushSwitch_NotAgencyOwner() {
//        SetPushSwitchRequest request = new SetPushSwitchRequest();
//        request.setAll(true);
//
//        List<String> items = new ArrayList<>();
//        items.add("ACTIVITY");
//        items.add("READING");
//        items.add("SONG");
//        items.add("INCIDENT");
//        request.setItemValues(items);
//
//        UserModel userModel = new UserModel();
//        userModel.setId("u001");
//        userModel.setRole("parent");
//
//        AgencyModel agencyModel = new AgencyModel();
//        agencyModel.setId("a001");
//
//        List<AgencyModel> agencyModels = new ArrayList<>();
//        agencyModels.add(agencyModel);
//
//        AgencyMetaDataEntity metaData = new AgencyMetaDataEntity();
//        metaData.setAgencyId("a001");
//        metaData.setMetaValue("{\"SONG\":true,\"INCIDENT\":true,\"READING\":true,\"ALLOW\":true,\"ACTIVITY\":true}");
//        metaData.setMetaKey("PUSH_TYPE");
//        metaData.setId("m001");
//
//        SwitchAndAgencyPushRequest switchAndAgencyPushRequest = new SwitchAndAgencyPushRequest();
//        switchAndAgencyPushRequest.setSetPushSwitchRequest(request);
//
//        Mockito.when(userDao.getAgencyByAgencyAdminId("u001")).thenReturn(agencyModels);
//        Mockito.when(userDao.getUserById("u001")).thenReturn(userModel);
//        Mockito.when(agencyDao.getMeta("a001", "PUSH_TYPE")).thenReturn(metaData);
//
//        PushSwitchResponse response = pushService.setAdminPushSwitch(request, "u001", null);
//
//        Mockito.verify(agencyDao, Mockito.times(1)).setMeta("a001", AgencyMetaKey.PUSH_TYPE.toString(), "{\"SONG\":true,\"INCIDENT\":true,\"READING\":true,\"ALLOW\":true,\"ACTIVITY\":true}");
//        Assert.assertEquals(true, response.getAll().isSelected());
//        Assert.assertEquals("Activity", response.getItemSettings().get(0).getName());
//        Assert.assertEquals(true, response.getItemSettings().get(0).isSelected());
//        Assert.assertEquals("Book", response.getItemSettings().get(1).getName());
//        Assert.assertEquals(true, response.getItemSettings().get(1).isSelected());
//        Assert.assertEquals("Learning Media", response.getItemSettings().get(2).getName());
//        Assert.assertEquals(true, response.getItemSettings().get(2).isSelected());
//        Assert.assertEquals("Incident", response.getItemSettings().get(3).getName());
//        Assert.assertEquals(true, response.getItemSettings().get(3).isSelected());
//    }
//
//    /**
//     * 没有agency
//     */
//    @Test(expected = LearningGenieRuntimeException.class)
//    public void setAdminPushSwitch_NotAgency() {
//        SetPushSwitchRequest request = new SetPushSwitchRequest();
//        request.setAll(true);
//
//        List<String> items = new ArrayList<>();
//        items.add("ACTIVITY");
//        items.add("READING");
//        items.add("SONG");
//        items.add("INCIDENT");
//        request.setItemValues(items);
//
//        UserModel userModel = new UserModel();
//        userModel.setId("u001");
//        userModel.setRole("parent");
//
//        AgencyModel agencyModel = new AgencyModel();
//        agencyModel.setId("a001");
//
//        List<AgencyModel> agencyModels = new ArrayList<>();
//
//        AgencyMetaDataEntity metaData = new AgencyMetaDataEntity();
//        metaData.setAgencyId("a001");
//        metaData.setMetaValue("{\"SONG\":true,\"INCIDENT\":true,\"READING\":true,\"ALLOW\":true,\"ACTIVITY\":true}");
//        metaData.setMetaKey("PUSH_TYPE");
//        metaData.setId("m001");
//
//        SwitchAndAgencyPushRequest switchAndAgencyPushRequest = new SwitchAndAgencyPushRequest();
//        switchAndAgencyPushRequest.setSetPushSwitchRequest(request);
//
//        Mockito.when(userDao.getAgencyByAgencyAdminId("u001")).thenReturn(agencyModels);
//        Mockito.when(userDao.getUserById("u001")).thenReturn(userModel);
//        Mockito.when(agencyDao.getMeta("a001", "PUSH_TYPE")).thenReturn(metaData);
//
//        PushSwitchResponse response = pushService.setAdminPushSwitch(request, "u001", null);
//
//        Mockito.verify(agencyDao, Mockito.times(1)).setMeta("a001", AgencyMetaKey.PUSH_TYPE.toString(), "{\"SONG\":true,\"INCIDENT\":true,\"READING\":true,\"ALLOW\":true,\"ACTIVITY\":true}");
//        Assert.assertEquals(true, response.getAll().isSelected());
//        Assert.assertEquals("Activity", response.getItemSettings().get(0).getName());
//        Assert.assertEquals(true, response.getItemSettings().get(0).isSelected());
//        Assert.assertEquals("Book", response.getItemSettings().get(1).getName());
//        Assert.assertEquals(true, response.getItemSettings().get(1).isSelected());
//        Assert.assertEquals("Learning Media", response.getItemSettings().get(2).getName());
//        Assert.assertEquals(true, response.getItemSettings().get(2).isSelected());
//        Assert.assertEquals("Incident", response.getItemSettings().get(3).getName());
//        Assert.assertEquals(true, response.getItemSettings().get(3).isSelected());
//    }
//
//    /**
//     * 用户不是家长
//     */
//    @Test(expected = LearningGenieRuntimeException.class)
//    public void getParentPushSwitch() {
//        UserModel userModel = new UserModel();
//        userModel.setId("u001");
//        userModel.setRole("AGENCY_OWNER");
//
//        Mockito.when(userDao.getUserById("u001")).thenReturn(userModel);
//
//        pushService.getParentPushSwitch("u001", null);
//    }
//
//    /**
//     * 有多个agency
//     *
//     * @throws Exception
//     */
//    @Test
//    public void getParentPushSwitch_MultiAgencies() throws Exception {
//        String userId = "p001";
//        UserModel userModel = new UserModel();
//        userModel.setId(userId);
//        userModel.setRole("parent");
//
//        AgencyEntity agencyModel = new AgencyEntity();
//        agencyModel.setId("a001");
//        AgencyEntity agencyModel2 = new AgencyEntity();
//        agencyModel2.setId("a002");
//
//        List<AgencyEntity> agencyModels = new ArrayList<>();
//        agencyModels.add(agencyModel);
//        agencyModels.add(agencyModel2);
//
//        AgencyMetaDataEntity metaData = new AgencyMetaDataEntity();
//        metaData.setAgencyId("a001");
//        metaData.setMetaValue("{\"ALLOW\":true,\"READING\":false,\"INCIDENT\":false,\"SONG\":false,\"ACTIVITY\":false}");
//        metaData.setMetaKey("PUSH_TYPE");
//        metaData.setId("m001");
//
//        AgencyMetaDataEntity metaData2 = new AgencyMetaDataEntity();
//        metaData2.setAgencyId("a001");
//        metaData2.setMetaValue("{\"ALLOW\":true,\"READING\":true,\"INCIDENT\":true,\"SONG\":true,\"ACTIVITY\":false}");
//        metaData2.setMetaKey("PUSH_TYPE");
//        metaData2.setId("m001");
//
//        com.learninggenie.common.data.entity.UserEntity parent = new com.learninggenie.common.data.entity.UserEntity();
//        parent.setId("p001");
//        parent.setRole("parent");
//
//        UserMetaDataEntity metaData3 = new UserMetaDataEntity();
//        metaData3.setUser(parent);
//        metaData3.setMetaValue("{\"ALLOW\":true,\"READING\":true,\"INCIDENT\":true,\"SONG\":true}");
//        metaData3.setMetaKey("PUSH_TYPE");
//        metaData3.setId("m003");
//
//        Mockito.when(userDao.getUserLang("p001")).thenReturn("en-US");
//        Mockito.when(userDao.getUserById("p001")).thenReturn(userModel);
//        Mockito.when(agencyDao.getAgencyByParentId("p001")).thenReturn(agencyModels);
//        Mockito.when(agencyDao.getMeta("a001", "PUSH_TYPE")).thenReturn(metaData);
//        Mockito.when(agencyDao.getMeta("a002", "PUSH_TYPE")).thenReturn(metaData2);
//        Mockito.when(userDao.getMetaData("p001", "PUSH_TYPE")).thenReturn(metaData3);
//
//        PushSwitchResponse response = pushService.getParentPushSwitch(userId, null);
//
//        Assert.assertEquals(true, response.getAll().isSelected());
//        Assert.assertEquals("Book", response.getItemSettings().get(0).getName());
//        Assert.assertEquals(true, response.getItemSettings().get(0).isSelected());
//        Assert.assertEquals("Learning Media", response.getItemSettings().get(1).getName());
//        Assert.assertEquals(true, response.getItemSettings().get(1).isSelected());
//        Assert.assertEquals("Incident", response.getItemSettings().get(2).getName());
//        Assert.assertEquals(true, response.getItemSettings().get(2).isSelected());
//    }
//
//    /**
//     * 设置agency的开关，开两个，activity，book
//     */
//    @Test
//    public void setParentPushSwitch_Open() {
//        SetPushSwitchRequest request = new SetPushSwitchRequest();
//        request.setAll(true);
//
//        List<String> items = new ArrayList<>();
//        items.add("ACTIVITY");
//        items.add("READING");
//        request.setItemValues(items);
//
//        String userId = "p001";
//        UserModel userModel = new UserModel();
//        userModel.setId(userId);
//        userModel.setRole("parent");
//
//        AgencyEntity agencyModel = new AgencyEntity();
//        agencyModel.setId("a001");
//        AgencyEntity agencyModel2 = new AgencyEntity();
//        agencyModel2.setId("a002");
//
//        List<AgencyEntity> agencyModels = new ArrayList<>();
//        agencyModels.add(agencyModel);
//
//        AgencyMetaDataEntity metaData = new AgencyMetaDataEntity();
//        metaData.setAgencyId("a001");
//        metaData.setMetaValue("{\"ALLOW\":false,\"READING\":true,\"INCIDENT\":true,\"SONG\":false,\"ACTIVITY\":false}");
//        metaData.setMetaKey("PUSH_TYPE");
//        metaData.setId("m001");
//
//        com.learninggenie.common.data.entity.UserEntity parent = new com.learninggenie.common.data.entity.UserEntity();
//        parent.setId("p001");
//        parent.setRole("parent");
//
//        UserMetaDataEntity metaData3 = new UserMetaDataEntity();
//        metaData3.setUser(parent);
//        metaData3.setMetaValue("{\"ALLOW\":true,\"READING\":true,\"INCIDENT\":true,\"SONG\":true}");
//        metaData3.setMetaKey("PUSH_TYPE");
//        metaData3.setId("m003");
//
//        Mockito.when(userDao.getUserLang("p001")).thenReturn("en-US");
//        Mockito.when(userDao.getUserById("p001")).thenReturn(userModel);
//        Mockito.when(agencyDao.getAgencyByParentId("p001")).thenReturn(agencyModels);
//        Mockito.when(agencyDao.getMeta("a001", "PUSH_TYPE")).thenReturn(metaData);
//        Mockito.when(userDao.getMetaData("p001", "PUSH_TYPE")).thenReturn(metaData3);
//
//        PushSwitchResponse response = pushService.setParentPushSwitch(request, userId, null);
//
//        Mockito.verify(userDao, Mockito.times(1)).setMetaData("p001", AgencyMetaKey.PUSH_TYPE.toString(), "{\"READING\":true,\"ALLOW\":true,\"ACTIVITY\":true}");
//        Assert.assertEquals(true, response.getAll().isSelected());
//        Assert.assertEquals("Book", response.getItemSettings().get(0).getName());
//        Assert.assertEquals(true, response.getItemSettings().get(0).isSelected());
//        Assert.assertEquals("Incident", response.getItemSettings().get(1).getName());
//        Assert.assertEquals(true, response.getItemSettings().get(1).isSelected());
//    }
//
//    /**
//     * 验证返回的上次推送成功时间是否跟预设的一致
//     */
//    @Test
//    public void getLastPushTime_ExpectedTime() {
//        MediaResourceEntity mediaResource = new MediaResourceEntity();
//        mediaResource.setPlaylistId("m001");
//        mediaResource.setCategory("Shawn Brown");
//
//        UserModel userModel = new UserModel();
//        userModel.setId("u001");
//        userModel.setRole("AGENCY_OWNER");
//
//        List<MediaResourceEntity> resources = new ArrayList<>();
//        resources.add(mediaResource);
//
//        Date date = TimeUtil.parse("11/01/2017 11:11", TimeUtil.format13);
//
//        AgencyModel agencyModel = new AgencyModel();
//        agencyModel.setId("a001");
//
//        List<AgencyModel> agencyModels = new ArrayList<>();
//        agencyModels.add(agencyModel);
//
//        PushNotificationModel push = new PushNotificationModel();
//        push.setId("p001");
//        push.setPushSuccessTime(date);
//
//        ResultPojo resultPojo = new ResultPojo();
//        resultPojo.setStatus(200);
//        resultPojo.setData("[{\"extEmbedCode\":\"Like?YesNo Comments:Submit\",\"duration\":\"PT4M41S\",\"contentDetails\":null,\"etag\":\"\\\"ld9biNPKjAjgjV7EZ4EKeEGrhao/jLeolpSpe26ZTcfY4-t75y6ccQU\\\"\",\"id\":\"tkmUgR6Geyg\",\"kind\":\"youtube#video\",\"snippet\":{\"channelId\":null,\"channelTitle\":null,\"description\":null,\"playlistId\":null,\"position\":null,\"publishedAt\":\"2015-02-15T16:56:22.000+00:00\",\"resourceId\":{\"channelId\":null,\"kind\":null,\"playlistId\":null,\"videoId\":\"tkmUgR6Geyg\",\"ETag\":null},\"thumbnails\":{\"default\":{\"height\":90,\"url\":\"https://i.ytimg.com/vi/tkmUgR6Geyg/default.jpg\",\"width\":120,\"ETag\":null},\"high\":{\"height\":360,\"url\":\"https://i.ytimg.com/vi/tkmUgR6Geyg/hqdefault.jpg\",\"width\":480,\"ETag\":null},\"maxres\":null,\"medium\":{\"height\":180,\"url\":\"https://i.ytimg.com/vi/tkmUgR6Geyg/mqdefault.jpg\",\"width\":320,\"ETag\":null},\"standard\":{\"height\":480,\"url\":\"https://i.ytimg.com/vi/tkmUgR6Geyg/sddefault.jpg\",\"width\":640,\"ETag\":null},\"ETag\":null},\"title\":\"Super Fun Show - by Shawn Brown\",\"ETag\":null},\"status\":null}]");
//
//        PowerMockito.when(mediaBookDao.getMediaResourceByCategory("Shawn Brown")).thenReturn(resources);
//        PowerMockito.when(pushNotificationDao.getAgencyOfTheVideoLastPush("a001", "tkmUgR6Geyg", "Shawn Brown")).thenReturn(push);
//        PowerMockito.when(userDao.getUserById("u001")).thenReturn(userModel);
//        PowerMockito.when(userDao.getAgencyByAgencyAdminId("u001")).thenReturn(agencyModels);
//        PowerMockito.mockStatic(RestApiUtil.class);
//        PowerMockito.when(RestApiUtil.get("null/api/medias/playlistItems?playlistIds=m001", null, false)).thenReturn(resultPojo);
//
//        LastTimeVideoResponse responses = pushService.getAgencyLastPushTime("u001", "Shawn Brown");
//
//        Mockito.verify(pushNotificationDao, Mockito.times(1)).getAgencyOfTheVideoLastPush("a001", "tkmUgR6Geyg", "Shawn Brown");
////        Assert.assertEquals("11/01/2017 11:11", responses.getVideoResponses().get(0).getLastPushTime());
//    }
//
//    /**
//     * 正常推送一条消息
//     * 时区洛杉矶
//     * 家长开通聊天功能
//     * 未给此家长发送过
//     * 推送类型为Report
//     * 老师或者管理员添加的report
//     *
//     * @throws Exception
//     */
//    @Test
//    public void pushNotificationServices_America_report() throws Exception {
//
//        String timeZone = "America/Los_Angeles";
//
//        Date utcNow = TimeUtil.getUtcNow();
//        Date localNow = TimeUtil.convertUtcToLocal(utcNow, timeZone);
//
//        AgencyModel agencyModel = new AgencyModel();
//        agencyModel.setId("a001");
//        agencyModel.setName("toto");
//
//        List<AgencyModel> agencyModels = new ArrayList<>();
//        agencyModels.add(agencyModel);
//
//        PushNotificationModel pushNotificationModel = new PushNotificationModel();
//        pushNotificationModel.setId("p001");
//        pushNotificationModel.setPushId("p001");
//        pushNotificationModel.setPushTime(localNow);
//        pushNotificationModel.setAgencyId("a001");
//        pushNotificationModel.setMediaId("v001");
//        pushNotificationModel.setMediaCategory("Shawn Brown");
//
//        List<PushNotificationModel> pushes = new ArrayList<>();
//
//        GoogleVideoHyphenate googleVideo = new GoogleVideoHyphenate();
//        googleVideo.setSongInfo("songInfo");
//        googleVideo.setTitle("hi");
//        googleVideo.setImgUrl("imgUrl");
//
//        UserModel user = new UserModel();
//        user.setRole("AGENCY_OWNER");
//        user.setId("u001");
//        user.setDisplayName("agencyOwner");
//
//        String parentId = "p001";
//        List<String> parentsId = new ArrayList<>();
//
//        EnrollmentEntity enrollment = new EnrollmentEntity();
//        enrollment.setId("e001");
//        enrollment.setDisplayName("enrollment");
//
//        List<EnrollmentEntity> enrollmentEntityList = new ArrayList<>();
//        enrollmentEntityList.add(enrollment);
//
//        UserEntity parent = new UserEntity();
//        parent.setId("parent001");
//        parent.setName("parent");
//
//        List<UserEntity> parents = new ArrayList<>();
//        parents.add(parent);
//
//        ResultPojo resultPojo = new ResultPojo();
//        resultPojo.setStatus(200);
//
//        UserModel parentModel = new UserModel();
//        parentModel.setId("parent002");
//        parentModel.setRole("parent");
//
//        AgencyEntity agencyEntity = new AgencyEntity();
//        agencyEntity.setId("a001");
//        agencyEntity.setName("toto");
//
//        List<AgencyEntity> agencyEntities = new ArrayList<>();
//        agencyEntities.add(agencyEntity);
//
//        ReportPush reportPush = new ReportPush();
//        reportPush.setId("r001");
//        reportPush.setNoteId("n001");
//        reportPush.setRelativePath("r001");
//        reportPush.setTarget("[s001]");
//        reportPush.setNoteType("Activity");
//        reportPush.setMessageType("Report");
//
//        List<ReportPush> reportPushes = new ArrayList<>();
//        reportPushes.add(reportPush);
//
//        EnrollmentModel enrollmentModel = new EnrollmentModel();
//        enrollmentModel.setId("e002");
//        enrollmentModel.setDisplayName("enrollment2");
//
//        List<EnrollmentModel> enrollmentModels = new ArrayList<>();
//        enrollmentModels.add(enrollmentModel);
//
//        com.learninggenie.common.data.entity.CenterEntity centerEntity = new CenterEntity();
//        centerEntity.setCenterTimeZone(timeZone);
//
//        UserEntity parentEntity = new UserEntity();
//        parentEntity.setId("parent002");
//        parentEntity.setName("parent");
//
//        List<UserEntity> parentEntities = new ArrayList<>();
//        parentEntities.add(parentEntity);
//
//        Mockito.when(agencyDao.getAgencyByParentId("parent002")).thenReturn(agencyEntities);
//        Mockito.when(userDao.getUserById("parent001")).thenReturn(parentModel);
//        PowerMockito.when(agencyDao.getPushNotifyAgency("IS_PUSH", "true")).thenReturn(agencyModels);
//        Mockito.when(centerDao.getFirstCenterTimeZoneByAgencyId("a001")).thenReturn(timeZone);
//        Mockito.when(pushNotificationDao.getTodayAgencyPushByTime(Mockito.anyString(), Mockito.anyString())).thenReturn(pushes);
//        Mockito.when(commService.getGoogleVideos("v001", "Shawn Brown", null)).thenReturn(googleVideo);
//        Mockito.when(pushNotificationDao.getParentsPushRecord("txt", "p001")).thenReturn(parentsId);
//        Mockito.when(studentDao.getEnrollmentByAgencyId("a001")).thenReturn(enrollmentEntityList);
//        Mockito.when(userDao.getParentsByStudentId("e001")).thenReturn(parents);
//        Mockito.when(commService.getUser(Mockito.anyString())).thenReturn(resultPojo);
//        Mockito.when(commService.sendMessages(Mockito.anyObject(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(resultPojo);
//        Mockito.when(pushNotificationDao.getReportPush()).thenReturn(reportPushes);
//        Mockito.when(fileSystem.getPublicUrl(Mockito.anyString())).thenReturn("u002.jpg");
//        Mockito.when(studentDao.getChildrenById(Mockito.anyString())).thenReturn(enrollmentModels);
//        Mockito.when(centerDao.getByChildId("e002")).thenReturn(centerEntity);
//        Mockito.when(userDao.getParentsByStudentId("e002")).thenReturn(parentEntities);
//        Mockito.when(userDao.getUserById("parent002")).thenReturn(parentModel);
//
//        pushService.pushNotificationServices();
//
//        Mockito.verify(userDao, Mockito.times(1)).getParentsByStudentId(Mockito.eq("e002"));
//        Mockito.verify(commService, Mockito.times(1)).getUser(Mockito.eq("PARENT002LG"));
//        Mockito.verify(commService, Mockito.times(1)).sendMessages(Mockito.anyObject(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString());
//    }
//
//    /**
//     * 正常推送一条消息
//     * 时区洛杉矶
//     * 家长开通聊天功能
//     * 未给此家长发送过
//     * Center类型
//     *
//     * @throws Exception
//     */
//    @Test
//    public void pushNotificationServices_Center() throws Exception {
//
//        String timeZone = "America/Los_Angeles";
//
//        Date utcNow = TimeUtil.getUtcNow();
//        Date localNow = TimeUtil.convertUtcToLocal(utcNow, timeZone);
//
//        AgencyModel agencyModel = new AgencyModel();
//        agencyModel.setId("a001");
//        agencyModel.setName("toto");
//
//        List<AgencyModel> agencyModels = new ArrayList<>();
//        agencyModels.add(agencyModel);
//
//        PushNotificationModel pushNotificationModel = new PushNotificationModel();
//        pushNotificationModel.setId("p001");
//        pushNotificationModel.setPushId("p001");
//        pushNotificationModel.setPushTime(localNow);
//        pushNotificationModel.setAgencyId(null);
//        pushNotificationModel.setMediaId("v001");
//        pushNotificationModel.setMediaCategory("Shawn Brown");
//        pushNotificationModel.setCenterId("c001");
//        pushNotificationModel.setGroupId("g001");
//
//        List<PushNotificationModel> pushes = new ArrayList<>();
//        pushes.add(pushNotificationModel);
//
//        GoogleVideoHyphenate googleVideo = new GoogleVideoHyphenate();
//        googleVideo.setSongInfo("songInfo");
//        googleVideo.setTitle("hi");
//        googleVideo.setImgUrl("imgUrl");
//
//        UserModel user = new UserModel();
//        user.setRole("AGENCY_OWNER");
//        user.setId("u001");
//        user.setDisplayName("agencyOwner");
//
//        String parentId = "p001";
//        List<String> parentsId = new ArrayList<>();
//
//        EnrollmentModel enrollment = new EnrollmentModel();
//        enrollment.setId("e001");
//        enrollment.setDisplayName("enrollment");
//
//        List<EnrollmentModel> enrollmentEntityList = new ArrayList<>();
//        enrollmentEntityList.add(enrollment);
//
//        UserEntity parent = new UserEntity();
//        parent.setId("parent001");
//        parent.setName("parent");
//
//        List<UserEntity> parents = new ArrayList<>();
//        parents.add(parent);
//
//        ResultPojo resultPojo = new ResultPojo();
//        resultPojo.setStatus(200);
//
//        UserModel parentModel = new UserModel();
//        parentModel.setId("parent001");
//        parentModel.setRole("parent");
//
//        AgencyEntity agencyEntity = new AgencyEntity();
//        agencyEntity.setId("a001");
//        agencyEntity.setName("toto");
//
//        List<AgencyEntity> agencyEntities = new ArrayList<>();
//        agencyEntities.add(agencyEntity);
//
//        List<String> centerIds = new ArrayList<>();
//        centerIds.add("c001");
//        centerIds.add("c002");
//
//        CenterEntity center = new CenterEntity();
//        center.setId("c001");
//
//        List<EnrollmentModel> enrollmentModels = new ArrayList<>();
//
//        EnrollmentModel enrollmentModel = new EnrollmentModel();
//        enrollmentModel.setId("e001");
//        enrollmentModel.setDisplayName("enrollment");
//        enrollmentModels.add(enrollmentModel);
//
//        List<String> ids = new ArrayList<>();
//        ids.add("g001");
//
//        List<UserModel> agencyUsers = new ArrayList<>();
//        agencyUsers.add(user);
//
//        Mockito.when(userDao.getParentsByStudentId(Mockito.anyString())).thenReturn(parents);
//        Mockito.when(userDao.getAgencyOwnerByAgencyId(Mockito.anyString())).thenReturn(agencyUsers);
//        Mockito.when(studentDao.getChildrenByGroupIds(ids, null)).thenReturn(enrollmentModels);
//        Mockito.when(studentDao.getChildrenByCenterIds(ids, null)).thenReturn(enrollmentModels);
//        Mockito.when(centerDao.getCenter(Mockito.anyString())).thenReturn(center);
//        Mockito.when(agencyDao.getAgencyByParentId("parent001")).thenReturn(agencyEntities);
//        Mockito.when(userDao.getUserById("parent001")).thenReturn(parentModel);
//        PowerMockito.when(agencyDao.getPushNotifyAgency("IS_PUSH", "true")).thenReturn(agencyModels);
//        Mockito.when(centerDao.getFirstCenterTimeZoneByAgencyId("a001")).thenReturn(timeZone);
//        Mockito.when(pushNotificationDao.getTodayAgencyPushByTime(Mockito.anyString(), Mockito.anyString())).thenReturn(pushes);
//        Mockito.when(commService.getGoogleVideos("v001", "Shawn Brown", null)).thenReturn(googleVideo);
//        Mockito.when(pushNotificationDao.getParentsPushRecord("txt", "p001")).thenReturn(parentsId);
//        Mockito.when(studentDao.getChildrenByCenterId("c001", null)).thenReturn(enrollmentEntityList);
//        Mockito.when(userDao.getParentsByStudentId(Mockito.anyString())).thenReturn(parents);
//        Mockito.when(commService.getUser("PARENT001LG")).thenReturn(resultPojo);
//        Mockito.when(commService.sendMessages(Mockito.anyObject(), Mockito.eq(null), Mockito.eq("America/Los_Angeles"), Mockito.eq("v001"), Mockito.eq("p001"))).thenReturn(resultPojo);
//        pushService.pushNotificationServices();
//
//        Mockito.verify(userDao, Mockito.times(1)).getParentsByStudentId("e001");
//        Mockito.verify(commService, Mockito.times(1)).getUser("PARENT001LG");
//        Mockito.verify(commService, Mockito.times(1)).sendMessages(Mockito.anyObject(), Mockito.eq(null), Mockito.eq("America/Los_Angeles"), Mockito.eq("v001"), Mockito.eq("p001"));
//        Mockito.verify(commService, Mockito.times(1)).sendMessages(Mockito.anyObject(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString());
//        Mockito.verify(pushNotificationDao, Mockito.times(1)).setSendStatus(Mockito.eq("p001"), Mockito.eq(true), Mockito.anyObject());
//    }
//
//    /**
//     * 正常推送一条消息
//     * 时区洛杉矶
//     * 家长开通聊天功能
//     * 未给此家长发送过
//     * Group类型
//     *
//     * @throws Exception
//     */
//    @Test
//    public void pushNotificationServices_Group() throws Exception {
//
//        String timeZone = "America/Los_Angeles";
//
//        Date utcNow = TimeUtil.getUtcNow();
//        Date localNow = TimeUtil.convertUtcToLocal(utcNow, timeZone);
//
//        AgencyModel agencyModel = new AgencyModel();
//        agencyModel.setId("a001");
//        agencyModel.setName("toto");
//
//        List<AgencyModel> agencyModels = new ArrayList<>();
//        agencyModels.add(agencyModel);
//
//        PushNotificationModel pushNotificationModel = new PushNotificationModel();
//        pushNotificationModel.setId("p001");
//        pushNotificationModel.setPushId("p001");
//        pushNotificationModel.setPushTime(localNow);
//        pushNotificationModel.setAgencyId(null);
//        pushNotificationModel.setMediaId("v001");
//        pushNotificationModel.setMediaCategory("Shawn Brown");
//        pushNotificationModel.setGroupId("g001");
//
//        List<PushNotificationModel> pushes = new ArrayList<>();
//        pushes.add(pushNotificationModel);
//
//        GoogleVideoHyphenate googleVideo = new GoogleVideoHyphenate();
//        googleVideo.setSongInfo("songInfo");
//        googleVideo.setTitle("hi");
//        googleVideo.setImgUrl("imgUrl");
//
//        UserModel user = new UserModel();
//        user.setRole("AGENCY_OWNER");
//        user.setId("u001");
//        user.setDisplayName("agencyOwner");
//
//        String parentId = "p001";
//        List<String> parentsId = new ArrayList<>();
//
//        EnrollmentModel enrollment = new EnrollmentModel();
//        enrollment.setId("e001");
//        enrollment.setDisplayName("enrollment");
//
//        List<EnrollmentModel> enrollmentEntityList = new ArrayList<>();
//        enrollmentEntityList.add(enrollment);
//
//        UserEntity parent = new UserEntity();
//        parent.setId("parent001");
//        parent.setName("parent");
//
//        List<UserEntity> parents = new ArrayList<>();
//        parents.add(parent);
//
//        ResultPojo resultPojo = new ResultPojo();
//        resultPojo.setStatus(200);
//
//        UserModel parentModel = new UserModel();
//        parentModel.setId("parent001");
//        parentModel.setRole("parent");
//
//        AgencyEntity agencyEntity = new AgencyEntity();
//        agencyEntity.setId("a001");
//        agencyEntity.setName("toto");
//
//        List<AgencyEntity> agencyEntities = new ArrayList<>();
//        agencyEntities.add(agencyEntity);
//
//        List<String> groupIds = new ArrayList<>();
//        groupIds.add("g001");
//        groupIds.add("g002");
//
//        GroupEntry group = new GroupEntry();
//        group.setId("g001");
//
//        List<EnrollmentModel> enrollmentModels = new ArrayList<>();
//
//        EnrollmentModel enrollmentModel = new EnrollmentModel();
//        enrollmentModel.setId("e001");
//        enrollmentModel.setDisplayName("enrollment");
//        enrollmentModels.add(enrollmentModel);
//
//        List<String> ids = new ArrayList<>();
//        ids.add("g001");
//
//        List<UserModel> agencyUsers = new ArrayList<>();
//        agencyUsers.add(user);
//
//        Mockito.when(userDao.getParentsByStudentId(Mockito.anyString())).thenReturn(parents);
//        Mockito.when(userDao.getAgencyOwnerByAgencyId(Mockito.anyString())).thenReturn(agencyUsers);
//        Mockito.when(studentDao.getChildrenByGroupIds(ids, null)).thenReturn(enrollmentModels);
//        Mockito.when(studentDao.getChildrenByCenterIds(ids, null)).thenReturn(enrollmentModels);
//        Mockito.when(groupDao.getGroup(Mockito.anyString())).thenReturn(group);
//        Mockito.when(agencyDao.getAgencyByParentId("parent001")).thenReturn(agencyEntities);
//        Mockito.when(userDao.getUserById("parent001")).thenReturn(parentModel);
//        PowerMockito.when(agencyDao.getPushNotifyAgency("IS_PUSH", "true")).thenReturn(agencyModels);
//        Mockito.when(centerDao.getFirstCenterTimeZoneByAgencyId("a001")).thenReturn(timeZone);
//        Mockito.when(pushNotificationDao.getTodayAgencyPushByTime(Mockito.anyString(), Mockito.anyString())).thenReturn(pushes);
//        Mockito.when(commService.getGoogleVideos("v001", "Shawn Brown", null)).thenReturn(googleVideo);
//        Mockito.when(pushNotificationDao.getParentsPushRecord("txt", "p001")).thenReturn(parentsId);
//        Mockito.when(studentDao.getChildrenByGroupId("g001", null)).thenReturn(enrollmentEntityList);
//        Mockito.when(userDao.getParentsByStudentId("e001")).thenReturn(parents);
//        Mockito.when(commService.getUser("PARENT001LG")).thenReturn(resultPojo);
//        Mockito.when(commService.sendMessages(Mockito.anyObject(), Mockito.eq(null), Mockito.eq("America/Los_Angeles"), Mockito.eq("v001"), Mockito.eq("p001"))).thenReturn(resultPojo);
//        pushService.pushNotificationServices();
//
//        Mockito.verify(userDao, Mockito.times(1)).getParentsByStudentId("e001");
//        Mockito.verify(commService, Mockito.times(1)).getUser("PARENT001LG");
//        Mockito.verify(commService, Mockito.times(1)).sendMessages(Mockito.anyObject(), Mockito.eq(null), Mockito.eq("America/Los_Angeles"), Mockito.eq("v001"), Mockito.eq("p001"));
//        Mockito.verify(commService, Mockito.times(1)).sendMessages(Mockito.anyObject(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString());
//        Mockito.verify(pushNotificationDao, Mockito.times(1)).setSendStatus(Mockito.eq("p001"), Mockito.eq(true), Mockito.anyObject());
//    }
//
//    /**
//     *成功删除一个任务
//     */
//    @Test
//    public void deletePushTask_deleteSuccess() {
//        UserModel userModel = new UserModel();
//        userModel.setId("u001");
//        userModel.setDisplayName("Adolph");
//        userModel.setRole("AGENCY_OWNER");
//
//        Mockito.when(userDao.getUserById("u001")).thenReturn(userModel);
//
//        pushService.deletePushTask("u001", "t001", false);
//
//        Mockito.verify(pushNotificationDao, Mockito.times(1)).deleteTask("t001");
//        Mockito.verify(pushNotificationDao, Mockito.times(1)).deleteTask("t001");
//    }
//
//    /**
//     * 删除推送任务，user为空
//     * 删除失败
//     */
//    @Test(expected = LearningGenieRuntimeException.class)
//    public void deletePushTask_UserIsNull() {
//        Mockito.when(userDao.getUserById("u001")).thenReturn(null);
//
//        pushService.deletePushTask("u001", "t001", false);
//
//        Mockito.verify(pushNotificationDao, Mockito.times(0)).deleteTask("t001");
//        Mockito.verify(pushNotificationDao, Mockito.times(0)).deleteTask("t001");
//    }
//
//    /**
//     * 删除一个任务,
//     * user不是agencyOwner
//     */
//    @Test(expected = LearningGenieRuntimeException.class)
//    public void deletePushTask_UserISNotAgencyOwner() {
//        UserModel userModel = new UserModel();
//        userModel.setId("u001");
//        userModel.setDisplayName("Adolph");
//
//        Mockito.when(userDao.getUserById("u001")).thenReturn(userModel);
//
//        pushService.deletePushTask("u001", "t001", false);
//
//        Mockito.verify(pushNotificationDao, Mockito.times(0)).deleteTask("t001");
//        Mockito.verify(pushNotificationDao, Mockito.times(0)).deleteTask("t001");
//    }
//}