package com.learninggenie.common.doc.service;

import com.deepoove.poi.data.*;
import com.learninggenie.common.doc.policy.MeasureDetailTablePolicy;
import com.learninggenie.common.doc.renderdata.MeasureMappingDetail;
import org.apache.poi.xwpf.usermodel.XWPFTable;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.*;

import static org.mockito.Mockito.*;

/**
 * 度量详细信息表策略测试
 *
 * <AUTHOR>
 * @date 2024/05/29
 */
public class MeasureDetailTablePolicyTest {

    @InjectMocks
    private MeasureDetailTablePolicy measureDetailTablePolicy;

    @Mock
    private XWPFTable table;

    @Mock
    private MeasureMappingDetail detailData;

    @Mock
    private RowRenderData rowRenderData;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    void testRender_NullData() throws Exception {
        // 准备数据
        when(detailData.getMeasures()).thenReturn(null);

        // 执行方法
        measureDetailTablePolicy.render(table, null);

        // 验证相关方法是否被调用
        verify(table, never()).removeRow(anyInt());
        // 验证其他相关逻辑
    }

    @Test
    void testRender_NonNullMeasures() throws Exception {
        // 准备数据
        List<RowRenderData> measures = Collections.singletonList(rowRenderData);
        CellRenderData cellRenderData = new CellRenderData();
        ArrayList<ParagraphRenderData> paragraphRenderData = new ArrayList<>();
        ParagraphRenderData renderData = new ParagraphRenderData();
        ArrayList<RenderData> contents = new ArrayList<>();
        contents.add(Rows.of(Cells.of(new TextRenderData("ATL-REG"))
                                .center().create(),
                        Cells.of(new TextRenderData("ATL-REG1"))
                                .center().create(),
                        Cells.of(new TextRenderData("Approaches to Learning - Self Regulation"))
                                .center().create())
                .center().create() );
        renderData.setContents(contents);
        paragraphRenderData.add(renderData);
        cellRenderData.setParagraphs(paragraphRenderData);
        List<CellRenderData> cells = Collections.singletonList(cellRenderData);
        rowRenderData.setCells(cells);
        // 模拟行为
        when(detailData.getMeasures()).thenReturn(measures);

        // 执行方法
        measureDetailTablePolicy.render(table, detailData);

        // 验证相关方法是否被调用
         verify(table, times(1)).removeRow(anyInt());
    }
}
