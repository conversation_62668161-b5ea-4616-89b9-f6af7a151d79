<%
    Object login = session.getAttribute("login");
    if (login == null) {
        response.sendRedirect("login.jsp");
    }
%>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <link href="/admin/css/bootstrap.min.css" rel="stylesheet"/>
</head>
<script src="/admin/js/jquery.min.js"></script>
<script src="/admin/js/bootstrap.min.js"></script>
<body>
<div class="container" style="padding-top:10px;">
    Welcome!<%= session.getAttribute("login")%>
    <form method="POST" action="logout.jsp" style="margin-bottom: 20px;">
        <button type="submit">Logout</button>
    </form>
    <hr>
    <div>
        <!-- Nav tabs -->
        <ul class="nav nav-tabs" role="tablist">
            <li role="presentation" class="active"><a href="#page1" aria-controls="page1" role="tab" data-toggle="tab">Send to someone by email</a></li>
            <li role="presentation"><a href="#page2" aria-controls="page2" role="tab" data-toggle="tab">Broadcasting</a></li>
            <li role="presentation"><a href="#page3" aria-controls="page3" role="tab" data-toggle="tab">Send to a group of people</a></li>
            <li role="presentation"><a href="#page4" aria-controls="page4" role="tab" data-toggle="tab">Send to someone by uid</a></li>
        </ul>

        <!-- Tab panes -->
        <div class="tab-content">
            <div class="tab-pane active" id="page1">
                <form>
                    <div class="form-group">
                        <label>Email address</label>
                        <input type="email" class="form-control" id="email1" placeholder="Enter email">
                    </div>
                    <div class="form-group">
                        <label>NotificationType</label>
                        <select class="form-control" id="notificationType1">
                            <option value="USER">User</option>
                            <option value="SYSTEM">System</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>NotificationAction</label>
                        <select class="form-control" id="notificationAction1">
                            <option value="alert">Alert</option>
                            <option value="refresh">Refresh</option>
                            <option value="logout">Logout</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Message</label>
                        <textarea class="form-control" id="message1" style="height:300px;"></textarea>
                    </div>
                    <button type="button" class="btn btn-default btn1">Submit</button>
                </form>
            </div>
            <div class="tab-pane" id="page2">
                <form>
                    <div class="form-group">
                        <label>NotificationType</label>
                        <select class="form-control" id="notificationType2">
                            <option value="USER">User</option>
                            <option value="SYSTEM">System</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>NotificationAction</label>
                        <select class="form-control" id="notificationAction2">
                            <option value="alert">Alert</option>
                            <option value="refresh">Refresh</option>
                            <option value="logout">Logout</option>
                        </select>
                    </div>
                    <div class="form-group">

                        <label>Message</label>
                        <textarea class="form-control" id="message2" style="height:300px;"></textarea>
                    </div>
                    <button type="button" class="btn btn-default btn2">Submit</button>
                </form>
            </div>
            <div class="tab-pane" id="page3">
                <form>
                    <div class="form-group">
                        <label>Role</label>
                        <select class="form-control" id="role">
                            <option value="AGENCY_OWNER">Agency Owner</option>
                            <option value="AGENCY_ADMIN">Agency Admin</option>
                            <option value="SITE_ADMIN">Site Admin</option>
                            <option value="COLLABORATOR">Teacher</option>
                            <option value="PARENT">Parent</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>NotificationType</label>
                        <select class="form-control" id="notificationType3">
                            <option value="USER">User</option>
                            <option value="SYSTEM">System</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>NotificationAction</label>
                        <select class="form-control" id="notificationAction3">
                            <option value="alert">Alert</option>
                            <option value="refresh">Refresh</option>
                            <option value="logout">Logout</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Message</label>
                        <textarea class="form-control" id="message3" style="height:300px;"></textarea>
                    </div>
                    <button type="button" class="btn btn-default btn3">Submit</button>
                </form>
            </div>
            <div class="tab-pane" id="page4">
                <form>
                    <div class="form-group">
                        <div class="form-group">
                            <label>UID</label>
                            <input type="text" class="form-control" id="uid" placeholder="UID">
                        </div>
                    </div>
                    <div class="form-group">
                        <label>NotificationType</label>
                        <select class="form-control" id="notificationType4">
                            <option value="USER">User</option>
                            <option value="SYSTEM">System</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>NotificationAction</label>
                        <select class="form-control" id="notificationAction4">
                            <option value="alert">Alert</option>
                            <option value="refresh">Refresh</option>
                            <option value="logout">Logout</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Message</label>
                        <textarea class="form-control" id="message4" style="height:300px;"></textarea>
                    </div>
                    <button type="button" class="btn btn-default btn4">Submit</button>
                </form>
            </div>
        </div>

    </div>
</div>
</body>
<script type="text/javascript">

    $('#myTabs a').click(function (e) {
        e.preventDefault();
        $(this).tab('show')
    });
    $(".btn1").click(function () {
        if (confirm("Do you want to send the message！")) {
            var email = $("#email1").val().trim("");
            var message = $("#message1").val().trim("");
            var messageType = $("#notificationType1").val().trim(" ");
            var attentionType = $("#notificationAction1").val().trim(" ");
            if (email != "" && message != "") {
                var url = "http://lg-api-service-prod-test.us-west-1.elasticbeanstalk.com/api/v1/notification/pushNotification/email"
                var entry = {email: email, message: message,messageType:messageType,attentionType:attentionType};
                postData(url, entry, "POST")
            } else {
                alert("input error");
            }
        }
    });
    $(".btn2").click(function () {
        if (confirm("Do you want to send the message！")) {
            var message = $("#message2").val().trim("");
            var messageType = $("#notificationType2").val().trim(" ");
            var attentionType = $("#notificationAction2").val().trim(" ");
            if (message != "") {
                var url = "http://lg-api-service-prod-test.us-west-1.elasticbeanstalk.com/api/v1/notification/pushNotification/all"
                var entry = {message: message,messageType:messageType,attentionType:attentionType};
                postData(url, entry, "POST")
            } else {
                alert("input error");
            }
        }
    });
    $(".btn3").click(function () {
        if (confirm("Do you want to send the message！")) {
            var role = $("#role").val().trim(" ");
            var messageType = $("#notificationType3").val().trim(" ");
            var attentionType = $("#notificationAction3").val().trim(" ");
            var message = $("#message3").val().trim("");
            if (role != "" && message != "") {
                var url = "http://lg-api-service-prod-test.us-west-1.elasticbeanstalk.com/api/v1/notification/pushNotification/role"
                var entry = {message: message, role: role.toLocaleUpperCase(),messageType:messageType,attentionType:attentionType};
                postData(url, entry, "POST")
            } else {
                alert("input error");
            }
        }
    });
    $(".btn4").click(function () {
        if (confirm("Do you want to send the message！")) {
            var uid = $("#uid").val().trim(" ");
            var messageType = $("#notificationType4").val().trim(" ");
            var attentionType = $("#notificationAction4").val().trim(" ");
            var message = $("#message4").val().trim("");
            if (uid != "" && message != "") {
                var url = "http://lg-api-service-prod-test.us-west-1.elasticbeanstalk.com/api/v1/notification/pushNotification/user"
                var entry = {userId:uid.toUpperCase(),message: message,messageType:messageType,attentionType:attentionType};
                postData(url, entry, "POST")
            } else {
                alert("input error");
            }
        }
    });
    function postData(url, datas, method) {
        $(".btn").addClass("disabled");
        $(".btn").text("waiting...");
        $.ajax({
            url: url,
            type: method,
            data: datas,
            success: function (data) {
                $(".btn").text("success");
                $(".btn").removeClass("btn-default");
                $(".btn").addClass("btn-success");
                setTimeout(function () {
                    $(".btn").text("Submit");
                    $(".btn").addClass("btn-default");
                    $(".btn").removeClass("btn-success");
                    $(".btn").removeClass("disabled");
                }, 2000);
            },
            error: function (data) {
                alert("error");
                $(".btn").text("Submit");
                $(".btn").removeClass("disabled");
            }
        });
    }
</script>
</html>